{"name": "newzealand", "type": "module", "version": "0.1.0", "private": true, "scripts": {"dev": "uni", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "svgicon": "node ./src/uni_modules/zui-svg-icon/tools/generate-svg-icon.js", "build": "uni build", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4070620250821001", "@dcloudio/uni-app-harmony": "3.0.0-4070620250821001", "@dcloudio/uni-app-plus": "3.0.0-4070620250821001", "@dcloudio/uni-components": "3.0.0-4070620250821001", "@dcloudio/uni-h5": "3.0.0-4070620250821001", "@dcloudio/uni-mp-alipay": "3.0.0-4070620250821001", "@dcloudio/uni-mp-baidu": "3.0.0-4070620250821001", "@dcloudio/uni-mp-harmony": "3.0.0-4070620250821001", "@dcloudio/uni-mp-jd": "3.0.0-4070620250821001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4070620250821001", "@dcloudio/uni-mp-lark": "3.0.0-4070620250821001", "@dcloudio/uni-mp-qq": "3.0.0-4070620250821001", "@dcloudio/uni-mp-toutiao": "3.0.0-4070620250821001", "@dcloudio/uni-mp-weixin": "3.0.0-4070620250821001", "@dcloudio/uni-mp-xhs": "3.0.0-4070620250821001", "@dcloudio/uni-quickapp-webview": "3.0.0-4070620250821001", "@intlify/shared": "11.1.7", "@uni-helper/uni-network": "^0.21.5", "@uni-helper/uni-use": "^0.19.14", "@vueuse/core": "9.13.0", "pinia": "2.2.4", "svgo": "^4.0.0", "vue": "3.4.21", "vue-i18n": "11.1.7", "wot-design-uni": "^1.11.1"}, "devDependencies": {"@dcloudio/types": "^3.4.19", "@dcloudio/uni-automator": "3.0.0-4070620250821001", "@dcloudio/uni-cli-shared": "3.0.0-4070620250821001", "@dcloudio/uni-stacktracey": "3.0.0-4070620250821001", "@dcloudio/vite-plugin-uni": "3.0.0-4070620250821001", "@mini-types/alipay": "^3.0.14", "@types/node": "^24.1.0", "@uni-helper/eslint-config": "^0.4.0", "@uni-helper/plugin-uni": "0.1.0", "@uni-helper/uni-types": "^1.0.0-alpha.6", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-platform": "^0.0.5", "@uni-helper/vite-plugin-uni-platform-modifier": "^0.0.2", "@vue/runtime-core": "3.4.21", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "miniprogram-api-typings": "^4.1.0", "sass": "1.64.2", "typescript": "^5.9.2", "vite": "5.2.8", "vue-tsc": "^3.0.5"}}