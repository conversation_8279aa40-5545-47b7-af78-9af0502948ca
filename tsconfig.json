{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "DOM.Iterable", "ESNext"], "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "@dcloudio/types", "@mini-types/alipay", "miniprogram-api-typings", "wot-design-uni/global.d.ts", "@uni-helper/uni-types"], "strict": true, "noImplicitThis": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "include": ["**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue"], "vueCompilerOptions": {"plugins": ["@uni-helper/uni-types/volar-plugin"]}}