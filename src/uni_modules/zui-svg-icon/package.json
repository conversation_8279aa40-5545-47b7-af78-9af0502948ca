{"id": "zui-svg-icon", "displayName": "多彩SVG图标，自由换色，全端适配", "version": "0.1.61", "description": "一款适用于 uni-app 的 SVG 图标组件。全端适配：APP-Plus、小程序、H5。支持多色SVG图标；支持换色；支持URI和Base64格式；支持spin动画；SVG优化", "keywords": ["svg", "svg-icon", "svg图标", "彩色图标", "换色"], "repository": "https://github.com/zivyuan/zui-svg-icon.git", "engines": {"HBuilderX": "^3.4.15"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "u", "字节跳动": "u", "QQ": "y", "钉钉": "y", "快手": "u", "飞书": "y", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}