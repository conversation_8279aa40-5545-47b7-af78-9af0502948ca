.header {
display: flex;
align-items: center;
flex-direction: row;

    &__icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 20rpx;
    }

    &__title {
        color: #000;
        margin-right: 12rpx;
    }

    &__operate {
        color: #103a62;
        text-decoration-line: underline;
    }
}
.guide__wrapper {

        padding: 0 32rpx;

        &__swiper {
            padding: 24rpx 0 32rpx 0;
        }

        &__space {
            width: 100%;
            height: 3px;
            background-color: #FAFAFA;
            margin: 32rpx 0;
        }

        &__record {
            &__title {
                margin-bottom: 32rpx;
                color: #000;
            }

            &__nav {
                position: relative;

                &__line{
                    position: absolute;
                    width: 100%;
                    height: 2px;
                    background-color: #EDF1F6;
                    bottom: 0;
                    left: 0;
                    z-index: 99;
                    border-radius: 1px;
                }
                :deep(.wd-tabs__nav-container){
                    
                    .wd-tabs__line{
                        background-color: #103a62;
                        bottom: 0;
                        height: 2px;
                        z-index: 102;
                    }
                    .wd-tabs__nav-item{
                        color: #ACBECF;
                        font-weight: 500;
                        font-size: 32rpx;
                    }
                }
            }

            &__list {
                margin-top: 30rpx;
                padding-bottom: 32rpx;
            }
        }

    }

