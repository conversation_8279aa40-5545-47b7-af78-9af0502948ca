.guide_item {
    display: flex;
    align-items: center;
    flex-direction: row;
    padding: 24rpx;
    background-color: #FAFAFA;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    position: relative;
  
    &:last-child {
      margin-bottom: 0;
    }
  
    &_img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }
  
    &_content {
      flex: 1;
      display: flex;
      align-items: center;
      flex-direction: row;
      justify-content: space-between;
  
      &_left {
  
        &_title {
          display: flex;
          align-items: center;
          flex-direction: row;
  
          &_text {
            max-width: 180rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
  
          &_tag {
            margin-left: 18rpx;
            background-color: #D5D7DA;
            padding: 10rpx 20rpx;
            border-radius: 10rpx;
            font-family: Alimama FangYuanTi VF !important;
            font-size: 20rpx !important;
            font-weight: 400 !important;
  
            &.blue{
              background-color: #EDF1F6;
              color: #103A62;
            }
  
            &.red{
              background-color: #FEE4E2;
              color: #B42318;
            }
          }
        }
  
        &_info {
          display: flex;
          align-items: center;
          flex-direction: row;
          margin-top: 18rpx;
  
          &_item {
            display: flex;
            align-items: center;
            flex-direction: row;
  
            &_text {
              color: #717680;
              margin-left: 10rpx;
              line-height: 24rpx;
            }
          }
        }
      }
  
      &_right {
        display: flex;
        align-items: center;
        flex-direction: row;
        align-items: center;
  
        &_price {
          font-family: Alimama FangYuanTi VF !important;
          font-size: 28rpx !important;
          font-weight: 400 !important;
          color: #103A62;
        }
  
        &_icon {
          margin-left: 30rpx;
        }
      }
    }
  
  }