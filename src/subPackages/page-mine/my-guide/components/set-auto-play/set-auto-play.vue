<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const isAutoPlay = ref<boolean>(false)
</script>

<template>
  <view class="guide__wrapper__play">
    <view class="guide__wrapper__play__left">
      <view class="guide__wrapper__play__left__title fs--14--700">
        {{ t('mine.myguide.autoPlay') }}
      </view>
      <view class="guide__wrapper__play__left__desc fs--12--400">
        {{ t('mine.myguide.autoPlayTips') }}
      </view>
    </view>
    <view class="guide__wrapper__play__right">
      <wd-switch
        v-model="isAutoPlay"
        active-color="#103A62"
        inactive-color="#D5D7DA"
        size="16px"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.guide__wrapper__play {
    padding: 24rpx;
    background-color: #FAFAFA;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;

    &__left {
        flex: 1;

        &__title {
            color: #000;
        }

        &__desc {
            color: #717680;
            margin-top: 12rpx;
        }
    }

    &__right {
        margin-left: 20rpx;
    }
}
</style>
