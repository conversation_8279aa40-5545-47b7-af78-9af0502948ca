<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import type { GuideOrderItemType } from '@/model/guide'
import { onLoad } from '@dcloudio/uni-app'
import { nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiBannerList } from '@/api/common'
import { ApiGuideOrder } from '@/api/guide'
import { useList } from '@/hooks/useList'
// import guideItem from '@/pages/page-guide/components/guide-item/guide-item.vue'
import { goBack, gotoPage } from '@/utils/router'
import orderItem from './components/order-item/order-item.vue'
import setAutoPlay from './components/set-auto-play/set-auto-play.vue'

const swiperList = ref<BaseSwiperItemType[]>([])
const { t } = useI18n()
const tabs = ref<any[]>([
  { label: 'Can be used', value: '1' },
  { label: 'Expired', value: '2' },
])
const activeTab = ref<string>('1')

async function getSwiperList() {
  const res = await ApiBannerList({ show_type: 2 })
  swiperList.value = res.lists
}

const { list, refresh, setParams } = useList<GuideOrderItemType>(ApiGuideOrder, { expire_type: activeTab.value })

function handleChange() {
  nextTick(() => {
    setParams({ expire_type: activeTab.value })
  })
}

onLoad(() => {
  getSwiperList()
  refresh()
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="header">
        <view class="header__icon" @tap="goBack">
          <zui-svg-icon width="48rpx" height="48rpx" icon="arrow-left2-light" />
        </view>
        <text class="header__title fs--16--700">
          {{ t('mine.myguide.title') }}
        </text>
        <text class="header__operate fs--14--400" @tap="gotoPage('/subPackages/page-mine/guide-use/guide-use')">
          {{ t('mine.myguide.use') }}
        </text>
      </view>
    </nav-bar-page>

    <view class="body">
      <view class="guide__wrapper">
        <view class="guide__wrapper__swiper">
          <base-swiper :list="swiperList" border-radius="24rpx" height="376rpx" />
        </view>
        <!-- 自动播放设置 -->
        <set-auto-play />

        <view class="guide__wrapper__space" />

        <view class="guide__wrapper__record">
          <view class="guide__wrapper__record__title fs--16--400">
            {{ t('mine.myguide.purchaseRecord') }}
          </view>

          <view class="guide__wrapper__record__nav">
            <view class="guide__wrapper__record__nav__line" />
            <wd-tabs
              v-model="activeTab"
              auto-line-width
              inactive-color="#ACBECF"
              color="#103A62"
              @change="handleChange"
            >
              <block v-for="item in tabs" :key="item.value">
                <wd-tab :title="`${item.label}`" :name="item.value" />
              </block>
            </wd-tabs>
          </view>

          <view class="guide__wrapper__record__list">
            <template v-if="list.length && list.length > 0">
              <order-item
                v-for="(item) in list"
                :key="item.id"
                :item="item"
              />
            </template>
            <empty v-else :description="t('mine.myguide.notContent')" height="450rpx" />
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-guide.scss'
</style>
