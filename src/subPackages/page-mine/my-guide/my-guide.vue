<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import { ref } from 'vue'
import guideItem from '@/pages/page-guide/components/guide-item/guide-item.vue'
import { goBack, gotoPage } from '@/utils/router'
import setAutoPlaya from './components/set-auto-playa.vue'

const swiperList = ref<BaseSwiperItemType[]>([
  { id: 1, url: '/static/test/guide-swiper.png' },
  { id: 2, url: '/static/test/guide-swiper.png' },
])

const tabs = ref<any[]>([
  { label: 'Can be used', value: 0 },
  { label: 'Expired', value: 1 },
])
const activeTab = ref<number>(0)

function handleChange() {
}
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="header">
        <view class="header__icon" @tap="goBack">
          <zui-svg-icon width="48rpx" height="48rpx" icon="arrow-left2-light" />
        </view>
        <text class="header__title fs--16--700">
          E-Guide
        </text>
        <text class="header__operate fs--14--400" @tap="gotoPage('/subPackages/page-mine/guide-use/guide-use')">
          How to use
        </text>
      </view>
    </nav-bar-page>

    <view class="body">
      <view class="guide__wrapper">
        <view class="guide__wrapper__swiper">
          <base-swiper :list="swiperList" border-radius="24rpx" height="376rpx" />
        </view>
        <!-- 自动播放设置 -->
        <set-auto-playa />

        <view class="guide__wrapper__space" />

        <view class="guide__wrapper__record">
          <view class="guide__wrapper__record__title fs--16--400">
            E-Guide purchase record
          </view>

          <view class="guide__wrapper__record__nav">
            <view class="guide__wrapper__record__nav__line" />
            <wd-tabs
              v-model="activeTab"
              auto-line-width
              inactive-color="#ACBECF"
              color="#103A62"
              @change="handleChange"
            >
              <block v-for="item in tabs" :key="item.value">
                <wd-tab :title="`${item.label}`" :name="item.value" />
              </block>
            </wd-tabs>
          </view>

          <view class="guide__wrapper__record__list">
            <guide-item v-for="(item, index) in 10" :key="index" />
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-guide.scss'
</style>
