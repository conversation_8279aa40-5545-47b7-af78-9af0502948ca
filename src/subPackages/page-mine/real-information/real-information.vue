<script setup lang="ts">
import { ref } from 'vue'
import { gotoPage } from '@/utils/router'

const show = ref<boolean>(false)

const formData = ref<any>({
  name: '',
  telePhone: '',
  passportNumber: '',
  flightNumber: '',
  departureTime: 0,
})

function openConfirm() {
  show.value = true
}

function submit() {
  gotoPage(`/subPackages/page-mine/coupon-package/coupon-package`)
}
</script>

<template>
  <page>
    <nav-bar-page title="Fill in real information" />
    <view class="body">
      <view class="tips">
        <text class="tips__text">
          Since the store where you redeemed the coupon is an outbound store, to facilitate your tax refund, we
          need to collect your real information in advance. Please verify and fill it in!
        </text>
      </view>
      <view class="form">
        <wd-form :model="formData">
          <wd-cell-group>
            <wd-input
              v-model="formData.name"
              label="Name"
              prop="name"
              placeholder="Please enter your name"
              label-width="120rpx"
              :rules="[{ required: true, message: '请填写用户名' }]"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>
            <wd-input
              v-model="formData.telePhone"
              label="Telephone"
              prop="telePhone"
              label-width="140rpx"
              placeholder="Please enter your telephone"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>
            <wd-input
              v-model="formData.passportNumber"
              label="Passport number"
              prop="passportNumber"
              placeholder="Please enter your passport number"
              label-width="250rpx"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>
            <wd-input
              v-model="formData.flightNumber"
              label="Flight number"
              prop="flightNumber"
              placeholder="Please enter your flight number"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>
            <wd-calendar
              v-model="formData.departureTime"
              label="Departure time"
              placeholder="Please select time"
              prop="departureTime"
              :z-index="1002"
            />
          </wd-cell-group>
        </wd-form>
      </view>

      <fixed-bottom-bar>
        <view class="bottom_bar" @tap="openConfirm">
          <view class="bottom_bar__btn">
            Confirm the conversation
          </view>
        </view>
      </fixed-bottom-bar>
    </view>
    <confirm-popup
      v-model="show"
      height="380rpx"
      title="Confirmation of Exchange Notice"
      content="Redemption successful. Please go to Mine - My Points - Coupon Package list to check"
      confirm-text="Coupon package"
      cancel-text="Return"
      @confirm="submit"
    />
  </page>
</template>

<style scoped lang="scss">
.tips {
    padding: 12rpx 32rpx;

    &__text {
        color: #535862;
        font-size: 28rpx;
        font-weight: 400;
        font-family: Alimama FangYuanTi VF !important;
    }
}

.form{
    padding: 0 32rpx;
    margin-top: 32rpx;
    :deep(.wd-input){
      border-radius: 24rpx;
      border-bottom: 1rpx solid #EAECF0;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
      text-align: right;

      .right{
        margin-left: 18rpx;
      }
    }

    :deep(.wd-calendar){
      border-radius: 24rpx;
      border-bottom: 1rpx solid #EAECF0;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);

      .wd-cell__value{
         text-align: right;

         .wd-calendar__arrow{
          color: #535862;
         }
      }
    }
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}
</style>
