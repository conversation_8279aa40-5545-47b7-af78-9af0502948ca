<script setup lang="ts">
import { gotoPage } from '@/utils/router'
import popupLang from './components/popup-lang.vue'
</script>

<template>
  <page>
    <nav-bar-page title="Setting" />
    <view class="body">
      <view class="list">
        <view class="list__item">
          <text class="list__item__title">
            Personal Information
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/account-security/account-security')">
          <text class="list__item__title">
            Account security
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/feedback/feedback')">
          <text class="list__item__title">
            Feedback
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/franchise-cooperation/franchise-cooperation')">
          <text class="list__item__title">
            Franchise cooperation
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <popupLang />
        <!-- <view class="list__item">
          <text class="list__item__title">
            Language switching
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view> -->
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/about-us/about-us')">
          <text class="list__item__title">
            About Us
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/cancel-account/cancel-account')">
          <text class="list__item__title">
            Cancel account
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="gotoPage('/subPackages/page-mine/my-inviter/my-inviter')">
          <text class="list__item__title">
            My inviter
          </text>
          <view class="list__item__right">
            <text class="list__item__right__text">
              Go binding
            </text>
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view
          class="list__item"
          @click="gotoPage(`/subPackages/page-home/notice/index?type=generalprotocol&name=user_agreement`)"
        >
          <text class="list__item__title">
            User Agreement
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view
          class="list__item"
          @click="gotoPage(`/subPackages/page-home/notice/index?type=generalprotocol&name=privacy_policy`)"
        >
          <text class="list__item__title">
            Privacy Policy
          </text>
          <view class="list__item__right">
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang='scss'>
@import './setting.scss'
</style>
