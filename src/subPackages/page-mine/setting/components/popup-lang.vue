<script lang="ts" setup>
import { ref, watch } from 'vue'

const selectPickerRef = ref()
const visible = defineModel('visible', {
  default: false,
})

const Language = ref<string>('en')

// watch(() => visible.value, (show: boolean) => {
//   Language.value = uni.getStorageSync('lang')
//   console.log(Language.value, 'Language-----')
//   show && selectPickerRef.value?.open()
// })

const columns = ref<Record<string, any>>([
  {
    value: 'zh-Hans',
    label: '简体中文',
  },
  {
    value: 'zh-Hant',
    label: '繁體中文',
  },
  {
    value: 'de',
    label: 'Deutsch',
  },
  {
    value: 'en',
    label: 'English',
  },
  {
    value: 'fr',
    label: 'Français',
  },
  {
    value: 'es',
    label: 'Español',
  },
])

function handleOpen() {
  Language.value = uni.getStorageSync('lang')
}

function handleConfirm({ value, selectedItems }) {
  uni.setStorageSync('lang', value)
  uni.setLocale(value)
  uni.reLaunch({ url: '/pages/page-home/index' })
//   customShow.value = selectedItems
//     .map((item) => {
//       return item.label
//     })
//     .join(', ')
}
</script>

<template>
  <wd-select-picker v-model="Language" type="radio" :close-on-click-modal="false" use-default-slot confirm-button-text="选择" :columns="columns" @open="handleOpen" @confirm="handleConfirm">
    <view class="list__item">
      <text class="list__item__title">
        Language switching
      </text>
      <view class="list__item__right">
        <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
      </view>
    </view>
  </wd-select-picker>
</template>

<style lang="scss" scoped>
@use '../setting.scss'
</style>
