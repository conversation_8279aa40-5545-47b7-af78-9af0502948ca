<script setup lang="ts">
import type { NoticeInfo } from '@/model/common'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetNoticeInfo } from '@/api/common'

const info = ref<NoticeInfo>({} as NoticeInfo)

async function getInfo() {
  const res = await ApiGetNoticeInfo({
    type: 'generalprotocol',
    name: 'franchise_cooperation',
  })
  info.value = res
}

function callPhone() {
  uni.makePhoneCall({
    phoneNumber: '10086', // 仅为示例
  })
}

onLoad(() => {
  getInfo()
})
</script>

<template>
  <page>
    <nav-bar-page title="Franchise cooperation" />
    <view class="body">
      <view class="content">
        <mp-html :content="info.content" />
      </view>
      <fixed-bottom-bar>
        <view class="bottom_bar" @click="callPhone">
          <view class="bottom_bar__btn">
            Contact us
          </view>
        </view>
      </fixed-bottom-bar>
    </view>
  </page>
</template>

<style scoped lang="scss">
.content{
    padding: 0 32rpx;
}
.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}
</style>
