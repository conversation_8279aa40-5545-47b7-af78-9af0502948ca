<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiEditInfo } from '@/api/user'
import { useUploadImg } from '@/hooks/useUploadImg'
import { useUserStore } from '@/stores/user'
import { goBack } from '@/utils/router'

const { t } = useI18n()
const userStore = useUserStore()
const show = ref<boolean>(false)
const model = ref({
  avatar: '',
  nickname: '',
  birthday: 0,
  country: '',
  sex: 1,
})

const avatar = ref('')
const time = ref<number>(0)

const { upload } = useUploadImg()

const sexList = ref<Record<string, any>>([
  { label: t('mine.editInfo.male'), value: 1 },
  { label: t('mine.editInfo.woman'), value: 2 },
])

function handleTimeConfirm(e: any) {
  model.value.birthday = e.value / 1000
}

function handleConfirmUpload(e: any) {
  const { tempFilePath } = e
  upload({
    tempFilePath,
    success: (url) => {
      model.value.avatar = url
    },
  })
  console.warn(e)
}

function selectImage() {
  uni.chooseImage({
    count: 1,
    success: (res: any) => {
      avatar.value = res.tempFilePaths[0]
      show.value = true
    },
  })
}

const formRef = ref<any>(null)
function submit() {
  formRef.value.validate().then(async ({ valid }) => {
    if (valid) {
      await ApiEditInfo(model.value)
      userStore.getUserInfo()
      uni.showToast({
        title: t('mine.editInfo.success'),
        icon: 'none',
      })
      setTimeout(() => {
        goBack()
      }, 500)
    }
  }).catch((error: any) => {
    console.error(error)
  })
}

onLoad(async () => {
  await userStore?.getUserInfo()
  model.value = {
    avatar: userStore.userInfo?.avatar || '',
    nickname: userStore.userInfo?.nickname || '',
    birthday: userStore.userInfo?.birthday || 0,
    country: userStore.userInfo?.country || '',
    sex: userStore.userInfo?.sex || 0,
  }
  time.value = model.value.birthday ? model.value.birthday * 1000 : 0
})
</script>

<template>
  <page>
    <nav-bar-page title="Edit materials" />
    <view class="body">
      <view class="form">
        <view class="form__avatar">
          <view class="form__avatar__main" @tap="selectImage">
            <image v-if="!model.avatar" src="/static/mine/avatar.png" class="form__avatar__main__default" mode="aspectFit" />
            <image v-else :src="model.avatar" class="form__avatar__main__default" mode="aspectFit" />
            <view v-if="model.avatar" class="form__avatar__main__inner">
              <zui-svg-icon icon="avatar-image" width="40rpx" height="40rpx" color="#fff" />
            </view>
          </view>
        </view>
        <wd-form ref="formRef" :model="model" error-type="toast">
          <wd-cell-group>
            <!-- 名称 -->
            <wd-input
              v-model="model.nickname"
              :label="$t('mine.editInfo.nickname_label')"
              prop="nickname"
              :placeholder="$t('mine.editInfo.nickname_placeholder')"
              label-width="120rpx"
              :rules="[{ required: true, message: `${$t('mine.editInfo.nickname_placeholder')}` }]"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>
            <!-- 生日 -->
            <wd-calendar
              v-model="time"
              :label="$t('mine.editInfo.birthday_label')"
              :placeholder="$t('mine.editInfo.birthday_placeholder')"
              prop="birthday"
              :z-index="1002"
              @confirm="handleTimeConfirm"
            />
            <!-- 邀请码 -->
            <view class="form__item">
              <view class="form__item__label">
                {{ $t('mine.editInfo.inviteCode_label') }}
              </view>
              <view class="form__item__value">
                445247
              </view>
            </view>

            <wd-select-picker
              v-model="model.sex"
              :z-index="1002"
              label="Gender"
              :columns="sexList"
              type="radio"
              placeholder="Please select Gender"
            />

            <!-- 年龄 -->
            <view class="form__item">
              <view class="form__item__label">
                {{ $t('mine.editInfo.age_label') }}
              </view>
              <view class="form__item__value">
                {{ userStore.userInfo?.age }}
              </view>
            </view>

            <wd-input
              v-model="model.country"
              :label="$t('mine.editInfo.country_label')"
              prop="country"
              :placeholder="$t('mine.editInfo.country_placeholder')"
              label-width="180rpx"
            >
              <template #suffix>
                <view class="right">
                  <zui-svg-icon icon="edit-light" width="32rpx" height="32rpx" color="#4B5353" />
                </view>
              </template>
            </wd-input>

            <view class="form__item">
              <view class="form__item__label">
                {{ $t('mine.editInfo.user_identity') }}
              </view>
              <view class="form__item__value">
                {{ userStore.userInfo?.account_type }}
              </view>
            </view>
            <view class="form__item">
              <view class="form__item__label">
                {{ $t('mine.editInfo.log_account') }}
              </view>
              <view class="form__item__value">
                {{ userStore.userInfo?.account }}
              </view>
            </view>
          </wd-cell-group>
        </wd-form>
      </view>
    </view>

    <fixed-bottom-bar v-if="!show">
      <view class="bottom_bar" @click="submit">
        <view class="bottom_bar__btn">
          {{ $t('mine.editInfo.submit') }}
        </view>
      </view>
    </fixed-bottom-bar>

    <wd-img-cropper
      v-model="show"
      :img-src="avatar"
      @confirm="handleConfirmUpload"
    />
  </page>
</template>

<style scoped lang="scss">
@import './edit-info.scss'
</style>
