.form{
  padding: 24rpx 32rpx;

  &__avatar{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 64rpx;

    &__main{
      width: 160rpx;
      height: 160rpx;
      position: relative;
      border-radius: 160rpx;
      overflow: hidden;

      &__inner{
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
        position: absolute;
        left: 0;
        top: 0;
      }

      &__default{
        width: 160rpx;
        height: 160rpx;
      }
    }
  }

  &__item{
    border-radius: 24rpx;
    border-bottom: 1rpx solid #EAECF0;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
    padding: 30rpx 24rpx;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__label{
      color: #717680;
      font-size: 24rpx;
      font-weight: 500;
    }

    &__value{
      color: #000;
      font-size: 24rpx;
      font-weight: 500;
    }
  }
  
  :deep(.wd-input){
      border-radius: 24rpx;
      border-bottom: 1rpx solid #EAECF0;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
      text-align: right;
      color: #000;
      font-size: 24rpx !important;
      font-weight: 500;

      input{
        font-size: 24rpx !important;
      }

      .right{
        margin-left: 18rpx;
      }

      .wd-input__label-inner{
        color: #717680;
        font-size: 24rpx;
        font-weight: 500;
      }

      .wd-input__placeholder{
        color: #000;
        font-size: 24rpx;
        font-weight: 500;
      }
    }

  :deep(.wd-calendar){
      border-radius: 24rpx !important;
      border-bottom: 1rpx solid #EAECF0;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .wd-calendar__arrow{
        color: #414651 !important;
      }

      .wd-cell__value{
         text-align: right;
        color: #000;
        font-size: 24rpx;
        font-weight: 500;
      }

      .wd-cell__title{
        color: #717680;
        font-size: 24rpx;
        font-weight: 500;
      }
  }

  :deep(.wd-select-picker){
     border-radius: 24rpx !important;
      border-bottom: 1rpx solid #EAECF0;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .wd-cell__title{
        color: #717680;
        font-size: 24rpx;
        font-weight: 500;
      }

       .wd-cell__value{
         text-align: right;
         color: #000;
        font-size: 24rpx;
        font-weight: 500;
      }

      .wd-cell__title{
        color: #717680;
        font-size: 24rpx;
        font-weight: 500;
      }
  }
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}