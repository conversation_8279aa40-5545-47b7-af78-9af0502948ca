<script setup lang="ts">
import type { NoticeInfo } from '@/model/common'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetNoticeInfo } from '@/api/common'
import { useConfigStore } from '@/stores/config'

const { config } = useConfigStore()
const info = ref<NoticeInfo>({} as NoticeInfo)

async function getInfo() {
  const res = await ApiGetNoticeInfo({
    type: 'aboutus',
  })
  info.value = res
}

onLoad(() => {
  getInfo()
})
</script>

<template>
  <page>
    <nav-bar-page title="About Us" />
    <view class="wrapper">
      <view class="wrapper__logo">
        <image :src="config?.website.logo" class="wrapper__logo__img" mode="aspectFit" />
      </view>
      <view class="wrapper__title">
        New Zealand free trave
      </view>
      <view>
        <mp-html :content="info.content" style="float: left; width: 100%; height: auto;" />
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './about-us.scss'
</style>
