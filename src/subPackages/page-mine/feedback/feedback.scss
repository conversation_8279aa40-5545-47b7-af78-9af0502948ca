.wrapper{
	padding: 32rpx 32rpx 0 32rpx;
	
	&__from{
		
		&__top{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			border-radius: 24rpx;
			background-color: #FFF;
			border-bottom: #EAECF0 solid 1rpx;
			box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
			margin-bottom: 20rpx;
			
			&__text{
				font-size: 24rpx;
				font-weight: 400;
				font-family: Alima<PERSON> FangYuanTi VF;
				color: #1D232E;
			}
			
			&__right{
				display: flex;
				flex-direction: row;
				align-items: center;
				
				&__text{
					color: #717680;
					font-size: 24rpx;
					font-weight: 400;
					font-family: Alimama FangYuanTi VF;
					margin-right: 12rpx;
				}
			}
		}
	
		
		&__bottom{
			padding: 24rpx;
			background-color: #FAFAFA;
			border-radius: 24rpx;
			margin-bottom: 20rpx;
			
			&__tips{
				color: #717680;
				font-size: 24rpx;
				font-weight: 400;
				font-family: <PERSON><PERSON><PERSON>uanTi VF;
				margin-right: 12rpx;
				margin-bottom: 32rpx;
			}
		}
		
		&__tips{
			&__text{
				color: #717680;
				font-size: 24rpx;
				font-weight: 400;
				font-family: <PERSON><PERSON><PERSON>YuanTi VF;
				margin-right: 12rpx;
				margin-bottom: 32rpx;
			}
		}
	}
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }

    &__tips{
        color: #717680;
        font-size: 24rpx;
        font-weight: 500;
        margin-top: 20rpx;
        font-family: Alimama FangYuanTi VF;
    }
}