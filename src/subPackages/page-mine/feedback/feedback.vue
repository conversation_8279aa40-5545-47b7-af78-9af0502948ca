<script setup lang="ts">
</script>

<template>
  <page>
    <nav-bar-page title="Feedback" />
    <view class="body">
      <view class="wrapper">
        <view class="wrapper__from">
          <view class="wrapper__from__top">
            <text class="wrapper__from__top__text">
              Question type
            </text>
            <view class="wrapper__from__top__right">
              <text class="wrapper__from__top__right__text">
                Select the type of question
              </text>
              <zui-svg-icon icon="arrow-down2-light" width="32rpx" height="32rpx" />
            </view>
          </view>

          <view class="wrapper__from__bottom">
            <text class="wrapper__from__bottom__tips">
              Please write down your suggestions, such as functional requirements, product teasing, etc. We will strive to improve.
            </text>

            <view class="wrapper__from__bottom__upload">
              上传组件占位
            </view>
          </view>

          <view class="wrapper__from__tips">
            <text class="wrapper__from__tips__text">
              If you have any questions to consult or solve, please contact the online customer service. Thank you!
            </text>
          </view>
        </view>
      </view>
    </view>

    <fixed-bottom-bar>
      <view class="bottom_bar">
        <view class="bottom_bar__btn">
          Submit
        </view>
        <view class="bottom_bar__tips">
          Safety reminder: The new password should not be too similar to the old one.
        </view>
      </view>
    </fixed-bottom-bar>
  </page>
</template>

<style scoped lang='scss'>
@import './feedback.scss'
</style>
