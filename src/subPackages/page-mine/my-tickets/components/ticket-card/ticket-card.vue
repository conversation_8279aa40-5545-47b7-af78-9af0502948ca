<script lang="ts" setup>
</script>

<template>
  <view class="card">
    <ticket-style-card>
      <!-- 车票上方 -->
      <template #top>
        <view class="card__top">
          <view class="card__top__left">
            <text class="card__top__left__date">
              2025/07/04
            </text>
            <text class="card__top__left__tag grey">
              Package
            </text>
            <text class="card__top__left__tag blue">
              To be used
            </text>
          </view>
          <text class="card__top__right">
            NZD 140
          </text>
        </view>
        <view class="card__line">
          <view class="card__line__stop">
            <view class="card__line__stop__name">
              Shenzhen N.
            </view>
            <view class="card__line__stop__date">
              10:00 AM
            </view>
          </view>
          <view class="card__line__center">
            <text class="card__line__center__num fs--14--400">
              D3534
            </text>
            <image class="card__line__center__img" src="/static/mine/location_right.png" />
            <text class="card__line__center__km fs--12--400">
              1h45m
            </text>
          </view>
          <view class="card__line__stop right">
            <view class="card__line__stop__name">
              Cairns
            </view>
            <view class="card__line__stop__date">
              11:45 AM
            </view>
          </view>
        </view>
        <view class="card__desc">
          <text class="card__desc__text left">
            Passenger:
          </text>
          <text class="card__desc__text right">
            Lulise
          </text>
        </view>

        <view class="card__tips">
          <text class="card__tips__text">
            t is expected to arrive at the pick-up point in 1 hour and 27 minutes
          </text>
        </view>
      </template>
      <!-- 车票下方 -->
      <template #bottom>
        <view class="card__bottom">
          <view class="card__bottom__left">
            <zui-svg-icon
              width="32rpx"
              hidden="32rpx"
              color="#5D7D9D"
              icon="location-light"
            />
            <text class="card__bottom__left__text">
              Boarding point
            </text>
          </view>
          <view class="card__bottom__btn">
            <text class="card__bottom__btn__text">
              Verify
            </text>
          </view>
        </view>
      </template>
    </ticket-style-card>
  </view>
</template>

<style scoped lang="scss">
@import './ticket-card.scss'
</style>
