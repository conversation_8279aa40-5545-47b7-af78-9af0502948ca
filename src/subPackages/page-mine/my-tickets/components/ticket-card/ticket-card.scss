.card {
    margin-bottom: 32rpx;

    &__top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-flow: row;
        margin-bottom: 32rpx;

        &__left {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-flow: row;

            &__date {
                font-size: 24rpx;
                color: #101010;
                line-height: 48rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
            }

            &__tag {
                color: #fff;
                padding: 8rpx 12rpx;
                border-radius: 100rpx;
                text-align: center;
                font-size: 20rpx;
                margin-left: 20rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;

                &.blue {
                    background-color: #0BA5EC;
                }

                &.grey {
                    background-color: #EDF1F6;
                    color: #101010;
                }
            }
        }

        &__right {
            color: #103A62;
            font-size: 36rpx;
            line-height: 48rpx;
            font-weight: 700;
            font-family: Alimama FangYuanTi VF;
        }
    }

    &__line {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;

        &__stop {
            width: 34%;

            &__name {
                font-size: 32rpx;
                font-weight: 700;
                line-height: 48rpx;
                color: #101010;
                font-family: <PERSON><PERSON><PERSON>uanTi VF;
            }

            &__date {
                font-size: 24rpx;
                color: #878787;
                line-height: 32rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
            }

            &.right {
                .card__line__stop__name {
                    text-align: right;
                }

                .card__line__stop__date {
                    text-align: right;
                }
            }
        }

        &__center {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &__num {
                color: #000;
                line-height: 32rpx;
            }

            &__img {
                width: 150rpx;
                height: 32rpx;
            }

            &__km {
                line-height: 32rpx;
                color: #103A62;
            }
        }
    }

    &__desc {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin: 32rpx 0;

        &__text {
            font-size: 24rpx;
            font-weight: 400;
            line-height: 32rpx;
            font-family: Alimama FangYuanTi VF;

            &.left {
                color: #878787;
            }

            &.right {
                color: #252B37;
            }
        }
    }

    &__tips {
        &__text {
            color: #ACBECF;
            line-height: 32rpx;
            font-weight: 700;
            font-size: 24rpx;
            font-family: Alimama FangYuanTi VF;
        }
    }

    &__bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;

        &__left {
            display: flex;
            align-items: center;
            flex-direction: row;

            &__text {
                font-size: 24rpx;
                font-weight: 400;
                line-height: 32rpx;
                text-decoration-line: underline;
                font-family: Alimama FangYuanTi VF;
                color: #5D7D9D;
                margin-left: 6rpx;
            }
        }

        &__btn {
            background-color: #103A62;
            border-radius: 100rpx;
            padding: 8rpx 24rpx;
            width: 150rpx;

            &__text {
                color: #fff;
                font-size: 28rpx;
                font-weight: 700;
                line-height: 40rpx;
                font-family: Alimama FangYuanTi VF;
                text-align: center;
            }
        }
    }
}