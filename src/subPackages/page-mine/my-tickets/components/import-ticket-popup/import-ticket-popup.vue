<script lang="ts" setup>
import { ref } from 'vue'

const emits = defineEmits(['confirm'])

const show = defineModel<boolean>('modelValue', { default: false })

const orderId = ref<string>('')

function close() {
  show.value = false
}

function confirm() {
  emits('confirm')
}
</script>

<template>
  <view>
    <base-popup v-model="show" is-footer title="Enter your order ID" height="500rpx">
      <view class="content">
        <view class="content__input">
          <text class="content__input__label">
            Train number
          </text>
          <input
            v-model="orderId" class="content__input__form" placeholder="Order ID"
            placeholder-class="content__input__form__text"
          >
        </view>
      </view>
      <template #footer>
        <view class="content__footer">
          <view class="content__footer__btn left" @tap="close">
            <text class="fs--14--400 content__footer__btn__text">
              Cancel
            </text>
          </view>
          <view class="content__footer__btn right" @tap="confirm">
            <text class="fs--14--400 content__footer__btn__text">
              Confirm
            </text>
          </view>
        </view>
      </template>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './import-ticket-popup.scss'
</style>
