.content {
    padding: 32rpx 0;

    &__input {
        padding: 24rpx;
        border-radius: 24rpx;
        border-bottom: 1rpx #EAECF0 solid;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
        background: #FFF;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-top: 50rpx;

        &__label {
            color: #717680;
            font-size: 24rpx;
            font-weight: 400;
            font-family: Alimama FangYuanTi VF;
            margin-right: 32rpx;
        }

        &__form {
            color: #A4A7AE;
            font-size: 24rpx;
            font-weight: 400;
            font-family: Alimama FangYuanTi VF;
            text-align: right;
            flex: 1;

            &__text {
                font-size: 24rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
            }
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;

        &__btn {
            height: 60rpx;
            width: 48%;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &.left {
                border: 1px solid #103A62;

                .content__footer__btn__text {
                    color: #103A62;
                }
            }

            &.right {
                border: 1px solid transparent;
                background-color: #103A62;

                .content__footer__btn__text {
                    color: #fff;
                }
            }
        }

    }
}