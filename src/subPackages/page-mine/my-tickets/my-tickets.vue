<script setup lang="ts">
import { ref } from 'vue'
import importTicketPopup from './components/import-ticket-popup/import-ticket-popup.vue'
import ticketCard from './components/ticket-card/ticket-card.vue'

const show = ref<boolean>(false)

const tabs = ref<any[]>([
  { label: 'Not used', value: 0 },
  { label: 'Used', value: 1 },
  { label: 'Expired', value: 2 },
])

const activeTab = ref<number>(0)

function handleChange() {}

function openImport() {
  show.value = true
}
</script>

<template>
  <page>
    <nav-bar-page title="My tickets">
      <template #right>
        <text class="nav-text" @tap="openImport">
          Import my ticket
        </text>
      </template>
    </nav-bar-page>

    <view class="body">
      <view class="tabs">
        <view class="tabs__line" />
        <wd-tabs
          v-model="activeTab"
          auto-line-width
          inactive-color="#ACBECF"
          color="#103A62"
          @change="handleChange"
        >
          <block v-for="item in tabs" :key="item.value">
            <wd-tab :title="`${item.label}`" :name="item.value" />
          </block>
        </wd-tabs>
      </view>
      <view class="list">
        <ticket-card v-for="(item, index) in 10" :key="index" />
      </view>
    </view>

    <import-ticket-popup v-model="show" />
  </page>
</template>

<style scoped lang="scss">
.nav-text {
    color: #103A62;
    font-size: 26rpx;
    font-weight: 400;
    font-family: Alimama FangYuanTi VF;
    text-decoration-line: underline;
}

.tabs {
    margin: 0 32rpx;
    position: relative;

    &__line{
        position: absolute;
        width: 100%;
        height: 2px;
        background-color: #EDF1F6;
        bottom: 0;
        left: 0;
        z-index: 99;
        border-radius: 1px;
    }

    :deep(.wd-tabs__nav-container){
        .wd-tabs__line{
            background-color: #103a62;
            bottom: 0;
            height: 2px;
            z-index: 102;
        }
        .wd-tabs__nav-item{
            color: #ACBECF;
            font-weight: 500;
            font-size: 32rpx;
        }
    }
}

.list{
    padding: 32rpx 32rpx 0 32rpx;
}
</style>
