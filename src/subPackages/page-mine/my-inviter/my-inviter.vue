<script setup lang="ts">
import { ref } from 'vue'

const code = ref<string>('')

const show = ref<boolean>(false)

const isBind = ref<boolean>(false)

function confirm() {
  show.value = false
}

function submit() {
  show.value = true
}
</script>

<template>
  <page>
    <nav-bar-page title="My inviter" />
    <view class="body">
      <view class="wrapper">
        <view v-if="isBind" class="wrapper__bind">
          <text class="wrapper__bind__label">
            Binding person ID
          </text>
          <text class="wrapper__bind__code">
            2341534122
          </text>
        </view>
        <view v-else class="wrapper__unbind">
          <input v-model="code" class="wrapper__unbind__input" placeholder="Please enter the inviter's ID" placeholder-class="wrapper__unbind__input__text">
        </view>
      </view>
      <fixed-bottom-bar v-if="!isBind">
        <view class="bottom_bar" @tap="submit">
          <view class="bottom_bar__btn">
            Binding
          </view>
        </view>
      </fixed-bottom-bar>
    </view>

    <confirm-popup
      v-model="show"
      height="400rpx"
      title="Binding notification"
      content="After binding the inviter, it cannot be changed. Do you confirm the binding?"
      cancel-text="Think again"
      confirm-text="OK"
      @confirm="confirm"
    />
  </page>
</template>

<style scoped lang="scss">
@import './my-inviter.scss'
</style>
