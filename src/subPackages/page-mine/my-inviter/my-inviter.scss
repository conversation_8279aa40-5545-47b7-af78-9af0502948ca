.wrapper{
	margin: 0 32rpx;
	
	&__bind{
		padding: 24rpx;
		border-radius: 24rpx;
		box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
		border-bottom: 1rpx solid #EAECF0;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		margin-top: 32rpx;
		
		&__label{
			color: #1D232E;
			font-size: 24rpx;
			font-weight: 400;
			font-family: Alimama FangYuanTi VF;
		}
		
		&__code{
			color:#000;
			font-size: 24rpx;
			font-weight: 400;
			font-family: Alimama FangYuanTi VF;
		}
	}

	&__unbind{
		height: 100%;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding-top: 32rpx;
		&__input{
			height: 98rpx;
			background-color: #FAFAFA;
			padding: 0 24rpx;
			border-radius: 24rpx;
			color: #717680;
			font-size: 24rpx;
			font-weight: 400;
			font-family: Alimama FangYuanTi VF;
			
			&__text{
				color: #717680;
				font-size: 24rpx;
				font-weight: 400;
				font-family: Alima<PERSON> FangYuanTi VF;
			}
		}
	}
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}