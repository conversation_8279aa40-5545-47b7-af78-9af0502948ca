<script lang="ts" setup>
import type { IOrderListType } from '@/model/order'
import { gotoPage } from '@/utils/router'
import CardLine from '../card-line.vue'
import CardTag from '../card-tag.vue'

const props = defineProps<{
  orderData: IOrderListType
  type: IOrderListType['type']
}>()

const emits = defineEmits(['handle-board'])

function handleSecond(time: number) {
  const date = +new Date()
  const closeTime = time - Math.fround(date / 100)
  return Number(closeTime)
}
</script>

<template>
  <view class="card" @tap="gotoPage(`/subPackages/page-mine/order-detail/order-detail?orderId=${props.orderData.id}&type=${props.type}&orderStatus=${props.orderData.status}&desc=${props.orderData.process_desc}`)">
    <ticket-style-card>
      <!-- 车票上方 -->
      <template #top>
        <!-- 时间/状态/价格 -->
        <view class="card__top">
          <CardTag :order-data="props.orderData" :type="props.type" />

          <text v-if="props.orderData.order_amount" class="card__top__right">
            NZD {{ props.orderData.order_amount }}
          </text>
          <text v-if="props.orderData.price" class="card__top__right">
            NZD {{ props.orderData.price }}
          </text>
        </view>

        <!-- 路线 -->
        <CardLine :order-data="props.orderData" :type="props.type" />

        <!-- 类型 -->
        <view v-if="props.type !== 'pickup_service' && props.orderData?.status !== 2" class="card__desc">
          <text class="card__desc__text left">
            Passenger:
          </text>
          <text class="card__desc__text right">
            {{ props.orderData?.passenger_type_text }}
          </text>
        </view>

        <view v-if="props.type !== 'pickup_service' && [0, 1].includes(props.orderData?.status)" class="card__tips">
          <text class="card__tips__text">
            {{ props.orderData.arriver_desc }}
          </text>
        </view>
      </template>
      <!-- 车票下方 -->
      <template #bottom>
        <view v-if="props.type !== 'pickup_service' && props.orderData?.status === 2" class="card__bottom">
          <view class="card__desc" style="margin: 0;">
            <text class="card__desc__text left">
              Passenger:
            </text>
            <text class="card__desc__text right">
              {{ props.orderData?.passenger_type_text }}
            </text>
          </view>
        </view>

        <view v-else-if=" props.orderData?.status !== 3" class="card__bottom">
          <view v-if="props.type !== 'pickup_service'" class="card__bottom__left" @tap.stop="emits('handle-board', props.orderData?.boarding_points)">
            <zui-svg-icon
              width="32rpx"
              hidden="32rpx"
              color="#5D7D9D"
              icon="location-light"
            />
            <text class="card__bottom__left__text">
              Boarding point
            </text>
          </view>
          <view v-else class="card__bottom__left">
            <text class="card__bottom__left__tag">
              {{ props.orderData?.car_type_text }}
            </text>
          </view>

          <view v-if="props.type !== 'pickup_service'" class="card__bottom__btn">
            <text v-if="props.orderData?.status === 1" class="card__bottom__btn__text">
              Verify
            </text>
            <view v-if="props.orderData?.status === 0" class="card__bottom__btn__down">
              Go and pay (<wd-count-down :time="handleSecond(props.orderData?.close_time)" custom-class="card__bottom__btn__down__time" />)
            </view>
          </view>
          <view v-else class="card__bottom__btnText">
            <view v-if="props.orderData?.status === 2" class="card__bottom__btnText__cat card__bottom__btnText__catNumber fs--14--400">
              {{ props.orderData?.coach_sn }}
            </view>
            <text v-if="props.orderData?.status === 1" class="card__bottom__btnText__cat">
              {{ props.orderData?.process_desc }}
            </text>
          </view>
        </view>

        <view v-if="props.orderData?.status === 3" class="card__tipsBut">
          {{ props.orderData?.cancel_desc }}
        </view>
      </template>
    </ticket-style-card>
  </view>
</template>

<style scoped lang="scss">
@import './ticket-card.scss'
</style>
