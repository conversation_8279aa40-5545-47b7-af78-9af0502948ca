<script setup lang="ts">
import type { TabsItem } from '@/model/common'

withDefaults(defineProps<{
  list?: TabsItem[]
}>(), {
  list: () => [] as TabsItem[],
})

const emits = defineEmits(['change'])
const current = defineModel<number>('modelValue', { default: 0 })

function handleChange() {
  emits('change', current.value)
}
</script>

<template>
  <view class="tabs">
    <view class="tabs__line" />
    <wd-tabs
      v-model="current"
      auto-line-width
      inactive-color="#ACBECF"
      color="#103A62"
      @change="handleChange"
    >
      <block v-for="item in list" :key="item.value">
        <wd-tab :title="`${item.label}`" :name="item.value" />
      </block>
    </wd-tabs>
  </view>
</template>

<style scoped lang="scss">
@import './status-tabs.scss'
</style>
