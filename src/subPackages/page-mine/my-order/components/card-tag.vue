<script setup lang="ts">
import type { IOrderListType } from '@/model/order'
import { dayjs } from 'wot-design-uni'

const props = defineProps<{
  orderData: IOrderListType
  type: IOrderListType['type']
}>()
</script>

<template>
  <view class="card__top__left">
    <text class="card__top__left__date">
      {{ dayjs(props.orderData.create_time).format('YYYY/MM/DD') }}
    </text>
    <text v-if="props.orderData.activity_type" class="card__top__left__tag grey">
      Package
    </text>
    <text v-if="props.orderData.status === 1 && props.type !== 'pickup_service'" class="card__top__left__tag blue">
      To be used
    </text>
    <text v-if="props.orderData.status === 2 && props.type !== 'pickup_service'" class="card__top__left__tag green">
      Used
    </text>
    <text v-if="props.orderData.status === 3" class="card__top__left__tag greyer">
      Cancelled
    </text>
    <text v-if="props.orderData.status === 0 && props.type !== 'pickup_service'" class="card__top__left__tag yellow">
      To be paid
    </text>

    <text v-if="props.orderData.status === 1 && props.type === 'pickup_service'" class="card__top__left__tag teal">
      Processing
    </text>
    <text v-if="props.orderData.status === 2 && props.type === 'pickup_service'" class="card__top__left__tag blue">
      Confirmed
    </text>
  </view>
</template>

<style scoped lang="scss">
@import './ticket-card/ticket-card.scss'
</style>
