<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { computed, ref, watch } from 'vue'
import { ApiOrderList } from '@/api/order'

import statusTabs from '../status-tabs/status-tabs.vue'
import ticketCard from '../ticket-card/ticket-card.vue'

const props = withDefaults(defineProps<{
  currentType?: number
  height?: string
}>(), {
  currentType: 0,
  height: '100%',
})

const current = ref<number>(0)

// 订单类型映射
const typeMapping = ['ticket', 'day_tour', 'pickup_service', 'attraction'] as const

// 获取当前订单类型
const currentOrderType = computed(() => {
  return typeMapping[props.currentType] || 'ticket'
})

const tabs = computed<TabsItem[]>(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    const list = [
      { label: 'Pending', value: 0 },
      { label: 'Used', value: 1 },
      { label: 'Cancelled', value: 2 },
      { label: 'Unpaid', value: 3 },
    ]
    return list
  }
  else {
    const list = [
      { label: 'Processing', value: 0 },
      { label: 'Confirmed', value: 1 },
      { label: 'Cancelled', value: 2 },
    ]
    return list
  }
})

// 状态映射
const statusMapping = computed(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    return ['pending', 'used', 'cancelled', 'unpaid']
  }
  else {
    return ['processing', 'confirmed', 'cancelled']
  }
})

// 获取当前状态
const currentStatus = computed(() => {
  return statusMapping.value[current.value] || ''
})

// 存储每个类型每个状态的订单列表数据
const orderListData = ref<Record<string, Record<string, IOrderListType[]>>>({})

// 初始化所有类型的默认状态订单数据
async function initAllTypeOrders() {
  const promises = typeMapping.map(async (type) => {
    const defaultStatus = statusMapping.value[0] // 每个类型的第一个状态

    try {
      const result = await ApiOrderList({
        type,
        status: defaultStatus,
        page_no: 1,
        page_size: 10,
      })

      if (!orderListData.value[type]) {
        orderListData.value[type] = {}
      }
      orderListData.value[type][defaultStatus] = result.lists || []
    }
    catch (error) {
      console.error(`获取 ${type} - ${defaultStatus} 订单失败:`, error)
      if (!orderListData.value[type]) {
        orderListData.value[type] = {}
      }
      orderListData.value[type][defaultStatus] = []
    }
  })

  await Promise.all(promises)
}

// 获取指定类型和状态的订单数据
async function fetchOrdersByTypeAndStatus(type: string, status: string) {
  try {
    const result = await ApiOrderList({
      type: type as any,
      status,
      page_no: 1,
      page_size: 10,
    })

    if (!orderListData.value[type]) {
      orderListData.value[type] = {}
    }
    orderListData.value[type][status] = result.lists || []

    return result.lists || []
  }
  catch (error) {
    console.error(`获取 ${type} - ${status} 订单失败:`, error)
    if (!orderListData.value[type]) {
      orderListData.value[type] = {}
    }
    orderListData.value[type][status] = []
    return []
  }
}

// 获取当前显示的订单列表
const currentOrderList = computed(() => {
  const type = currentOrderType.value
  const status = currentStatus.value
  return orderListData.value[type]?.[status] || []
})

// 监听订单类型变化，切换类型时请求当前状态的数据
watch(() => props.currentType, async () => {
  const type = currentOrderType.value
  const status = currentStatus.value

  // 如果当前类型和状态的数据不存在，则请求
  if (!orderListData.value[type]?.[status]) {
    await fetchOrdersByTypeAndStatus(type, status)
  }
}, { immediate: false })

// 监听状态变化，切换状态时请求当前类型的数据
watch(() => current.value, async () => {
  const type = currentOrderType.value
  const status = currentStatus.value

  // 如果当前类型和状态的数据不存在，则请求
  if (!orderListData.value[type]?.[status]) {
    await fetchOrdersByTypeAndStatus(type, status)
  }
})

// 初始化时请求所有类型的默认状态数据
watch(() => props.currentType, () => {
  if (props.currentType === 0) { // 只在第一个类型时初始化
    initAllTypeOrders()
  }
}, { immediate: true })

// 获取数据
function handleChange() {
  // 状态改变时的处理逻辑
}
</script>

<template>
  <view class="layout" :style="{ height }">
    <view class="layout__tabs">
      <statusTabs v-model="current" :list="tabs" @change="handleChange" />
    </view>
    <scroll-view class="layout__list" scroll-y :show-scrollbar="false">
      <view class="layout__list__inner">
        <ticket-card v-for="(item, index) in 10" :key="index" />
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import './layout.scss'
</style>
