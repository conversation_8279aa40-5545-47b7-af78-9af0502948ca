<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import { computed, ref } from 'vue'
import statusTabs from '../status-tabs/status-tabs.vue'
import ticketCard from '../ticket-card/ticket-card.vue'

const props = withDefaults(defineProps<{
  currentType?: number
  height?: string
}>(), {
  currentType: 0,
  height: '100%',
})

const current = ref<number>(0)

const tabs = computed<TabsItem[]>(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    const list = [
      { label: 'Pending', value: 0 },
      { label: 'Used', value: 1 },
      { label: 'Cancelled', value: 2 },
      { label: 'Unpaid', value: 3 },
    ]
    return list
  }
  else {
    const list = [
      { label: 'Processing', value: 0 },
      { label: 'Confirmed', value: 1 },
      { label: 'Cancelled', value: 2 },
    ]
    return list
  }
})

// 获取数据
function handleChange() {
  console.log('数据改变')
}
</script>

<template>
  <view class="layout" :style="{ height }">
    <view class="layout__tabs">
      <statusTabs v-model="current" :list="tabs" @change="handleChange" />
    </view>
    <scroll-view class="layout__list" scroll-y :show-scrollbar="false">
      <view class="layout__list__inner">
        <ticket-card v-for="(item, index) in 10" :key="index" />
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import './layout.scss'
</style>
