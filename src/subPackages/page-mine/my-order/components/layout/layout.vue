<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { computed, ref, watch } from 'vue'
import statusTabs from '../status-tabs/status-tabs.vue'
import ticketCard from '../ticket-card/ticket-card.vue'

const props = withDefaults(defineProps<{
  currentType?: number
  height?: string
  orderData?: Record<string, Record<string, IOrderListType[]>>
  fetchOrders?: (type: string, status: string) => Promise<IOrderListType[]>
}>(), {
  currentType: 0,
  height: '100%',
  orderData: () => ({}),
  fetchOrders: () => Promise.resolve([]),
})

const current = ref<number>(0)

// 订单类型映射
const typeMapping = ['ticket', 'day_tour', 'pickup_service', 'attraction'] as const

// 获取指定类型的状态映射
function getStatusMappingForType(typeIndex: number) {
  if ([0, 1, 3].includes(typeIndex)) {
    return ['pending', 'used', 'cancelled', 'unpaid']
  }
  else {
    return ['processing', 'confirmed', 'cancelled']
  }
}

// 获取当前类型
const currentOrderType = computed(() => {
  return typeMapping[props.currentType] || 'ticket'
})

// 获取当前类型的状态映射
const statusMapping = computed(() => {
  return getStatusMappingForType(props.currentType)
})

// 获取当前状态
const currentStatus = computed(() => {
  return statusMapping.value[current.value] || ''
})

const tabs = computed<TabsItem[]>(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    const list = [
      { label: 'Pending', value: 0 },
      { label: 'Used', value: 1 },
      { label: 'Cancelled', value: 2 },
      { label: 'Unpaid', value: 3 },
    ]
    return list
  }
  else {
    const list = [
      { label: 'Processing', value: 0 },
      { label: 'Confirmed', value: 1 },
      { label: 'Cancelled', value: 2 },
    ]
    return list
  }
})

// 获取当前显示的订单列表
const currentOrderList = computed(() => {
  const type = currentOrderType.value
  const status = currentStatus.value
  return props.orderData[type]?.[status] || []
})

// 监听状态变化，按需请求数据
watch(() => current.value, async () => {
  const type = currentOrderType.value
  const status = currentStatus.value

  // 如果当前类型和状态的数据不存在，则请求
  if (!props.orderData[type]?.[status] && props.fetchOrders) {
    await props.fetchOrders(type, status)
  }
})

// 获取数据
async function handleChange() {
  const type = currentOrderType.value
  const status = currentStatus.value

  // 如果当前类型和状态的数据不存在，则请求
  if (!props.orderData[type]?.[status] && props.fetchOrders) {
    await props.fetchOrders(type, status)
  }
}
</script>

<template>
  <view class="layout" :style="{ height }">
    <view class="layout__tabs">
      <statusTabs v-model="current" :list="tabs" @change="handleChange" />
    </view>
    <scroll-view class="layout__list" scroll-y :show-scrollbar="false">
      <view class="layout__list__inner">
        <ticket-card
          v-for="(item, index) in currentOrderList"
          :key="item.id || index"
          :order-data="item"
        />
        <view v-if="currentOrderList.length === 0" class="layout__list__empty">
          暂无订单数据
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import './layout.scss'
</style>
