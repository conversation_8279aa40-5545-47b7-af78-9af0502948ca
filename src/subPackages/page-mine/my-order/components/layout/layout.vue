<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { computed, ref, watch } from 'vue'
import { ApiOrderList } from '@/api/order'
import { useList } from '@/hooks/useList'
import statusTabs from '../status-tabs/status-tabs.vue'
import ticketCard from '../ticket-card/ticket-card.vue'

const props = withDefaults(defineProps<{
  currentType?: number
  height?: string
}>(), {
  currentType: 0,
  height: '100%',
})

const current = ref<number>(0)

// 订单类型映射
const typeMapping = ['ticket', 'day_tour', 'pickup_service', 'attraction'] as const

// 获取当前订单类型
const currentOrderType = computed(() => {
  return typeMapping[props.currentType] || 'ticket'
})

const tabs = computed<TabsItem[]>(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    const list = [
      { label: 'Pending', value: 0 },
      { label: 'Used', value: 1 },
      { label: 'Cancelled', value: 2 },
      { label: 'Unpaid', value: 3 },
    ]
    return list
  }
  else {
    const list = [
      { label: 'Processing', value: 0 },
      { label: 'Confirmed', value: 1 },
      { label: 'Cancelled', value: 2 },
    ]
    return list
  }
})

// 状态映射
const statusMapping = computed(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    return ['pending', 'used', 'cancelled', 'unpaid']
  }
  else {
    return ['processing', 'confirmed', 'cancelled']
  }
})

// 获取当前状态
const currentStatus = computed(() => {
  return statusMapping.value[current.value] || ''
})

// 使用useList hook管理订单列表
const {
  loading,
  list: orderList,
  total,
  finished,
  refresh,
  loadMore,
  setParams,
} = useList<IOrderListType>(ApiOrderList, {
  type: currentOrderType.value,
  status: currentStatus.value,
})

// 监听订单类型变化
watch(() => props.currentType, () => {
  setParams({
    type: currentOrderType.value,
    status: currentStatus.value,
  })
}, { immediate: true })

// 监听状态变化
watch(() => current.value, () => {
  setParams({
    type: currentOrderType.value,
    status: currentStatus.value,
  })
})

// 获取数据
function handleChange() {
  console.log('数据改变', {
    type: currentOrderType.value,
    status: currentStatus.value,
  })
}
</script>

<template>
  <view class="layout" :style="{ height }">
    <view class="layout__tabs">
      <statusTabs v-model="current" :list="tabs" @change="handleChange" />
    </view>
    <scroll-view class="layout__list" scroll-y :show-scrollbar="false">
      <view class="layout__list__inner">
        <ticket-card v-for="(item, index) in 10" :key="index" />
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import './layout.scss'
</style>
