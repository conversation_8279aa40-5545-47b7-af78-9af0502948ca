<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import { computed, ref, watch } from 'vue'
import statusTabs from '../status-tabs/status-tabs.vue'
import ticketCard from '../ticket-card/ticket-card.vue'

const props = withDefaults(defineProps<{
  currentType?: number
  height?: string
  orderListInstances?: Record<string, Record<string, any>>
  getListInstance?: (type: string, status: string) => any
  updateStatusIndex?: (typeIndex: number, statusIndex: number) => void
}>(), {
  currentType: 0,
  height: '100%',
  orderListInstances: () => ({}),
  getListInstance: () => null,
  updateStatusIndex: () => {},
})

const current = ref<number>(0)

// 订单类型映射
const typeMapping = ['ticket', 'day_tour', 'pickup_service', 'attraction'] as const

// 获取指定类型的状态标签配置
function getStatusTabsForType(typeIndex: number) {
  if ([0, 1, 3].includes(typeIndex)) {
    return [
      { label: 'Pending', value: 'pending' },
      { label: 'Used', value: 'used' },
      { label: 'Cancelled', value: 'cancelled' },
      { label: 'Unpaid', value: 'unpaid' },
    ]
  }
  else {
    return [
      { label: 'Processing', value: 'processing' },
      { label: 'Confirmed', value: 'confirmed' },
      { label: 'Cancelled', value: 'cancelled' },
    ]
  }
}

// 获取当前类型
const currentOrderType = computed(() => {
  return typeMapping[props.currentType] || 'ticket'
})

// 获取当前类型的状态标签配置
const currentStatusTabs = computed(() => {
  return getStatusTabsForType(props.currentType)
})

// 获取当前状态
const currentStatus = computed(() => {
  return currentStatusTabs.value[current.value]?.value || ''
})

const tabs = computed<TabsItem[]>(() => {
  if ([0, 1, 3].includes(props.currentType)) {
    const list = [
      { label: 'Pending', value: 0 },
      { label: 'Used', value: 1 },
      { label: 'Cancelled', value: 2 },
      { label: 'Unpaid', value: 3 },
    ]
    return list
  }
  else {
    const list = [
      { label: 'Processing', value: 0 },
      { label: 'Confirmed', value: 1 },
      { label: 'Cancelled', value: 2 },
    ]
    return list
  }
})

// 获取当前的 useList 实例
const currentListInstance = computed(() => {
  const type = currentOrderType.value
  const status = currentStatus.value
  return props.getListInstance?.(type, status)
})

// 获取当前显示的订单列表
const currentOrderList = computed(() => {
  return currentListInstance.value?.list || []
})

// 获取当前列表的加载状态
// const loading = computed(() => {
//   return currentListInstance.value?.loading || false
// })

// 获取当前列表是否已完成
// const finished = computed(() => {
//   return currentListInstance.value?.finished || false
// })

// 监听状态变化，按需请求数据
watch(() => current.value, async (newStatusIndex) => {
  // 通知父组件状态变化
  props.updateStatusIndex?.(props.currentType, newStatusIndex)

  const listInstance = currentListInstance.value

  // 如果当前状态的数据为空且不在加载中，则刷新数据
  if (listInstance && listInstance.list.length === 0 && !listInstance.loading) {
    await listInstance.refresh()
  }
})
</script>

<template>
  <view class="layout" :style="{ height }">
    <view class="layout__tabs">
      <statusTabs v-model="current" :list="tabs" />
    </view>
    <scroll-view class="layout__list" scroll-y :show-scrollbar="false">
      <view class="layout__list__inner">
        <ticket-card
          v-for="(item, index) in currentOrderList"
          :key="item.id || index"
          :order-data="item"
        />

        <!-- 加载状态 -->
        <!-- <view v-if="loading" class="layout__list__loading">
          <text>加载中...</text>
        </view> -->

        <!-- 空状态 -->
        <!-- <view v-if="!loading && currentOrderList.length === 0" class="layout__list__empty">
          <text>暂无订单数据</text>
        </view> -->

        <!-- 加载完成提示 -->
        <!-- <view v-if="!loading && currentOrderList.length > 0 && finished" class="layout__list__finished">
          <text>没有更多数据了</text>
        </view> -->
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import './layout.scss'
</style>
