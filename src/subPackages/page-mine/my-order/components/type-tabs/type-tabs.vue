<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import { nextTick, ref } from 'vue'

withDefaults(defineProps<{
  list?: TabsItem[]
}>(), {
  list: () => [] as TabsItem[],
})
const emits = defineEmits(['change'])
const current = defineModel<number>('modelValue', { default: 0 })
const currentView = ref<string>('type__item__0')

function selectTab(index: number) {
  current.value = index
  currentView.value = `type__item__${index}` // 添加这一行
  nextTick(() => {
    emits('change')
  })
}
</script>

<template>
  <scroll-view class="type" scroll-x scroll-with-animation :show-scrollbar="false">
    <view
      v-for="(item, index) in list"
      :id="`type__item__${index}`"
      :key="index"
      class="type__item"
      :scroll-into-view="currentView"
      :class="{ active: current === index }"
      @tap="selectTab(index)"
    >
      {{ item.label }}
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
@import './type-tabs.scss'
</style>
