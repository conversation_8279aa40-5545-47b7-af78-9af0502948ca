<script lang="ts" setup>
// import type { BaseSwiperItemType } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { computed, ref } from 'vue'

const props = defineProps<{
  data: IOrderListType['boarding_points']
}>()

const show = defineModel<boolean>('modelValue', { default: false })

const active = ref(0)

const swiperList = computed(() => {
  return props.data.map((item, index) => ({
    id: index,
    name: item.name,
    image: item.image_lists[0],
  }))
})

const activeItem = computed(() => props.data[active.value])
</script>

<template>
  <view>
    <!-- 车辆信息 -->
    <base-popup v-model="show" title="Boarding point" height="820rpx">
      <view class="wrapper">
        <view class="wrapper__top">
          <view v-for="(item, index) in props.data" :key="item.name" class="wrapper__top__item" :class="[{ active: index === active }]" @tap="active = index">
            <text class="wrapper__top__item__text fs--12--400">
              {{ item.name }}
            </text>
          </view>
        </view>
        <view class="wrapper__info">
          <view class="wrapper__info__left">
            <view class="wrapper__info__left__title">
              {{ activeItem.name }}
            </view>
            <view class="wrapper__info__left__desc">
              {{ activeItem.address }}
            </view>
          </view>
          <zui-svg-icon icon="send-light" color="#103A62" width="40rpx" height="40rpx" />
        </view>
        <base-swiper :list="swiperList" height="350rpx" border-radius="24rpx" />
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './board-point-popup.scss'
</style>
