<script setup lang="ts">
import type { IOrderListType } from '@/model/order'

const props = defineProps<{
  orderData: IOrderListType
  type: IOrderListType['type']
}>()

function formatSeconds(seconds: number): string {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  return `${h}h${m}m`
}
</script>

<template>
  <view class="card__line">
    <view class="card__line__stop">
      <view class="card__line__stop__name">
        {{ props.orderData.from }}
      </view>
      <view class="card__line__stop__date">
        {{ props.orderData.departure_time_text }}
      </view>
    </view>
    <view class="card__line__center">
      <text class="card__line__center__num fs--14--400">
        {{ props.orderData?.coach_sn }}
      </text>
      <image class="card__line__center__img" src="/static/mine/location_right.png" />
      <text class="card__line__center__km fs--12--400">
        {{ type === 'pickup_service' ? props.orderData?.distance : formatSeconds(Number(props.orderData?.travel_time)) }}
      </text>
    </view>
    <view class="card__line__stop right">
      <view class="card__line__stop__name">
        {{ props.orderData.to }}
      </view>
      <view class="card__line__stop__date">
        {{ props.orderData.arrive_time_text }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './ticket-card/ticket-card.scss'
</style>
