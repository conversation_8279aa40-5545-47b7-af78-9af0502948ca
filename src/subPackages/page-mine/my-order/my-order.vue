<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import { computed, nextTick, onMounted, ref } from 'vue'
import { ApiOrderList } from '@/api/order'
import { useList } from '@/hooks/useList'
import { useNavBar } from '@/hooks/useNavBar'
import boardPointPopup from './components/board-point-popup/board-point-popup.vue'
import layout from './components/layout/layout.vue'
import typeTabs from './components/type-tabs/type-tabs.vue'

// 展示board-point
const isOpen = ref<boolean>(false)
// 详情
const boardDetail = ref<IOrderListType['boarding_points']>([])

const { placeholderHeight } = useNavBar(108)
const typeList = ref<TabsItem[]>([
  { label: 'Ticket', value: 'ticket' },
  { label: 'Day Tours', value: 'day_tour' },
  { label: 'Transfer', value: 'pickup_service' },
  // TODO 暂时不展示
  // { label: 'Attraction', value: 'attraction' },
])
const tabsHeight = ref<number>(0)
const contentHeight = computed(() => {
  return `calc(100vh - ${placeholderHeight.value + tabsHeight.value}px)`
})

// 当前类型
const currentType = ref<number>(0)

// 每个类型当前选中的状态索引
const currentStatusIndexes = ref<Record<number, number>>({
  0: 1, // ticket
  1: 1, // day_tour
  2: 1, // pickup_service
  3: 1, // attraction
})
// 获取指定类型的状态标签配置
function getStatusTabsForType(typeIndex: number) {
  if ([0, 1, 3].includes(typeIndex)) {
    return [
      { label: 'Pending', value: 1 },
      { label: 'Used', value: 2 },
      { label: 'Cancelled', value: 3 },
      { label: 'Unpaid', value: 0 },
    ]
  }
  else {
    return [
      { label: 'Processing', value: 1 },
      { label: 'Confirmed', value: 2 },
      { label: 'Cancelled', value: 3 },
    ]
  }
}

// 为每个类型创建独立的 useList 实例
const orderListInstances = ref<Record<string, Record<string, any>>>({})

// 初始化所有类型的 useList 实例
function initOrderListInstances() {
  typeList.value.forEach((type, typeIndex) => {
    const statusTabs = getStatusTabsForType(typeIndex)
    orderListInstances.value[type.value] = {}

    statusTabs.forEach((statusTab) => {
      orderListInstances.value[type.value][statusTab.value] = useList<IOrderListType>(
        ApiOrderList,
        { type: type.value, status: statusTab.value },
      )
    })
  })
}

// 初始化所有类型的默认状态订单数据
async function initAllTypeOrders() {
  const promises = typeList.value.map(async (type, typeIndex) => {
    const statusTabs = getStatusTabsForType(typeIndex)
    const defaultStatus = statusTabs[0].value // 每个类型的第一个状态

    // 获取对应的 useList 实例并刷新数据
    const listInstance = orderListInstances.value[type.value]?.[defaultStatus]
    if (listInstance) {
      await listInstance.refresh()
    }
  })

  await Promise.all(promises)
}

// 获取指定类型和状态的 useList 实例
function getOrderListInstance(type: string, status: number) {
  return orderListInstances.value[type]?.[status]
}

// 更新指定类型的状态索引
function updateStatusIndex(typeIndex: number, statusIndex: number) {
  currentStatusIndexes.value[typeIndex] = statusIndex
}

// 监听当前类型，变化请求数据
function handleSwiperChange(e: any) {
  currentType.value = e.detail.current
}

onMounted(async () => {
  // 初始化所有类型的 useList 实例
  initOrderListInstances()

  // 初始化所有类型的订单数据
  await initAllTypeOrders()

  nextTick(() => {
    const query = uni.createSelectorQuery().in(this) // 注意要指定 this 才会在当前组件内查找
    query.select('.wrapper__tabs').boundingClientRect((rect) => {
      if (rect) {
        tabsHeight.value = (rect as UniApp.NodeInfo).height || 0
      }
    }).exec()
  })
})

// 获取当前显示的订单类型
function getCurrentOrderType() {
  return typeList.value[currentType.value]?.value || 'ticket'
}

// 获取当前显示的状态
function getCurrentStatus(typeIndex: number): number {
  // const statusTabs = getStatusTabsForType(typeIndex)
  const statusIndex = currentStatusIndexes.value[typeIndex] || 0

  return statusIndex
  // return statusTabs[statusIndex]?.value || statusTabs[0].value
  // return statusTabs.find(item => item.value === (statusIndex))?.value || statusTabs[0].value
}

// 获取当前显示的 useList 实例
function getCurrentListInstance() {
  const type = getCurrentOrderType() as string
  const status = getCurrentStatus(currentType.value)
  return getOrderListInstance(type, status)
}

// 下拉刷新
onPullDownRefresh(async () => {
  try {
    const listInstance = getCurrentListInstance()
    if (listInstance) {
      await listInstance.refresh()
    }
  }
  catch (error) {
    console.error('下拉刷新失败:', error)
  }
  finally {
    uni.stopPullDownRefresh()
  }
})

// 触底加载更多
onReachBottom(async () => {
  try {
    const listInstance = getCurrentListInstance()
    if (listInstance && !listInstance.loading && !listInstance.finished) {
      await listInstance.loadMore()
    }
  }
  catch (error) {
    console.error('加载更多失败:', error)
  }
})

function handleBoardClick(data: IOrderListType['boarding_points']) {
  boardDetail.value = data
  isOpen.value = true
}
</script>

<template>
  <page>
    <nav-bar-page title="My order" />
    <view class="body">
      <view class="wrapper">
        <!-- 类型tabs -->
        <view class="wrapper__tabs">
          <type-tabs v-model="currentType" :list="typeList" />
          <view class="wrapper__tabs__space">
            <view class="wrapper__tabs__space__line" />
          </view>
        </view>
        <!-- 计算好的内容高度 -->
        <view class="wrapper__content" :style="{ height: contentHeight }">
          <swiper
            class="wrapper__content__swiper"
            :current="currentType"
            @change="handleSwiperChange"
          >
            <swiper-item
              v-for="(item, index) in typeList"
              :key="index"
            >
              <layout
                :current-type="index"
                :height="contentHeight"
                :order-list-instances="orderListInstances"
                :get-list-instance="getOrderListInstance"
                :update-status-index="updateStatusIndex"
                @board-click="handleBoardClick"
              />
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>

    <board-point-popup v-model="isOpen" :data="boardDetail" />
  </page>
</template>

<style scoped lang="scss">
@import './my-order.scss'
</style>
