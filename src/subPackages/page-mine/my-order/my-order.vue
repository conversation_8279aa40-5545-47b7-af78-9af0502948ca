<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import type { IOrderListType } from '@/model/order'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ApiOrderList } from '@/api/order'
import { useNavBar } from '@/hooks/useNavBar'
import layout from './components/layout/layout.vue'
import typeTabs from './components/type-tabs/type-tabs.vue'

const { placeholderHeight } = useNavBar(108)
const typeList = ref<TabsItem[]>([
  { label: 'Ticket', value: 'ticket' },
  { label: 'Day Tours', value: 'day_tour' },
  { label: 'Transfer', value: 'pickup_service' },
  { label: 'Attraction', value: 'attraction' },
])
const tabsHeight = ref<number>(0)
const contentHeight = computed(() => {
  return `calc(100vh - ${placeholderHeight.value + tabsHeight.value}px)`
})

// 当前类型
const currentType = ref<number>(0)

// 订单类型映射
const typeMapping = ['ticket', 'day_tour', 'pickup_service', 'attraction'] as const

// 获取指定类型的状态映射
function getStatusMappingForType(typeIndex: number) {
  if ([0, 1, 3].includes(typeIndex)) {
    return ['pending', 'used', 'cancelled', 'unpaid']
  }
  else {
    return ['processing', 'confirmed', 'cancelled']
  }
}

// 存储所有类型的订单数据：{ type: { status: IOrderListType[] } }
const orderData = ref<Record<string, Record<string, IOrderListType[]>>>({})

// 初始化标记
const isInitialized = ref(false)

// 初始化所有类型的默认状态订单数据
async function initAllTypeOrders() {
  if (isInitialized.value)
    return
  isInitialized.value = true

  const promises = typeMapping.map(async (type, index) => {
    const typeStatusMapping = getStatusMappingForType(index)
    const defaultStatus = typeStatusMapping[0] // 每个类型的第一个状态

    try {
      const result = await ApiOrderList({
        type,
        status: defaultStatus,
        page_no: 1,
        page_size: 10,
      })

      if (!orderData.value[type]) {
        orderData.value[type] = {}
      }
      orderData.value[type][defaultStatus] = result.lists || []
    }
    catch (error) {
      console.error(`获取 ${type} - ${defaultStatus} 订单失败:`, error)
      if (!orderData.value[type]) {
        orderData.value[type] = {}
      }
      orderData.value[type][defaultStatus] = []
    }
  })

  await Promise.all(promises)
}

// 获取指定类型和状态的订单数据
async function fetchOrdersByTypeAndStatus(type: string, status: string) {
  try {
    const result = await ApiOrderList({
      type: type as any,
      status,
      page_no: 1,
      page_size: 10,
    })

    if (!orderData.value[type]) {
      orderData.value[type] = {}
    }
    orderData.value[type][status] = result.lists || []

    return result.lists || []
  }
  catch (error) {
    console.error(`获取 ${type} - ${status} 订单失败:`, error)
    if (!orderData.value[type]) {
      orderData.value[type] = {}
    }
    orderData.value[type][status] = []
    return []
  }
}

// 监听当前类型，变化请求数据
function handleSwiperChange(e: any) {
  currentType.value = e.detail.current
}

onMounted(async () => {
  // 初始化所有类型的订单数据
  await initAllTypeOrders()

  nextTick(() => {
    const query = uni.createSelectorQuery().in(this) // 注意要指定 this 才会在当前组件内查找
    query.select('.wrapper__tabs').boundingClientRect((rect) => {
      if (rect) {
        tabsHeight.value = (rect as UniApp.NodeInfo).height || 0
      }
    }).exec()
  })
})
</script>

<template>
  <page>
    <nav-bar-page title="My order" />
    <view class="body">
      <view class="wrapper">
        <!-- 类型tabs -->
        <view class="wrapper__tabs">
          <type-tabs v-model="currentType" :list="typeList" />
          <view class="wrapper__tabs__space">
            <view class="wrapper__tabs__space__line" />
          </view>
        </view>
        <!-- 计算好的内容高度 -->
        <view class="wrapper__content" :style="{ height: contentHeight }">
          <swiper
            class="wrapper__content__swiper"
            :current="currentType"
            @change="handleSwiperChange"
          >
            <swiper-item
              v-for="(item, index) in typeList"
              :key="index"
            >
              <layout
                :current-type="index"
                :height="contentHeight"
                :order-data="orderData"
                :fetch-orders="fetchOrdersByTypeAndStatus"
              />
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-order.scss'
</style>
