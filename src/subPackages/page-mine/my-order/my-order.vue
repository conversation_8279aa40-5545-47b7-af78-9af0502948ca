<script setup lang="ts">
import type { TabsItem } from '@/model/common'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useNavBar } from '@/hooks/useNavBar'
import layout from './components/layout/layout.vue'
import typeTabs from './components/type-tabs/type-tabs.vue'

const { placeholderHeight } = useNavBar(108)
const typeList = ref<TabsItem[]>([
  { label: 'Ticket', value: 'ticket' },
  { label: 'Day Tours', value: 'day_tour' },
  { label: 'Transfer', value: 'pickup_service' },
  { label: 'Attraction', value: 'attraction' },
])
const tabsHeight = ref<number>(0)
const contentHeight = computed(() => {
  return `calc(100vh - ${placeholderHeight.value + tabsHeight.value}px)`
})

// 当前类型
const currentType = ref<number>(0)
const pageList = ref<any[]>([])

// 初始化页面列表
function initPageList() {
  pageList.value = typeList.value.map(() => {
    return {
      list: [],
    }
  })
}
initPageList()

// 监听当前类型，变化请求数据
function handleSwiperChange(e: any) {
  currentType.value = e.detail.current
}

watch(() => currentType.value, (newVal: number) => {
  if (pageList.value[newVal].list.length === 0) {
    console.warn('请求数据')
  }
}, {
  immediate: true,
})

onMounted(async () => {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this) // 注意要指定 this 才会在当前组件内查找
    query.select('.wrapper__tabs').boundingClientRect((rect) => {
      if (rect) {
        tabsHeight.value = (rect as UniApp.NodeInfo).height || 0
      }
    }).exec()
  })
})
</script>

<template>
  <page>
    <nav-bar-page title="My order" />
    <view class="body">
      <view class="wrapper">
        <!-- 类型tabs -->
        <view class="wrapper__tabs">
          <type-tabs v-model="currentType" :list="typeList" />
          <view class="wrapper__tabs__space">
            <view class="wrapper__tabs__space__line" />
          </view>
        </view>
        <!-- 计算好的内容高度 -->
        <view class="wrapper__content" :style="{ height: contentHeight }">
          <swiper
            class="wrapper__content__swiper"
            :current="currentType"
            @change="handleSwiperChange"
          >
            <swiper-item
              v-for="(item, index) in pageList"
              :key="index"
            >
              <layout :current-type="currentType" :height="contentHeight" />
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-order.scss'
</style>
