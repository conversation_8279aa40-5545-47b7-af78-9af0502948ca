<script setup lang="ts">
import { ref } from 'vue'
import couponsCard from './components/coupons-card/coupons-card.vue'

const isRemove = ref<boolean>(false)
const isEdit = ref<boolean>(false)

const selected = ref<number[]>([] as number[])

function couponSelect(index: number) {
  const findIndex = selected.value.findIndex((e: number) => e === index)
  if (findIndex === -1) {
    selected.value.push(index)
  }
  else {
    selected.value.splice(findIndex, 1)
  }
}

function removeCoupon() {
  isRemove.value = true
}

function selectCoupon() {
  isEdit.value = !isEdit.value
}

function submitRemove() {
  isRemove.value = false
  isEdit.value = false
}
</script>

<template>
  <page>
    <nav-bar-page :title="$t('mine.points.couponPackage')">
      <template #right>
        <text class="nav-edit" @tap="selectCoupon">
          {{ $t('mine.edit') }}
        </text>
      </template>
    </nav-bar-page>

    <view class="body">
      <view class="container">
        <coupons-card
          v-for="(item, index) in 10"
          :key="index"
          :is-select="isEdit"
          :index="index"
          :is-active="selected.includes(index)"
          @select="couponSelect"
        />
      </view>
    </view>

    <fixed-bottom-bar v-if="isEdit">
      <view class="bottom_bar" @tap="removeCoupon">
        <view class="bottom_bar__btn">
          {{ $t('mine.delete') }}
        </view>
      </view>
    </fixed-bottom-bar>

    <!-- 确认弹窗 -->
    <confirm-popup
      v-model="isRemove"
      height="380rpx"
      title="Confirm deletion"
      content="Check the ticket you want to delete. Once deleted, it cannot be restored"
      :confirm-text="$t('mine.delete')"
      :cancel-text="$t('mine.return')"
      confirm-btn-color="red"
      @confirm="submitRemove"
    />
  </page>
</template>

<style scoped lang="scss">
@import './coupon-package.scss'
</style>
