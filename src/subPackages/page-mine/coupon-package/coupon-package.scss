.nav-edit {
    color: #103A62;
    font-size: 26rpx;
    text-decoration-line: underline;
    font-weight: 400;
    font-family: <PERSON><PERSON><PERSON> FangYuanTi VF;
}

.tabs {
    padding-bottom: 12rpx;
}

.container {
    padding: 0 32rpx;

    &__tips {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-bottom: 32rpx;
        padding-bottom: 32rpx;

        &__text {
            color: #A4A7AE;
            font-size: 24rpx;
            font-family: Alima<PERSON> FangYuanTi VF;
            font-family: 400;

            &.blue {
                color: #103A62;
                text-decoration-line: underline;
            }
        }
    }
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #B42318;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}