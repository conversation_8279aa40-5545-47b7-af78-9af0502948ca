<script lang="ts" setup>
const props = withDefaults(defineProps<{
  index?: number // 渲染索引暂时使用
  isSelect?: boolean // 是否展开选择框
  isActive?: boolean // 是否被选中
}>(), {
  index: 0,
  isSelect: false,
  isActive: false,
})

const emits = defineEmits<{
  (e: 'select', index: number): void
  (e: 'voucher'): void
}>()

function select() {
  emits('select', props.index)
}

function voucher() {
  emits('voucher')
}
</script>

<template>
  <view class="card" :class="{ select: isSelect }">
    <view v-if="isSelect" class="select">
      <zui-svg-icon v-if="isActive" icon="selected" width="28rpx" height="28rpx" @tap="select" />
      <zui-svg-icon v-else icon="un-selected" width="28rpx" height="28rpx" @tap="select" />
    </view>
    <view class="coupon">
      <image class="coupon__bg" src="/static/mine/coupons.png" />
      <view class="coupon__body">
        <view class="coupon__body__left">
          <text class="coupon__body__left__price">
            50
          </text>
        </view>
        <view class="coupon__body__center">
          <view class="coupon__body__center__title">
            Day tours coupon for independent tourists in New Zealand
          </view>
          <view class="coupon__body__center__date">
            Expires on 31 July 2025
          </view>
          <view class="coupon__body__center__tag">
            <image class="coupon__body__center__tag__bg" src="/static/mine/coupon-tag.png" />
            <view class="coupon__body__center__tag__content">
              <text class="coupon__body__center__tag__content__price">
                50
              </text>
              <text class="coupon__body__center__tag__content__unit">
                points
              </text>
            </view>
          </view>
        </view>
        <view class="coupon__body__right" @tap="voucher">
          <!-- <text class="coupon__body__right__text">Used</text> -->
          <!-- <text class="coupon__body__right__text grey">Expired</text> -->
          <text class="coupon__body__right__text blue">
            Voucher
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './coupons-card.scss'
</style>
