<script setup lang="ts">
import type { NoticeInfo } from '@/model/common'
import { onMounted, ref } from 'vue'
import { ApiGetNoticeInfo } from '@/api/common'
import { useTabbar } from '@/hooks/useTabbar'
import { useUserStore } from '@/stores/user'

const info = ref<NoticeInfo>({} as NoticeInfo)

const { getUserInfo } = useUserStore()

const { bottomSafeArea } = useTabbar()

async function getInfo() {
  const res = await ApiGetNoticeInfo({
    type: 'generalprotocol',
    name: 'logout_agreement',
  })
  info.value = res
}

async function cancel() {
  console.warn('注销账号')
  await getUserInfo()
}

onMounted(() => {
  getInfo()
})
</script>

<template>
  <view class="wrapper">
    <view class="wrapper__content">
      <mp-html :content="info.content" />
    </view>
    <view class="wrapper__bottom" :style="{ paddingBottom: `${bottomSafeArea + 10}px` }">
      <view class="wrapper__bottom__btn" @click="cancel">
        Cancel account
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.wrapper{
    color: #414651;
    font-size: 24rpx;
    font-weight: 600;
    line-height: 40rpx;
    padding: 0;

    &__content{
        padding: 0 32rpx;
    }

    &__bottom{
        background-color: #fff;
        box-shadow: 0 -6px 16px 0 rgba(0, 0, 0, 0.03);
        padding: 20rpx 32rpx 0 32rpx;
        width: 750rpx;

        &__btn{
            background-color: #103A62;
            border-radius: 24rpx;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            line-height: 48rpx;
            font-weight: 500;
            padding: 24rpx 0;
        }
    }
}
</style>
