<script setup lang="ts">
import { ref } from 'vue'

const isCancel = ref<boolean>(false)

const str = `I. Conditions for Cancellation Application ‌
Account status requirement: ‌
Account must be in normal use and not frozen or restricted from logging in ‌;
No sensitive operations (such as password modification, change of bound mobile phone/email) in the past 14 days ‌;
The account must complete real-name authentication and the identity information must be consistent with that at the time of registration ‌.
Liquidation of assets and equity ‌、
All balances in the account, virtual currencies, unwithdrawn earnings, etc. must be reset to zero ‌;
Unbind all subscription services (such as membership, auto-renewal) and third-party payments ‌;
Complete unfinished transactions or disputes ‌.

Ii. Cancellation process ‌
Submit an application ‌
Submit the application through [Account Settings - Security Center - Account Cancellation] entry ‌;
Identity verification must be completed via SMS verification code, facial recognition or security question ‌.
The cooling-off period is set at ‌
After submitting the application, you will enter a ‌ 15-day cooling-off period ‌ during which you are prohibited from logging in or using your account ‌.
If you log in during the cooling-off period, it will be regarded as a withdrawal of the cancellation application and you need to resubmit ‌.
Finally log out ‌
After the cooling-off period ends, the system automatically clears the data and releases the account ‌.
`
</script>

<template>
  <page>
    <nav-bar-page title="Cancel account" />
    <view class="body">
      <!-- 注销后 -->
      <view v-if="isCancel" class="content">
        <view class="content__center">
          <zui-svg-icon icon="tick-square" width="180rpx" height="180rpx" color="#103A62" />
          <text class="content__center__text">
            New Zealand Freelancer will process your application and delete all your data within 15 days. If you operate to cancel the cancellation within 15 days, your cancellation application will be cancelled by default
          </text>
        </view>
      </view>
      <!-- 注销前 -->
      <view v-else class="wrapper">
        <mp-html :content="str" style="width: 100%;float: left;" />
      </view>
    </view>
    <fixed-bottom-bar>
      <view class="bottom_bar" @tap="isCancel = !isCancel">
        <view class="bottom_bar__btn">
          Revocation and cancellation
        </view>
      </view>
    </fixed-bottom-bar>
  </page>
</template>

<style scoped lang='scss'>
@import './cancel-account.scss'
</style>
