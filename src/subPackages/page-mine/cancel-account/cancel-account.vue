<script setup lang="ts">
import type { Component } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { ref, shallowRef } from 'vue'
import canceAfter from './components/cance-after/cance-after.vue'
import canceBefore from './components/cance-before/cance-before.vue'

const currentComponent = ref<string>('canceAfter')
const componentMap = shallowRef<Record<string, Component>>({
  canceAfter,
  canceBefore,
})

onLoad(() => {
  // 接口信息判断展示什么组件
})
</script>

<template>
  <page>
    <nav-bar-page title="Cancel account" />
    <!-- 组件切换 -->
    <view class="body">
      <component :is="componentMap[currentComponent]" />
    </view>
  </page>
</template>

<style scoped lang='scss'>
</style>
