<script setup lang="ts">
import { ref } from 'vue'
import { gotoPage } from '@/utils/router'
import changeInfoPopup from './components/change-info-popup.vue'

const show = ref<boolean>(false)
const openType = ref<string>('')

function openPopup(type: string) {
  openType.value = type
  show.value = true
}

function goChange() {
  gotoPage(`/subPackages/page-mine/change-info/change-info?type=${openType.value}`)
  show.value = false
}
</script>

<template>
  <page>
    <nav-bar-page title="Account security" />
    <view class="body">
      <view class="list">
        <view class="list__item">
          <text class="list__item__title">
            Google
          </text>
          <view class="list__item__right">
            <text class="list__item__right__text">
              Go binding
            </text>
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item">
          <text class="list__item__title">
            Apple
          </text>
          <view class="list__item__right">
            <text class="list__item__right__text">
              Unbind
            </text>
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="openPopup('phone')">
          <text class="list__item__title">
            Mobile phone number
          </text>
          <view class="list__item__right">
            <text class="list__item__right__text">
              Change binding
            </text>
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
        <view class="list__item" @tap="openPopup('email')">
          <text class="list__item__title">
            Email
          </text>
          <view class="list__item__right">
            <text class="list__item__right__text">
              Change binding
            </text>
            <zui-svg-icon icon="arrow-right2-light" width="38rpx" height="38rpx" />
          </view>
        </view>
      </view>
    </view>
    <change-info-popup
      v-model="show"
      :type="openType"
      @submit="goChange"
    />
  </page>
</template>

<style scoped lang='scss'>
@import './account-security.scss'
</style>
