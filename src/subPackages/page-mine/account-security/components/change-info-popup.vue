<script setup lang="ts">
withDefaults(defineProps<{
  type?: string
}>(), {
  type: '',
})

const emits = defineEmits(['submit'])

const show = defineModel<boolean>('modelValue', { default: false })

function submit() {
  emits('submit')
}
</script>

<template>
  <view>
    <base-popup v-model="show" is-footer height="500rpx">
      <view class="wrapper">
        <text class="wrapper__label">
          {{ type === 'phone' ? 'Currently bound mobile phone number:' : 'The currently bound email address:' }}
        </text>
        <text class="wrapper__phone">
          {{ type === 'phone' ? '132****1616' : '<EMAIL>' }}
        </text>
      </view>

      <template #footer>
        <view class="wrapper__footer" @tap="submit">
          <text class="wrapper__footer__text">
            {{ type === 'phone' ? 'Change the mobile phone number' : 'Change email address' }}
          </text>
        </view>
      </template>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './change-info-popup.scss'
</style>
