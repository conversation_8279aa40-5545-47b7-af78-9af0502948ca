page{
    height: 100vh;
}
.wrapper{
    width: 750rpx;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);  /* 兼容旧 iOS */
    padding-bottom: env(safe-area-inset-bottom);
    background-image: url('/static/login/login.png');
    background-size: cover;
    background-repeat: no-repeat;

    &__main{
        padding: 32rpx 24rpx 48rpx 24rpx;
        background-color: #fff;
        border-radius: 24rpx;
        position: fixed;
        width: 680rpx;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);

        &__top{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 80rpx;

            &__logo{
                image{
                    width: 296rpx;
                    height: 100rpx;

                }
            }

            &__title{
                font-size: 48rpx;
                font-weight: 500;
                line-height: 64rpx;
                margin-top: 24rpx;
            }
        }

        &__cont{
            :deep(.wd-segmented){
                background-color: #EDF1F6;
                padding: 24rpx;
                border-radius: 24rpx;

                .wd-segmented__item{
                    height: 70rpx;
                    line-height: 70rpx;

                    &.is-active{
                        .wd-segmented__item-label{
                            color: #fff;
                        }
                    }
                }

                .wd-segmented__item-label{
                    font-size: 28rpx;
                    color: #103A62;
                    font-weight: 500;
                }

                .wd-segmented__item--active{
                    background-color: #103A62;
                    height: 70rpx;
                    border-radius: 20rpx;
                }
            }

            &__agreement{
                margin: 48rpx 0;
                display: flex;
                
                &__text{
                    color: #4B5353;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    font-weight: 400;
                    letter-spacing: 0.15px;
                    margin-left: 20rpx;

                    .blue{
                        color: #103A62;
                        text-decoration-line: underline;
                    }
                }
            }

            &__other{
                &__title{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 48rpx;

                    &__line{
                        width: 30%;
                        height: 1px;
                        background-color: #F5F6F6;
                    }

                    &__text{
                        font-size: 28rpx;
                        font-weight: 400;
                        line-height: 40rpx;
                        letter-spacing: 0.15px;
                        color: #4B5353;
                        margin: 0 16rpx;
                    }
                }

                &__btn{
                    margin-bottom: 16rpx;
                    border: 3rpx solid #F5F6F6;
                    border-radius: 32rpx;
                    padding: 24rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    &__text{
                        color: #4B5353;
                        font-size: 28rpx;
                        font-weight: 500;
                        line-height: 48rpx;
                        margin-left: 16rpx;
                    }

                    &:last-child{
                        margin-bottom: 0;
                    }
                }
            }
        }

        
    }
}