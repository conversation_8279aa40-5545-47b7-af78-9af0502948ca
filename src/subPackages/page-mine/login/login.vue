<script setup lang="ts">
import type { Component } from 'vue'
import type { LoginParamsType } from '@/model/login'
import { computed, ref, shallowRef } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'
import { LoginUtilsProvider } from '@/utils/login'
import { goBack, gotoPage } from '@/utils/router'
import emailLogin from './components/email-login/email-login.vue'
import phoneLogin from './components/phone-login/phone-login.vue'

const userStore = useUserStore()
const configStore = useConfigStore()
const fromMap = shallowRef<Record<string, Component>>({
  emailLogin,
  phoneLogin,
})

const list = ref<string[]>(['Phone number', 'Email'])
const current = ref<string>('Phone number')

const currentComponents = computed<string>(() => {
  return current.value === 'Phone number' ? 'phoneLogin' : 'emailLogin'
})

const isAgree = ref<boolean>(false)

async function handleLogin(e: 'phone' | 'email', model: LoginParamsType) {
  try {
    if (!isAgree.value) {
      return uni.showToast({ title: 'Please check the agreement', icon: 'none' })
    }
    e === 'phone' ? await userStore.phoneLogin(model) : await userStore.emailLogin(model)
    goBack()
  }
  catch (error) {
    console.error(error)
  }
  finally {
    uni.hideLoading()
  }
}

/*
  三方登录
*/
async function appLogin(provider: LoginUtilsProvider) {
  try {
    uni.showLoading({
      title: 'Logging in',
      mask: true,
    })
    if (!isAgree.value) {
      return uni.showToast({ title: 'Please check the agreement', icon: 'none' })
    }
    await userStore.appLogin(provider)
    goBack()
  }
  catch (error) {
    console.error(error)
    uni.showToast({
      title: 'Login Fail',
      icon: 'none',
    })
  }
  finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <view class="wrapper">
    <view class="wrapper__main">
      <!-- 顶部 -->
      <view class="wrapper__main__top">
        <view class="wrapper__main__top__logo">
          <image src="/static/login/login-top.png" mode="aspectFit" />
        </view>
        <view class="wrapper__main__top__title">
          Sign in
        </view>
      </view>
      <!-- 主要内容 -->
      <view class="wrapper__main__cont">
        <view class="wrapper__main__cont__tabs">
          <wd-segmented v-model:value="current" :options="list" />
        </view>
        <!-- 登陆表单 -->
        <component :is="fromMap[currentComponents]" @login="handleLogin" />
        <!-- 协议 -->
        <view class="wrapper__main__cont__agreement">
          <wd-checkbox v-model="isAgree" checked-color="#103A62" />
          <view class="wrapper__main__cont__agreement__text">
            <text>Make sure to check the</text>
            <text class="blue" @click="gotoPage(`/subPackages/page-home/notice/index?type=generalprotocol&name=user_agreement`)">
              "User Policy"
            </text>
            <text>and</text>
            <text class="blue" @click="gotoPage(`/subPackages/page-home/notice/index?type=generalprotocol&name=privacy_policy`)">
              "Privacy Agreement"
            </text>
          </view>
        </view>
        <!-- 其他登陆方式 -->
        <view class="wrapper__main__cont__other">
          <view class="wrapper__main__cont__other__title">
            <view class="wrapper__main__cont__other__title__line" />
            <view class="wrapper__main__cont__other__title__text">
              or Login with
            </view>
            <view class="wrapper__main__cont__other__title__line" />
          </view>
          <view
            v-if="[5, 6].includes(configStore.terminal as number)"
            class="wrapper__main__cont__other__btn"
            @click.stop="appLogin(LoginUtilsProvider.Google)"
          >
            <zui-svg-icon icon="google" width="48rpx" height="48rpx" />
            <view class="wrapper__main__cont__other__btn__text">
              Continue with Google
            </view>
          </view>
          <view
            v-if="configStore.terminal === 5"
            class="wrapper__main__cont__other__btn"
            @click="appLogin(LoginUtilsProvider.Apple)"
          >
            <zui-svg-icon icon="apple" width="48rpx" height="48rpx" />
            <view class="wrapper__main__cont__other__btn__text">
              Continue with Apple
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang='scss'>
@import './login.scss'
</style>
