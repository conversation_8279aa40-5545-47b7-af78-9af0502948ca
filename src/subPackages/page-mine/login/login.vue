<script setup lang="ts">
import type { Component } from 'vue'
import type { LoginParamsType } from '@/model/login'
import { computed, ref, shallowRef } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'
import { goBack } from '@/utils/router'
import emailLogin from './components/email-login/email-login.vue'
import phoneLogin from './components/phone-login/phone-login.vue'

const userStore = useUserStore()
const configStore = useConfigStore()
const fromMap = shallowRef<Record<string, Component>>({
  emailLogin,
  phoneLogin,
})

const list = ref<string[]>(['Phone number', 'Email'])
const current = ref<string>('Phone number')

const currentComponents = computed<string>(() => {
  return current.value === 'Phone number' ? 'phoneLogin' : 'emailLogin'
})

const isAgree = ref<boolean>(false)

async function handleLogin(e: 'phone' | 'email', model: LoginParamsType) {
  if (!isAgree.value) {
    return uni.showToast({ title: 'Please check the agreement', icon: 'none' })
  }
  e === 'phone' ? await userStore.phoneLogin(model) : await userStore.emailLogin(model)
  goBack()
}
</script>

<template>
  <view class="wrapper">
    <view class="wrapper__main">
      <!-- 顶部 -->
      <view class="wrapper__main__top">
        <view class="wrapper__main__top__logo">
          <image src="/static/login/login-top.png" mode="aspectFit" />
        </view>
        <view class="wrapper__main__top__title">
          Sign in
        </view>
      </view>
      <!-- 主要内容 -->
      <view class="wrapper__main__cont">
        <view class="wrapper__main__cont__tabs">
          <wd-segmented v-model:value="current" :options="list" />
        </view>
        <!-- 登陆表单 -->
        <component :is="fromMap[currentComponents]" @login="handleLogin" />
        <!-- 协议 -->
        <view class="wrapper__main__cont__agreement">
          <wd-checkbox v-model="isAgree" checked-color="#103A62" />
          <view class="wrapper__main__cont__agreement__text">
            <text>Make sure to check the</text>
            <text class="blue">
              "User Policy"
            </text>
            <text>and</text>
            <text class="blue">
              "Privacy Agreement"
            </text>
          </view>
        </view>
        <!-- 其他登陆方式 -->
        <view class="wrapper__main__cont__other">
          <view class="wrapper__main__cont__other__title">
            <view class="wrapper__main__cont__other__title__line" />
            <view class="wrapper__main__cont__other__title__text">
              or Login with
            </view>
            <view class="wrapper__main__cont__other__title__line" />
          </view>
          <view class="wrapper__main__cont__other__btn">
            <zui-svg-icon icon="google" width="48rpx" height="48rpx" />
            <view class="wrapper__main__cont__other__btn__text">
              Continue with Google
            </view>
          </view>
          <view v-if="[3, 5].includes(configStore.terminal as number)" class="wrapper__main__cont__other__btn">
            <zui-svg-icon icon="apple" width="48rpx" height="48rpx" />
            <view class="wrapper__main__cont__other__btn__text">
              Continue with Apple
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang='scss'>
@import './login.scss'
</style>
