<script setup lang="ts">
import type { LoginParamsType } from '@/model/login'
import { ref } from 'vue'
import { useConfigStore } from '@/stores/config'

const emit = defineEmits<{
  (e: 'login', type: 'phone' | 'email', model: LoginParamsType): void
}>()
const model = ref<LoginParamsType>({
  scene: 2,
  account: '',
  terminal: null,
  code: '',
})

const phoneFrom = ref<any>(null)

function handleSubmit() {
  phoneFrom.value.validate().then(({ valid }) => {
    if (valid) {
      model.value.terminal = useConfigStore().terminal
      emit('login', 'phone', model.value)
    }
  }).catch((error: any) => {
    console.error(error)
  })
}
</script>

<template>
  <view class="form">
    <wd-form ref="phoneFrom" :model="model" error-type="toast">
      <wd-cell-group>
        <wd-input
          v-model="model.account"
          prop="account"
          no-border
          placeholder="Enter your phone number"
          :rules="[{ required: true, message: 'Enter your phone number' }]"
        />
        <wd-input
          v-model="model.code"
          prop="code"
          no-border
          placeholder="Enter your OTP"
          :rules="[{ required: true, message: 'Enter your OTP' }]"
        >
          <template #suffix>
            <send-code-btn
              code-type="phone"
              :account="model.account"
              scene="YZMDL"
              rules="\S+"
            />
          </template>
        </wd-input>
      </wd-cell-group>
      <view class="form__footer">
        <wd-button custom-class="form__footer__btn" block @click="handleSubmit">
          Login
        </wd-button>
      </view>
    </wd-form>
  </view>
</template>

<style scoped lang="scss">
@import '../form.scss'
</style>
