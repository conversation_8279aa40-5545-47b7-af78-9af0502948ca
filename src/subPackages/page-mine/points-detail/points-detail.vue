<script setup lang="ts">
import type { CalendarInstance } from 'wot-design-uni/components/wd-calendar/types'
import type { PointDetailType } from '@/model/mine'
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { dayjs } from 'wot-design-uni'
import { ApiPointDetail } from '@/api/user'
import { useList } from '@/hooks/useList'

const month = ref(Date.now())
const calendar = ref<CalendarInstance>()

const list = useList<PointDetailType>(
  ApiPointDetail,
  {
    page_no: 1,
    page_size: 10,
  },
)
onLoad(() => {
  list.fetchList()
})

onPullDownRefresh(() => {
  list.refresh()
  uni.stopPullDownRefresh()
})

onReachBottom(() => {
  list.loadMore()
})

// 处理数据
const listData = computed(() => {
  const grouped: { date: string, list: PointDetailType[] }[] = Object.values(
    list.list.value.reduce((acc, item) => {
      if (!acc[item.create_date]) {
        acc[item.create_date] = { date: item.create_date, list: [] }
      }
      acc[item.create_date].list.push({ ...item, create_time: dayjs(item.create_time).format('YYYY.MM/DD  HH:mm') })
      return acc
    }, {} as Record<string, { date: string, list: PointDetailType[] }>),
  )
  return grouped.length
    ? grouped
    : [
        {
          date: dayjs().format('YYYY/MM'),
          list: [],
        },
      ]
})

// 打开时间选择器弹窗
function openCalendar() {
  calendar.value?.open()
}
// 确认选择
function handleConfirm({ value }: { value: number }) {
  list.setParams({
    create_time_start: dayjs(value).startOf('month').format('YYYY-MM-DD'),
    create_time_end: dayjs(value).endOf('month').format('YYYY-MM-DD'),
  })
}
</script>

<template>
  <page>
    <nav-bar-page :title="$t('mine.points.PointsDetails')" />
    <wd-calendar ref="calendar" v-model="month" :with-cell="false" type="month" @confirm="handleConfirm" />

    <view class="body">
      <view v-for="(item, index) in listData" :key="index" class="module">
        <view class="module__title">
          <text class="module__title__text">
            {{ item.date }}
          </text>
          <zui-svg-icon
            v-if="index === 0" icon="calendar-light" width="40rpx" height="40rpx" color="#103A62" @tap="openCalendar"
          />
        </view>
        <view v-for="(iitem, idx) in item.list" :key="idx" class="module__item">
          <view class="module__item__top">
            <text class="module__item__top__title">
              {{ iitem.title }}
            </text>
            <text class="module__item__top__text">
              {{ iitem.action === 1 ? '+' : '-' }} {{ iitem.change_amount }}
            </text>
          </view>
          <view class="module__item__bottom">
            <text class="module__item__bottom__text">
              {{ iitem.create_time }}
            </text>
            <text class="module__item__bottom__text">
              {{ iitem.type_remark }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './points-detail.scss'
</style>
