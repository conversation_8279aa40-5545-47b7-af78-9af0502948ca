.module{
padding: 0 32rpx;

    &__title{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 32rpx;
        
        &__text{
            font-size: 32rpx;
            font-weight: 400;
            font-family: Alimama FangYuanTi VF;
            color: #1D232E;
        }
    }

    &__item{
        margin-bottom: 32rpx;
        padding: 24rpx;
        background-color: #fff;
        border-radius: 24rpx;
        border-bottom: 1rpx solid #EAECF0;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
        
        &__top{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            
            &__title{
                color: #000;
                font-size: 32rpx;
                font-weight: 700;
                font-family: Alimama FangYuanTi VF;
            }
            
            &__text{
                color: #103A62;
                font-size: 32rpx;
                font-weight: 700;
                font-family: Alimama FangYuanTi VF;
            }
        }
        
        &__bottom{
            margin-top: 24rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            
            &__text{
                font-size: 24rpx;
                color: #717680;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
            }
        }
    }
}