<script setup lang="ts">
import type { PointCenterType, StoreListType } from '@/model/mine'
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiPointCenter, ApiStoreList } from '@/api/user'
import { useList } from '@/hooks/useList'
import { gotoPage } from '@/utils/router'
import pointsCard from './components/points-card/points-card.vue'

const info = ref<PointCenterType>({} as PointCenterType)

async function getInfo() {
  const res = await ApiPointCenter()
  info.value = res
}

const list = useList<StoreListType>(
  ApiStoreList,
  {
    page_no: 1,
    page_size: 10,
  },
)
onLoad(() => {
  list.fetchList()
  getInfo()
})

onPullDownRefresh(() => {
  list.refresh()
  getInfo()
  uni.stopPullDownRefresh()
})

onReachBottom(() => {
  list.loadMore()
})
</script>

<template>
  <page>
    <nav-bar-page :title="$t('mine.points.pageTitle')">
      <template #right>
        <zui-svg-icon
          icon="info-square-light"
          width="46rpx"
          height="46rpx"
          color="#1570EF"
          @tap="gotoPage('/subPackages/page-mine/guide-use/guide-use')"
        />
      </template>
    </nav-bar-page>

    <view class="body">
      <view class="wrapper">
        <!-- 积分 -->
        <view class="wrapper__pointer" @tap="gotoPage('/subPackages/page-mine/points-detail/points-detail')">
          <view class="wrapper__pointer__left">
            <view class="wrapper__pointer__left__top">
              <text class="wrapper__pointer__left__top__num fs--24--700">
                {{ info.integrate }}
              </text>
              <text class="wrapper__pointer__left__top__unit fs--14--700">
                {{ $t('mine.points.title') }}
              </text>
            </view>
            <text class="wrapper__pointer__left__conversion fs--14--400">
              1 {{ $t('mine.points.point_unit') }} = {{ info.integrate_rate }} {{ $t('mine.points.unit') }}
            </text>
          </view>
          <zui-svg-icon
            icon="arrow-right2-light"
            width="40rpx"
            height="40rpx"
            color="#5B5E68"
          />
        </view>
        <!-- 按钮 -->
        <view class="wrapper__btn">
          <view class="wrapper__btn__left" @tap="gotoPage('/subPackages/page-mine/coupon-package/coupon-package')">
            <text class="wrapper__btn__left__text fs--14--400">
              {{ $t('mine.points.vocherPackage') }}
            </text>
          </view>
          <view class="wrapper__btn__right" @tap="gotoPage('/subPackages/page-mine/earn-points/earn-points')">
            <text class="wrapper__btn__right__text fs--14--400">
              {{ $t('mine.points.earnPoints') }}
            </text>
          </view>
        </view>

        <!-- 列表标题 -->
        <view class="wrapper__title">
          <text class="wrapper__title__left fs--16--400">
            {{ $t('mine.points.redeemCoupons') }}
          </text>
          <text class="wrapper__title__right fs--14--400" @tap="gotoPage('/subPackages/page-mine/guide-use/guide-use')">
            {{ $t('mine.points.redeemCouponsUse') }}
          </text>
        </view>
        <!-- 列表 -->
        <view class="wrapper__list">
          <points-card v-for="(item) in list.list.value" :key="item.id" :item="item" />
          <!-- <view v-if="list.finished" />
          <view v-else /> -->
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-points.scss'
</style>
