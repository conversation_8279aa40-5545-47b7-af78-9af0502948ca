.card{
    margin-bottom: 32rpx;
    padding: 24rpx;
    background-color: #FAFAFA;
    border-radius: 24rpx;
    
    &__image{
        width: 100%;
        height: 200rpx;
        margin-bottom: 24rpx;
    }
    
    &__title{
        margin-bottom: 24rpx;
        color: #000;
        font-size: 32rpx;
        font-weight: 600;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    &__info{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin: 24rpx 0;
        
        &__right{
            padding-left: 12rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            flex: 1;
            line-height: 40rpx;
            font-size: 24rpx;
            font-weight: 600;
            color: #5D7D9D;
        }
    }

    &__btn{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        
        &__container{
            border-radius: 100rpx;
            width: 48%;
            background-color: #103A62;
            padding: 10rpx 0;
            text-align: center;
            &__text{
                color:#fff;
                text-align: center;
                line-height: 48rpx;
                font-size: 28rpx;
                font-weight: 500;
            }
        }
    }
}