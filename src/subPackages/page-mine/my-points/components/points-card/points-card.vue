<script lang="ts" setup>
import type { StoreListType } from '@/model/mine'
import { gotoPage } from '@/utils/router'

const props = defineProps<{
  item: StoreListType
}>()
</script>

<template>
  <view class="card">
    <image :src="props.item.image" class="card__image" mode="widthFix" />
    <text class="card__title">
      {{ props.item.name }}
    </text>
    <view class="card__info">
      <zui-svg-icon
        icon="location-light-blue"
        width="36rpx"
        height="36rpx"
        color="#5D7D9D"
      />
      <view class="card__info__right">
        <text>{{ props.item.type_text }}</text>
        <text> 丨 </text>
        <text>{{ props.item.business_time_start }} - {{ props.item.business_time_end }}</text>
      </view>
    </view>
    <view class="card__btn">
      <view class="card__btn__container">
        <text class="card__btn__container__text" @tap="gotoPage(`/subPackages/page-mine/store-detail/store-detail?id=${props.item.id}`)">
          Store details
        </text>
      </view>
      <view class="card__btn__container">
        <text class="card__btn__container__text" @tap="gotoPage(`/subPackages/page-mine/redeem-coupons/redeem-coupons?id=${props.item.id}`)">
          Redeem coupons
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './points-card.scss'
</style>
