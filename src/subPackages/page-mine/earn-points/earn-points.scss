.list {
    padding: 0 32rpx;

    &__item {
        padding: 24rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx #EAECF0 solid;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
        background-color: #fff;
        margin-bottom: 32rpx;
        border-radius: 24rpx;

        &__left {
            width: 400rpx;

            &__text {
                color: #717680;
                font-size: 28rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF !important;
            }
        }

        &__right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-left: 32rpx;
            flex: 1;

            &__top {
                margin-bottom: 16rpx;

                &__text {
                    color: #D92D20;
                    font-size: 26rpx;
                    font-weight: 400;
                    font-family: Alimama FangYuanTi VF !important;
                }
            }

            &__btn {
                background-color: #103A62;
                border-radius: 32rpx;
                display: flex;
                align-items: center;
                justify-content: center;

                &__text {
                    line-height: 48rpx;
                    color: #fff;
                    font-weight: 400;
                    font-family: <PERSON><PERSON><PERSON>uanTi VF !important;
                    font-size: 24rpx;
                    padding: 4rpx 24rpx;
                }
            }
        }
    }
}
