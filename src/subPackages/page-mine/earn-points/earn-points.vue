<script setup lang="ts">
import type { EarnPointsItemTyp } from '@/model/mine'
import { ref } from 'vue'

const list = ref<EarnPointsItemTyp[]>([
  { id: 1, title: 'Register as a member and complete the account binding', is_btn: true, point: 150 },
  { id: 2, title: 'For every 1NZD spent', is_btn: false, point: 1 },
  { id: 3, title: 'Purchase a package', is_btn: false, point: 200 },
  { id: 4, title: 'Successfully shared with friends', is_btn: true, point: 150 },
  { id: 5, title: 'My friend\'s first order', is_btn: false, point: 200 },
])
</script>

<template>
  <page>
    <nav-bar-page title="Earn points" />
    <view class="body">
      <view class="list">
        <view v-for="(item, index) in list" :key="index" class="list__item">
          <view class="list__item__left">
            <text class="list__item__left__text">
              {{ item.title }}
            </text>
          </view>
          <view class="list__item__right">
            <view class="list__item__right__top">
              <text class="list__item__right__top__text">
                + {{ item.point }} points
              </text>
            </view>
            <view v-if="item.is_btn" class="list__item__right__btn">
              <text class="list__item__right__btn__text">
                Go and get
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './earn-points.scss'
</style>
