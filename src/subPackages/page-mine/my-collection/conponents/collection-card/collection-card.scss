.card{
padding: 24rpx;
background-color: #fafafa;
border-radius: 24rpx;
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
margin-bottom: 32rpx;

&__left{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    
    &__img{
        width: 100rpx;
        height: 100rpx;
        border-radius: 10rpx;
        margin-right: 20rpx;
    }
    
    &__info{
        flex: 1;
        
        &__title{
            font-size: 26rpx;
            color: #000;
            font-weight: 700;
            font-family: Alimama FangYuanTi VF;
            width: 90%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        &__time{
            margin-top: 16rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            
            &__date{
                display: flex;
                flex-direction: row;
                align-items: center;
                
                &__text{
                    margin-left: 10rpx;
                    font-size: 24rpx;
                    color: #717680;
                    font-weight: 400;
                }
                
                &.margin{
                    margin-left: 20rpx;
                }
            }
        }
    }
}
}