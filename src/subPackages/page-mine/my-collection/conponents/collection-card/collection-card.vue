<script lang="ts" setup>
</script>

<template>
  <view class="card">
    <view class="card__left">
      <image class="card__left__img" src="/static/test/guide-item.png" />
      <view class="card__left__info">
        <text class="card__left__info__title">
          OCT
        </text>
        <view class="card__left__info__time">
          <view class="card__left__info__time__date">
            <zui-svg-icon width="30rpx" height="30rpx" color="#717680" icon="time-circle-light" />
            <!-- <l-svg src="/static/icons/time-circle-light.svg" color="#717680" style="width: 30rpx; height: 30rpx;" /> -->
            <text class="card__left__info__time__date__text">
              07:08:43
            </text>
          </view>
          <view class="card__left__info__time__date margin">
            <zui-svg-icon width="30rpx" height="30rpx" color="#717680" icon="bag-light" />
            <text class="card__left__info__time__date__text">
              24 times
            </text>
          </view>
        </view>
      </view>
    </view>
    <zui-svg-icon width="48rpx" height="48rpx" color="#103A62" icon="heart" />
  </view>
</template>

<style lang="scss" scoped>
@import './collection-card.scss'
</style>
