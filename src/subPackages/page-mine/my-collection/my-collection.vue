<script setup lang="ts">
import collectionCard from './conponents/collection-card/collection-card.vue'
</script>

<template>
  <page>
    <nav-bar-page title="My collection" />
    <view class="body">
      <view class="container">
        <collection-card v-for="(item, index) in 10" :key="index" />
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
.container{
padding: 0 32rpx;

    &__inner{
        padding-top: 24rpx;
    }
}
</style>
