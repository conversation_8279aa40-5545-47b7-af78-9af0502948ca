<script lang="ts" setup>
const emits = defineEmits(['verify', 'change', 'refund'])

function onVerify() {
  emits('verify')
}

function onChange() {
  emits('change', 'change')
}

function onRefund() {
  emits('refund', 'refund')
}
</script>

<template>
  <view>
    <!-- 待使用车票 -->
    <ticket-style-card>
      <template #top>
        <view class="info">
          <view class="info__left">
            <text class="info__left__title fs--16--700">
              Ticket purchased
            </text>
            <text class="info__left__tag fs--10--400 blue">
              Used
            </text>
          </view>
          <view class="info__price">
            <text class="info__price__unit fs--14--700">
              NZ$
            </text>
            <text class="info__price__val fs--18--700">
              140.00
            </text>
          </view>
        </view>
        <view class="info__bottom">
          <text class="info__bottom__label fs--12--400">
            Passenger:
          </text>
          <text class="info__bottom__text fs--12--400">
            Lulise
          </text>
        </view>
      </template>
      <template #bottom>
        <view class="btn">
          <view class="btn__wrapper main" @tap="onVerify">
            <text class="btn__wrapper__text fs--14--700">
              Verify
            </text>
          </view>
          <view class="btn__wrapper blue" @tap="onChange">
            <text class="btn__wrapper__text fs--14--700">
              Change
            </text>
          </view>
          <view class="btn__wrapper red" @tap="onRefund">
            <text class="btn__wrapper__text fs--14--700">
              Refund
            </text>
          </view>
        </view>
      </template>
    </ticket-style-card>
  </view>
</template>

<style scoped lang="scss">
@import './pending-ticket-card.scss'
</style>
