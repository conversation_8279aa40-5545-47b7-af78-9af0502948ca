.info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;

    &__left {
        display: flex;
        align-items: center;
        flex-direction: row;

        &__title {
            line-height: 48rpx;
            color: #101010;
        }

        &__tag {
            padding: 8rpx 12rpx;
            border-radius: 100rpx;
            margin-left: 16rpx;

            &.blue {
                color: #fff;
                background-color: #0BA5EC;
            }
        }
    }

    &__price {
        display: flex;
        align-items: center;
        flex-direction: row;


        &__unit {
            color: #103A62;
        }

        &__val {
            color: #103A62;
            margin-left: 10rpx;
        }
    }

    &__bottom {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin-top: 32rpx;
    
        &__label {
            color: #878787;
            line-height: 32rpx;
        }
    
        &__text {
            color: #252B37;
            line-height: 32rpx;
        }
    }
}

.btn {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;

    &__wrapper {
        width: 190rpx;
        height: 56rpx;
        border-radius: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;

        &__text {
            color: #fff;
            line-height: 40rpx;
        }

        &.main {
            background-color: #103A62;
        }

        &.blue {
            background-color: #0086C9;
        }

        &.red {
            background-color: #B42318;
        }

    }
}