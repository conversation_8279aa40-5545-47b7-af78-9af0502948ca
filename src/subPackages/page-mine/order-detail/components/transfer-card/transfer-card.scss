.info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;

    &__left {
        display: flex;
        align-items: center;
        flex-direction: row;

        &__title {
            line-height: 48rpx;
            color: #101010;
        }
    }

    &__price {
        display: flex;
        align-items: center;
        flex-direction: row;

        &__unit {
            color: #103A62;
        }

        &__val {
            color: #103A62;
            margin-left: 10rpx;
        }
    }
}

.bottom {
    &__cancel {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        background-color: #91A8BE;
        border-radius: 100rpx;

        &__text {
            color: #fff;
            padding: 8rpx 24rpx;
            line-height: 40rpx;
        }
    }

    &__used{
        line-height: 32rpx;
        color: #2E90FA;
    }
}