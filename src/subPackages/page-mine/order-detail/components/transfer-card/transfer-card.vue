<script lang="ts" setup>
import { ApiCancelOrder } from '@/api/order'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

async function cancelOrder() {
  try {
    uni.showModal({
      title: 'Kind Reminder',
      content: 'Are you sure you want to cancel this order?',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      success: async (res: any) => {
        if (res.confirm) {
          await ApiCancelOrder({ order_id: props.data.id })
          uni.showToast({
            title: 'Cancel success',
            icon: 'success',
          })
        }
      },
    })
    // await ApiCancelOrder({ order_id: props.data.id })
    // uni.showToast({
    //   title: 'Cancel success',
    //   icon: 'success',
    // })
  }
  catch (error) {
    console.error(error)
  }
}
</script>

<template>
  <!-- 专车卡片 -->
  <ticket-style-card>
    <template #top>
      <view class="info">
        <view class="info__left">
          <text class="info__left__title fs--16--700">
            {{ props.data.status_text }}
          </text>
        </view>
        <view class="info__price">
          <text class="info__price__unit fs--14--700">
            NZD
          </text>
          <text class="info__price__val fs--18--700">
            {{ props.data.order_amount }}
          </text>
        </view>
      </view>
    </template>
    <template #bottom>
      <view v-if="props.data.status === 1" class="bottom">
        <!-- 取消按钮 -->
        <view class="bottom__cancel" @tap="cancelOrder">
          <text class="bottom__cancel__text fs--14--700">
            Cancel
          </text>
        </view>
      </view>
      <!-- 已使用提示文字 -->
      <text v-else-if="props.data.status === 2" class="bottom__used fs--12--700">
        Driver assigned — Trip cannot be cancelled.Contact support if issues.
      </text>
    </template>
  </ticket-style-card>
</template>

<style scoped lang="scss">
@import './transfer-card.scss'
</style>
