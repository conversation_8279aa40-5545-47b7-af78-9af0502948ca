<script lang="ts" setup>
</script>

<template>
  <!-- 专车卡片 -->
  <ticket-style-card>
    <template #top>
      <view class="info">
        <view class="info__left">
          <text class="info__left__title fs--16--700">
            Processing
          </text>
        </view>
        <view class="info__price">
          <text class="info__price__unit fs--14--700">
            NZ$
          </text>
          <text class="info__price__val fs--18--700">
            140.00
          </text>
        </view>
      </view>
    </template>
    <template #bottom>
      <view class="bottom">
        <!-- 取消按钮 -->
        <view class="bottom__cancel">
          <text class="bottom__cancel__text fs--14--700">
            Cancel
          </text>
        </view>
        <!-- 已使用提示文字 -->
        <!-- <text class="bottom__used fs--12--700">
          Driver assigned — Trip cannot be cancelled.Contact support if issues.
        </text> -->
      </view>
    </template>
  </ticket-style-card>
</template>

<style scoped lang="scss">
@import './transfer-card.scss'
</style>
