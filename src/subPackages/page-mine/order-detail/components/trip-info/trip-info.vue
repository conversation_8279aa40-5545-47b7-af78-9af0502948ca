<script setup lang="ts">
import { ref } from 'vue'
import { dayjs } from 'wot-design-uni'
import tripInfoPopup from '../trip-info-popup/trip-info-popup.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },

})

const show = ref<boolean>(false)
</script>

<template>
  <view class="trip">
    <view class="trip__title fs--16--400">
      Itinerary information
    </view>
    <view class="trip__list">
      <view class="timeline">
        <!-- 整体竖线 -->
        <view class="line" />
        <!-- 出发站 -->
        <view class="timeline-item">
          <view class="timeline-item-dot" />
          <view class="timeline-item-content">
            <view class="timeline-item-content-header">
              <view class="timeline-item-content-header-index">
                1
              </view>
              <view class="timeline-item-content-header-title">
                {{ props.data.from_name }}
              </view>
            </view>
            <view class="timeline-item-content-time">
              {{ dayjs(props.data.travel_time).format('YYYY/MM/DD HH:mm') }} Departure
            </view>
          </view>
        </view>

        <!-- 中间信息 -->
        <view v-if="props.data.type !== 'pickup_service'" class="timeline-middle" @tap="show = true">
          Three stops, 21 minutes
        </view>
        <view v-else class="timeline-middle">
          Ride for {{ props.data.distance }}
        </view>

        <!-- 到达站 -->
        <view class="timeline-item">
          <view class="timeline-item-dot" />
          <view class="timeline-item-content">
            <view class="timeline-item-content-header">
              <view class="timeline-item-content-header-index">
                2
              </view>
              <view class="timeline-item-content-header-title">
                {{ props.data.to_name }}
              </view>
            </view>
            <!-- <view class="timeline-item-content-time">
              2025/07/10 14:00 Departure
            </view> -->
          </view>
        </view>
      </view>
    </view>

    <trip-info-popup v-model="show" />
  </view>
</template>

<style scoped lang="scss">
@import './trip-info.scss'
</style>
