.trip {
    &__title {
        color: #1D232E;
        margin-bottom: 32rpx;
    }

    &__list{

    }

    .timeline {
        position: relative;
        padding-left: 60rpx;
        /* 整体竖线 */
        .line {
            position: absolute;
            left: 20rpx;
            top: 0;
            bottom: 0;
            width: 6rpx;
            border-radius: 100rpx;
            background-color: #D5D7DA;
        }

        &-item{
            position: relative;
            margin-bottom: 32rpx;

            &-dot{
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                left: -45rpx;
                width: 14rpx;
                height: 14rpx;
                background: #000;
                border-radius: 50%;
            }

            &-content {
                background: #FAFAFA;
                border-radius: 24rpx;
                padding: 24rpx;

                &-header{
                    display: flex;
                    align-items: center;
                    margin-bottom: 24rpx;

                        &-index {
                            width: 40rpx;
                            height: 40rpx;
                            background: #103A62;
                            color: #fff;
                            border-radius: 50%;
                            text-align: center;
                            line-height: 40rpx;
                            margin-right: 20rpx;
                            font-size: 22rpx;
                            font-weight: 600;
                        }

                        &-title {
                            font-size: 24rpx;
                            font-weight: 600;
                            color: #000;
                        }
                }

                &-time{
                    color: #717680;
                    font-size: 24rpx;
                    font-weight: 400;
                }
            }
        }

        &-middle{
            padding: 18rpx 24rpx;
            border: 1px solid #D5D7DA;
            border-radius: 20rpx;
            color: #161616;
            font-size: 26rpx;
            font-weight: 400;
            margin-bottom: 32rpx;
            width: 400rpx;
            text-align: center;
        }
    }



}