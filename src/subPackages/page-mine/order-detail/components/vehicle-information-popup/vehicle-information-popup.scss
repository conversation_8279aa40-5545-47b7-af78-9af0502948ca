.wrapper {

    &__top {
        margin: 32rpx 0;
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;

        &__item {
            background-color: #ACBECF;
            padding: 12rpx 24rpx;
            border-radius: 100rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-right: 32rpx;

            &__text {
                color: #fff;
            }

            &.active {
                background-color: #103A62;
            }
        }
    }

    &__info {
        margin-bottom: 32rpx;
        padding: 24rpx;
        border-radius: 20rpx;
        background-color: #EDF1F6;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        &__left {
            flex: 1;
            margin-right: 16rpx;

            &__title {
                font-size: 32rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
                color: #103A62;
            }

            &__desc {
                font-size: 28rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
                color: #A4A7AE;
                margin-top: 16rpx;
            }
        }
    }

    &__desc {
        line-height: 52rpx;
        color: #A4A7AE;
        margin-bottom: 32rpx;
    }

    &__img {
        width: 100%;
        height: 350rpx;

        image {
            width: 100%;
            height: 100%;
        }
    }
}