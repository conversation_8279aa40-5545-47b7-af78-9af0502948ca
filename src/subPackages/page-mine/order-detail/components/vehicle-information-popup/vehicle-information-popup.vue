<script lang="ts" setup>
// import type { BaseSwiperItemType } from '@/model/common'
import type { IOrderDetail } from '@/model/order'

const props = defineProps<{
  data?: IOrderDetail['coachdriver']
}>()

const show = defineModel<boolean>('modelValue', { default: false })
</script>

<template>
  <view>
    <!-- 车辆信息 -->
    <base-popup v-model="show" title="License plate number" height="820rpx">
      <view class="wrapper">
        <view class="wrapper__top">
          <view class="wrapper__top__item active">
            <text class="wrapper__top__item__text fs--12--400">
              {{ props.data?.car_number }}
            </text>
          </view>
        </view>

        <view class="wrapper__desc">
          A {{ props.data?.car_color }} {{ props.data?.car_type }} with a capacity of {{ props.data?.max_passengers }} people
        </view>

        <view class="wrapper__img">
          <image :src="props.data?.car_image" mode="aspectFill" />
        </view>
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './vehicle-information-popup.scss'
</style>
