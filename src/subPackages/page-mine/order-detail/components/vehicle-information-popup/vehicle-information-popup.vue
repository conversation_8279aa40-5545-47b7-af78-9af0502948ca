<script lang="ts" setup>
import type { BaseSwiperItemType } from '@/model/common'
import { ref } from 'vue'

const show = defineModel<boolean>('modelValue', { default: false })

const swiperList = ref<BaseSwiperItemType[]>([
  { id: 1, url: '/static/test/guide-detail.png' },
  { id: 2, url: '/static/test/guide-detail.png' },
])
</script>

<template>
  <view>
    <!-- 车辆信息 -->
    <base-popup v-model="show" title="License plate number" height="820rpx">
      <view class="wrapper">
        <view class="wrapper__top">
          <view class="wrapper__top__item active">
            <text class="wrapper__top__item__text fs--12--400">
              D3534
            </text>
          </view>
          <view class="wrapper__top__item">
            <text class="wrapper__top__item__text fs--12--400">
              D3534
            </text>
          </view>
        </view>
        <view class="wrapper__info">
          <view class="wrapper__info__left">
            <view class="wrapper__info__left__title">
              Shenzhen N.
            </view>
            <view class="wrapper__info__left__desc">
              No. 92, Meilong Road, Minzhi Sub-district, Longhua District, Shenzhen City
            </view>
          </view>
          <zui-svg-icon icon="send-light" color="#103A62" width="40rpx" height="40rpx" />
        </view>
        <base-swiper :list="swiperList" height="350rpx" border-radius="24rpx" />
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './vehicle-information-popup.scss'
</style>
