<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    desc?: string
  }>(),
  {
    desc: 'The vehicle is expected to arrive at the pick-up point in 3 minutes. Please get ready',
  },
)
</script>

<template>
  <view class="tips">
    <zui-svg-icon width="48rpx" height="48rpx" icon="notification" color="#103A62" />
    <text class="tips__content fs--12--700">
      {{ props.desc }}
    </text>
  </view>
</template>

<style scoped lang="scss">
@import './pending-tips.scss'
</style>
