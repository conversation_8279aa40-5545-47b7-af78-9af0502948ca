.card__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;

    &__left {
        display: flex;
        align-items: center;
        flex-direction: row;

        &__title {
            line-height: 48rpx;
            color: #101010;
        }

        &__tag {
            padding: 8rpx 12rpx;
            border-radius: 100rpx;
            margin-left: 16rpx;

            &.green {
                color: #fff;
                background-color: #17B26A;
            }

            &.grey {
                background-color: #D5D7DA;
                color: #414651;
            }

            &.unpay {
                background-color: #91A8BE;
                color: #fff;
            }
        }
    }

    &__right {
        display: flex;
        align-items: center;
        flex-direction: row;


        &__unit {
            color: #103A62;
        }

        &__val {
            color: #103A62;
            margin-left: 10rpx;
        }
    }
}

.card__bottom {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin-top: 32rpx;

    &__label {
        color: #878787;
        line-height: 32rpx;
    }

    &__text {
        color: #252B37;
        line-height: 32rpx;
    }
}