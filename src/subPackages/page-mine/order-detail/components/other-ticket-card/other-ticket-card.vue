<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
})
const statusClass = computed(() => {
  switch (props.data.status) {
    case 1:
      return 'green'
    case 2:
      return 'grey'
    case 3:
      return 'unpay'
    default:
      return 'green'
  }
})
</script>

<template>
  <!-- 其他状态车票卡片 -->
  <ticket-style-card type="default">
    <view class="card__top">
      <view class="card__top__left">
        <view class="card__top__left__title fs--16--700">
          Ticket purchased
        </view>
        <view class="card__top__left__tag fs--10--400" :class="statusClass">
          <!-- Used -->
          {{ props.data.status_text }}
        </view>
        <!-- <view class="card__top__left__tag fs--10--400 grey">Cancelled</view> -->
        <!-- <view class="card__top__left__tag fs--10--400 unpay">Unpaid</view> -->
      </view>
      <view class="card__top__right">
        <view class="card__top__right__unit fs--14--700">
          NZ$
        </view>
        <view class="card__top__right__val fs--18--700">
          {{ props.data.order_amount }}
        </view>
      </view>
    </view>

    <view v-if="props.type !== 'pickup_service'" class="card__bottom">
      <view class="card__bottom__label fs--12--400">
        Passenger:
      </view>
      <view class="card__bottom__text fs--12--400">
        Lulise
      </view>
    </view>
  </ticket-style-card>
</template>

<style scoped lang="scss">
@import './other-ticket-card.scss'
</style>
