<script setup lang="ts">
import type { IOrderDetail } from '@/model/order'
import { ref } from 'vue'
import vehicleInformationPopup from '../vehicle-information-popup/vehicle-information-popup.vue'

const props = defineProps({
  data: {
    type: Object as () => IOrderDetail['coachdriver'],
    default: () => ({}),
  },
  type: {
    type: String,
    default: '',
  },
})

const isOpen = ref<boolean>(false)

function openInfo() {
  isOpen.value = true
}

function callDriver() {
  uni.makePhoneCall({
    phoneNumber: props.data?.driver_phone,
  })
}
</script>

<template>
  <view class="cart">
    <view class="cart__title fs--16--400">
      Vehicle information
    </view>

    <view class="cart__list">
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Vehicle type
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            {{ props.data?.car_type }}
          </text>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Body color
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            {{ props.data?.car_color }}
          </text>
        </view>
      </view>
      <view class="cart__list__item" @click="openInfo">
        <text class="cart__list__item__label fs--12--400">
          License plate number
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            {{ props.data?.car_number }}
          </text>
          <view style="margin-left: 6rpx;">
            <zui-svg-icon width="28rpx" height="28rpx" icon="arrow-right2-light" />
          </view>
        </view>
      </view>

      <template v-if="props.type === 'pickup_service'">
        <view class="cart__list__item">
          <text class="cart__list__item__label fs--12--400">
            Driver's name
          </text>
          <view class="cart__list__item__right">
            <text class="fs--12--400">
              {{ props.data?.driver_name }}
            </text>
          </view>
        </view>
        <view class="cart__list__item">
          <text class="cart__list__item__label fs--12--400">
            Driver's phone number
          </text>
          <view class="cart__list__item__right" @tap="callDriver">
            <text class="fs--12--400">
              {{ props.data?.driver_phone }}
            </text>
            <view style="margin-left: 6rpx;">
              <zui-svg-icon width="28rpx" height="28rpx" icon="call-light" />
            </view>
          </view>
        </view>
      </template>
      <view v-else class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Operation route
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            ZQN-DUN-CHC
          </text>
        </view>
      </view>
    </view>

    <!-- 车辆信息弹窗 -->
    <vehicle-information-popup v-model="isOpen" :data="props.data" />
  </view>
</template>

<style scoped lang="scss">
@import './vehicle-information.scss'
</style>
