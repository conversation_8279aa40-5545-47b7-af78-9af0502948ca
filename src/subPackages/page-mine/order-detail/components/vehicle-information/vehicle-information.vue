<script setup lang="ts">
import { ref } from 'vue'
import vehicleInformationPopup from '../vehicle-information-popup/vehicle-information-popup.vue'

const isOpen = ref<boolean>(false)

function openInfo() {
  isOpen.value = true
}
</script>

<template>
  <view class="cart">
    <view class="cart__title fs--16--400">
      Vehicle information
    </view>

    <view class="cart__list">
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Vehicle type
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            Large bus
          </text>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Body color
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            White
          </text>
        </view>
      </view>
      <view class="cart__list__item" @click="openInfo">
        <text class="cart__list__item__label fs--12--400">
          License plate number
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            D3534
          </text>
          <view style="margin-left: 20rpx;">
            <zui-svg-icon width="38rpx" height="38rpx" icon="arrow-right2-light" />
          </view>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Operation route
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            ZQN-DUN-CHC
          </text>
        </view>
      </view>
    </view>

    <!-- 车辆信息弹窗 -->
    <vehicle-information-popup v-model="isOpen" />
  </view>
</template>

<style scoped lang="scss">
@import './vehicle-information.scss'
</style>
