<script lang="ts" setup>
withDefaults(defineProps<{
  type?: string
}>(), {
  type: 'change',
})

const emits = defineEmits(['continue'])

const show = defineModel<boolean>('modelValue', { default: false })

const str: string = 'Ticket Change Policy ⏰ Change Deadline Changes must be made at least 48 hours before departure.Late requests subject to seat availability.💳 Change FeesFree changes if made 7+ days in advance.$30 fee for changes within 3-7 days.$50 fee + fare difference within 48 hours.🔄 How to ChangeOnline: Visit "Manage Booking" on our website/app.On-site: Visit ticket counters at least 4 hours pre-departure.'

function close() {
  show.value = false
}

function submit() {
  emits('continue')
}
</script>

<template>
  <view>
    <base-popup v-model="show" is-footer title="Ticket Change Policy" height="900rpx">
      <view class="content">
        <mp-html :content="str" />
      </view>
      <template #footer>
        <view class="content__footer">
          <view class="content__footer__btn left" @tap.stop="close">
            <text class="fs--14--400 content__footer__btn__text">
              Cancel
            </text>
          </view>
          <view class="content__footer__btn right" @tap.stop="submit">
            <text class="fs--14--400 content__footer__btn__text">
              Continue
            </text>
          </view>
        </view>
      </template>
    </base-popup>
  </view>
</template>

<style lang="scss" scoped>
@import './operation-instructions.scss'
</style>
