.wrapper {
    padding: 32rpx 0;

    &__title {
        color: #A4A7AE;
        line-height: 52rpx;
        margin-bottom: 32rpx;
    }

    &__time {
        margin-bottom: 32rpx;

        &__text {
            color: #717680;
        }

        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        padding: 24rpx;
        border-radius: 12px;
        border-bottom: 1rpx solid #EAECF0;
        background: #FFF;
        box-shadow: 0 4rpx 20rpx 0 rgba(0, 0, 0, 0.04);

        &__right {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &__text {
                color: #000;
                margin-right: 20rpx;
            }
        }
    }

    &__tips {
        padding: 12rpx 20rpx;
        background-color: #EDF1F6;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        flex-direction: row;

        &__text {
            display: flex;
            align-items: center;
            flex-direction: row;
            flex: 1;
            margin-left: 32rpx;
            line-height: 32rpx;
            color: #103A62;
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;

        &__btn {
            height: 60rpx;
            width: 48%;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &.left {
                border: 1px solid #103A62;

                .wrapper__footer__btn__text {
                    color: #103A62;
                }
            }

            &.right {
                border: 1px solid transparent;
                background-color: #103A62;

                .wrapper__footer__btn__text {
                    color: #fff;
                }
            }
        }

    }
}