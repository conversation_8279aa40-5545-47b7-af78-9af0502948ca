<script lang="ts" setup>
// import { ref } from 'vue'

const emits = defineEmits(['prev', 'submit'])

const show = defineModel<boolean>('modelValue', { default: false })

const time = defineModel<string>('time', { default: '' })

// const times = ref<number>(0)

function prevstep() {
  emits('prev')
}

function submit() {
  emits('submit')
}
</script>

<template>
  <view>
    <base-popup v-model="show" is-footer title="Rescheduling" height="720rpx">
      <view class="wrapper">
        <view class="wrapper__title fs--14--400">
          Please select a new boarding time
        </view>
        <view class="wrapper__time">
          <text class="wrapper__time__text fs--12--400">
            Date
          </text>
          <view class="wrapper__time__right">
            <text class="wrapper__time__right__text fs--12--400">
              {{ time }}
            </text>
            <zui-svg-icon width="38rpx" height="38rpx" color="#000" icon="arrow-right2-light" />
          </view>
        </view>
        <view class="wrapper__time">
          <text class="wrapper__time__text fs--12--400">
            Train number
          </text>
          <view class="wrapper__time__right">
            <text class="wrapper__time__right__text fs--12--400">
              14:00 Departure（D3534）
            </text>
            <zui-svg-icon width="38rpx" height="38rpx" color="#000" icon="arrow-right2-light" />
          </view>
        </view>
        <view class="wrapper__tips">
          <zui-svg-icon width="42rpx" height="42rpx" color="#000" icon="notification" />
          <text class="wrapper__tips__text fs--12--700">
            After a ticket change, the original ticket will be cancelled and the system will generate a new
            one for you
          </text>
        </view>
      </view>
      <template #footer>
        <view class="wrapper__footer">
          <view class="wrapper__footer__btn left" @tap="prevstep">
            <text class="fs--14--400 wrapper__footer__btn__text">
              Previous step
            </text>
          </view>
          <view class="wrapper__footer__btn right" @tap="submit">
            <text class="fs--14--400 wrapper__footer__btn__text">
              Confirm
            </text>
          </view>
        </view>
      </template>
    </base-popup>
    <!-- 日期选择 -->
    <!-- <l-calendar v-model:visible="visible" :z-index="1003" @change="handleConfirm" /> -->
  </view>
</template>

<style lang="scss" scoped>
@import './rebook-popup.scss'
</style>
