<script lang="ts" setup>
import type { cellProps } from 'wot-design-uni/components/wd-cell/types'
import { ref } from 'vue'

const show = defineModel<boolean>('modelValue', { default: false })

const coloumns = ref([
  {
    prop: 'name',
    label: 'station',
    width: '226rpx',
    // align: 'center',
    // slot: 'site',
  },
  {
    prop: 'school',
    label: 'Arrive',
    width: '132rpx',
    align: 'center',
  },
  {
    prop: 'major',
    label: 'Departure',
    width: '132rpx',
    align: 'center',
  },
  {
    prop: 'stay',
    label: 'Stay',
    width: '80rpx',
    align: 'center',
  },
  {
    prop: 'stay',
    label: 'Delay',
    width: '132rpx',
    align: 'center',
  },
])
</script>

<template>
  <view>
    <base-popup v-model="show" title="Timetable" height="1016rpx" :style="{ padding: '40rpx 24rpx 20rpx' }">
      <view class="wrapper__table">
        <base-table
          :coloumns="coloumns"
          :table-props="{
            border: false,
            cellProps: {
              padding: '0',
            },
          }"
        />
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
.wrapper {
  &__table {
    --wot-table-font-size: 24rpx
:deep(.wd-table__cell) {
  padding: 0 !important;
}
  }
}
</style>
