<script lang="ts" setup>
function copyOrderId() {
  uni.setClipboardData({
    data: 'D222 6885 1564 1643',
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}
</script>

<template>
  <view class="cart">
    <view class="cart__title fs--16--400">
      Order information
    </view>

    <view class="cart__list">
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Order ID
        </text>
        <view class="cart__list__item__right" @tap.stop="copyOrderId">
          <text class="fs--12--400">
            D222 6885 1564 1643
          </text>
          <view style="margin-left: 20rpx;">
            <zui-svg-icon width="38rpx" height="38rpx" icon="copy-light" color="#103A62" />
          </view>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Creation time
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            2025/07/10 14:00
          </text>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Payment method
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            Stripe/WeChat/Alipay
          </text>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Payment status
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            Paid (55.00NZD+100 points)
          </text>
        </view>
      </view>
      <view class="cart__list__item">
        <text class="cart__list__item__label fs--12--400">
          Payment time
        </text>
        <view class="cart__list__item__right">
          <text class="fs--12--400">
            2025/07/10
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './order-information.scss'
</style>
