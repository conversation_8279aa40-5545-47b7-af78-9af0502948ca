<script setup lang="ts">
import type { IOrderDetail, IOrderListType } from '@/model/order'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiDayToursOrderDetail, ApiOrderDetail, ApiPickupServiceOrderDetail } from '@/api/order'
import operationInstructions from './components/operation-instructions/operation-instructions.vue'
import orderInformation from './components/order-information/order-information.vue'
import otherTicketCard from './components/other-ticket-card/other-ticket-card.vue'
import pendingTicketCard from './components/pending-ticket-card/pending-ticket-card.vue'
import pendingTips from './components/pending-tips/pending-tips.vue'
import rebookPopup from './components/rebook-popup/rebook-popup.vue'
import transferCard from './components/transfer-card/transfer-card.vue'
import tripInfo from './components/trip-info/trip-info.vue'
import vehicleInformation from './components/vehicle-information/vehicle-information.vue'

const orderType = ref<IOrderListType['type']>('ticket')

const status = ref<number>(0) // 订单状态
const detail = ref<IOrderDetail>()

const desc = ref<string>('')

const isVerify = ref<boolean>(false)

// 状态区分
const orderDetailMap: Record<IOrderListType['type'], any> = {
  ticket: {
    getDetail: async (id: number) => {
      detail.value = await ApiOrderDetail({ order_id: id })
    },
  },
  day_tour: {
    getDetail: async (id: number) => {
      detail.value = await ApiDayToursOrderDetail({ order_id: id })
    },
  },
  pickup_service: {
    getDetail: async (id: number) => {
      detail.value = await ApiPickupServiceOrderDetail({ order_id: id })
    },
  },
  attraction: {
    getDetail: () => {

    },
  },
}

function openVerify() {
  isVerify.value = true
}

// 确认弹窗和类型
const isInformation = ref<boolean>(false)
const informationType = ref<string>('')
function openInformation(type: string) {
  informationType.value = type
  isInformation.value = true
}

const isRebook = ref<boolean>(false)
const rebookTime = ref<string>('2025/08/10')
function openRebook() {
  isRebook.value = true
}
function submitRebook() {
  isRebook.value = false
}

// 确认须知
function continueInstructions() {
  isInformation.value = false
  openRebook()
}

// 回退上一步
function prevstep() {
  isRebook.value = false
  isInformation.value = true
}

onLoad((options) => {
  orderType.value = options?.type
  status.value = Number(options?.orderStatus)
  desc.value = options?.desc

  if (options?.orderId) {
    orderDetailMap[orderType.value].getDetail(options.orderId)
  }
})
</script>

<template>
  <page>
    <nav-bar-page title="Order details" />
    <view class="body">
      <view class="detail">
        <view class="detail__container">
          <!-- 待使用车票||一日游 -->
          <pending-ticket-card
            v-if="['ticket', 'day_tour'].includes(orderType)"
            @verify="openVerify"
            @change="openInformation"
            @refund="openInformation"
          />
          <!-- 专车车票 -->
          <transfer-card v-if="orderType === 'pickup_service' && status !== 3" :data="detail?.ticket" />

          <!-- 发车提示 -->
          <pending-tips v-if="status === 1" :desc="desc" />

          <!-- 其他状态车票 -->
          <other-ticket-card v-if="status === 3" :data="detail?.ticket" :type="orderType" />

          <!-- 分割线 -->
          <view class="detail__container__space" />

          <!-- 行程信息 -->
          <trip-info :data="detail?.points" />

          <!-- 分割线 -->
          <view class="detail__container__space" />

          <!-- 车辆信息 -->
          <vehicle-information v-if="!(orderType === 'pickup_service' && status === 1)" :data="detail?.coachdriver" :type="orderType" />

          <!-- 订单信息 -->
          <order-information :data="detail?.amount" />
        </view>
      </view>
    </view>

    <!-- 验票 -->
    <verify-ticket-popup v-model="isVerify" />
    <!-- 退改签前置通知 -->
    <operation-instructions
      v-model="isInformation"
      :type="informationType"
      @continue="continueInstructions"
    />
    <!-- 确认改签 -->
    <!-- 确认改签 -->
    <rebook-popup
      v-model="isRebook" v-model:time="rebookTime" @prev="prevstep"
      @submit="submitRebook"
    />
  </page>
</template>

<style scoped lang="scss">
@import './order-detail.scss'
</style>
