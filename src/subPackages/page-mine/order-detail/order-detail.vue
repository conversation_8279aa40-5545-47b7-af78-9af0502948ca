<script setup lang="ts">
import { ref } from 'vue'
import operationInstructions from './components/operation-instructions/operation-instructions.vue'
import orderInformation from './components/order-information/order-information.vue'
import otherTicketCard from './components/other-ticket-card/other-ticket-card.vue'
import pendingTicketCard from './components/pending-ticket-card/pending-ticket-card.vue'
import pendingTips from './components/pending-tips/pending-tips.vue'
import rebookPopup from './components/rebook-popup/rebook-popup.vue'
import transferCard from './components/transfer-card/transfer-card.vue'
import tripInfo from './components/trip-info/trip-info.vue'
import vehicleInformation from './components/vehicle-information/vehicle-information.vue'

const orderType = ref<'ticket' | 'day_tour' | 'pickup_service' | 'attraction'>('ticket')

const status = ref<number>(0) // 订单状态

const isVerify = ref<boolean>(false)

function openVerify() {
  isVerify.value = true
}

// 确认弹窗和类型
const isInformation = ref<boolean>(false)
const informationType = ref<string>('')
function openInformation(type: string) {
  informationType.value = type
  isInformation.value = true
}

const isRebook = ref<boolean>(false)
const rebookTime = ref<string>('2025/08/10')
function openRebook() {
  isRebook.value = true
}
function submitRebook() {
  isRebook.value = false
}

// 确认须知
function continueInstructions() {
  isInformation.value = false
  openRebook()
}

// 回退上一步
function prevstep() {
  isRebook.value = false
  isInformation.value = true
}
</script>

<template>
  <page>
    <nav-bar-page title="Order details" />
    <view class="body">
      <view class="detail">
        <view class="detail__container">
          <!-- 待使用车票||一日游 -->
          <pending-ticket-card
            v-if="['ticket', 'day_tour'].includes(orderType)"
            @verify="openVerify"
            @change="openInformation"
            @refund="openInformation"
          />
          <!-- 发车提示 -->
          <pending-tips v-if="['ticket', 'day_tour'].includes(orderType)" />

          <!-- 其他状态车票 -->
          <other-ticket-card v-if="['ticket', 'day_tour'].includes(orderType) && status !== 0" />

          <!-- 专车车票 -->
          <transfer-card v-if="orderType === 'pickup_service'" />

          <!-- 分割线 -->
          <view class="detail__container__space" />

          <!-- 行程信息 -->
          <trip-info />

          <!-- 分割线 -->
          <view class="detail__container__space" />

          <!-- 车辆信息 -->
          <vehicle-information />

          <!-- 订单信息 -->
          <order-information />
        </view>
      </view>
    </view>

    <!-- 验票 -->
    <verify-ticket-popup v-model="isVerify" />
    <!-- 退改签前置通知 -->
    <operation-instructions
      v-model="isInformation"
      :type="informationType"
      @continue="continueInstructions"
    />
    <!-- 确认改签 -->
    <!-- 确认改签 -->
    <rebook-popup
      v-model="isRebook" v-model:time="rebookTime" @prev="prevstep"
      @submit="submitRebook"
    />
  </page>
</template>

<style scoped lang="scss">
@import './order-detail.scss'
</style>
