<script lang="ts" setup>
import { ref } from 'vue'

const activeTab = ref<number>(0)
const activeItemId = ref<string>('item0')

const current = ref<number>(-1)

function selectItem(index: number) {
  activeTab.value = index
  activeItemId.value = `item${index}`
}
</script>

<template>
  <page>
    <nav-bar-page title="My itinerary" />
    <view class="body">
      <view class="wrapper">
        <scroll-view
          class="wrapper__tabs get-height" scroll-x :scroll-into-view="activeItemId"
          :scroll-with-animation="true" :show-scrollbar="false"
        >
          <view
            v-for="(item, index) in 12" :id="`item${index}`"
            :key="index"
            class="wrapper__tabs__item"
            :class="{ active: activeTab === index }"
            @tap="selectItem(index)"
          >
            <text class="wrapper__tabs__item__text fs--14--400">
              {{ `2025/07/0${2 + index}` }}
            </text>
          </view>
        </scroll-view>

        <view class="wrapper__list">
          <wd-steps :active="current" vertical dot>
            <wd-step v-for="(item, index) in 10" :key="index" class="wrapper__list__item">
              <template #title>
                <view>
                  <view class="wrapper__list__item__title">
                    <view class="wrapper__list__item__title__idx">
                      {{ index + 1 }}
                    </view>
                    <view class="wrapper__list__item__title__text">
                      2025/07/01
                    </view>
                  </view>
                  <view class="wrapper__list__item__title__description">
                    Special vehicle: Shenzhen Airport > Bao 'an Center
                  </view>
                </view>
              </template>
            </wd-step>
          </wd-steps>
        </view>
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './my-itinerary.scss'
</style>
