.wrapper {
&__tabs {
    padding: 24rpx 0rpx 32rpx 32rpx;
    white-space: nowrap;
    &__item {
        padding: 16rpx 24rpx;
        border-radius: 20rpx;
        background-color: #ACBECF;
        margin-right: 20rpx;
        display: inline-block;

        &__text {
            color: #fff;
            line-height: 48rpx;
        }

        &.active {
            background-color: #103A62;
        }
    }
}

&__list {
    margin: 0 32rpx;

    &__item{
        :deep(.wd-step__content){
            padding: 24rpx !important;
            border-radius: 24rpx;
            // margin-bottom: 32rpx;
            background-color: #f7f7f7;
            margin-bottom: 32rpx;
        }
        :deep(.wd-step__line){
            height: calc(100% + 32rpx) !important;
        }
        &__title{
            display: flex;
            align-items: cenetr;
        
            &__idx{
                width: 40rpx;
                height: 40rpx;
                border-radius: 40rpx;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #103A62;
                font-size: 20rpx;
                font-weight: 600;
            }
            &__text{
                margin-left: 16rpx;
                color: #000;
                font-size: 24rpx;
                font-weight: 600;
                line-height: 40rpx;
            }
            &__description{
                color: #717680;
                font-size: 24rpx;
                font-weight: 400;
                margin-top:24rpx;
            }
    
            
        }
    }

    
}

&__tips {
    width: 100%;
    padding: 30rpx 0;
    &__text {
        color: #A4A7AE;
        text-align: center;
    }
}
}