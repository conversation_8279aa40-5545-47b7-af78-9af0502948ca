<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import newInfo from './components/new-info/new-info.vue'
import oldInfo from './components/old-info/old-info.vue'

const type = ref<string>('')
const step = ref<number>(1)

function nextStep() {
  step.value++
}

onLoad((e: any) => {
  if (e.type) {
    type.value = e.type
  }
})
</script>

<template>
  <page>
    <nav-bar-page />
    <view class="body">
      <old-info v-if="step === 1" :type="type" @submit="nextStep" />
      <new-info v-if="step === 2" :type="type" />
    </view>
  </page>
</template>
