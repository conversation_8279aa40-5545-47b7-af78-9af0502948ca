<script setup lang="ts">
withDefaults(defineProps<{
  prop?: string
  placeholder?: string
  btnText?: string
  type?: string
}>(), {
  prop: '',
  placeholder: '',
  btnText: '',
  type: 'phone',
})

const code = defineModel<string>('modelValue', { default: '' })

function sendOtp() {}
</script>

<template>
  <wd-input
    v-model="code"
    no-border
    :prop="prop"
    :placeholder="placeholder"
    custom-class="info__from__input"
  >
    <template #suffix>
      <view class="info__from__input__suffix" @tap="sendOtp">
        <text>{{ btnText }}</text>
      </view>
    </template>
  </wd-input>
</template>

<style scoped lang="scss">
@import '../style.scss'
</style>
