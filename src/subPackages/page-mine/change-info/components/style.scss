.info{
    padding: 0 32rpx;

    &__top{
        padding-top: 120rpx;

        &__title{
            font-size: 36rpx;
            font-weight: 400;
            color: #1D232E;
            font-family: Alimama FangYuanTi VF;
            text-align: center;
            padding-bottom: 120rpx;
        }
    }

    &__from{

        &__input{
            margin-bottom: 24rpx;
            background-color: #FAFAFA;
            border-radius: 24rpx;
            overflow: hidden;

            :deep(.wd-input__value){
                height: 88rpx;
                border-radius: 24rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 24rpx;
            }

            &__suffix{
                height: 88rpx;
                background-color: #103A62;
                color: #fff;
                width: 200rpx;
                font-size: 28rpx;
                font-weight: 600;
                letter-spacing: 0.37px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}
.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}