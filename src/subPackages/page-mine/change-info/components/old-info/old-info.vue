<script setup lang="ts">
import { ref } from 'vue'
import CodeInput from '../code-input/code-input.vue'

withDefaults(defineProps<{
  type?: string
}>(), {
  type: '',
})

const emits = defineEmits(['submit'])

const maps = ref<any>({
  phone: {
    title: 'Verify the old mobile phone number',
    placeholder: 'Enter your phone number',
    otp: 'Please enter OTP',
  },
  email: {
    title: 'Verify the old email address',
    placeholder: 'Enter your email address',
    otp: 'Please enter E-mail OTP',
  },
})

const model = ref<{
  phone: string
  email: string
  code: string
}>({
  phone: '',
  email: '',
  code: '',
})

const form = ref<any>(null)

function submit() {
  emits('submit')
}
</script>

<template>
  <view>
    <view class="info">
      <view class="info__top">
        <view class="info__top__title">
          {{ type === 'phone' ? maps.phone.title : maps.email.title }}
        </view>
      </view>
      <view class="info__from">
        <wd-form ref="form" :model="model">
          <wd-cell-group>
            <wd-input
              v-if="type === 'phone'"
              v-model="model.phone"
              no-border
              prop="phone"
              :placeholder="maps.phone.placeholder"
              custom-class="info__from__input"
            />
            <wd-input
              v-if="type === 'email'"
              v-model="model.email"
              no-border
              prop="email"
              :placeholder="maps.email.placeholder"
              custom-class="info__from__input"
            />
            <code-input
              v-model="model.code"
              :type="type"
              :placeholder="type === 'phone' ? maps.phone.otp : maps.email.otp"
              prop="code"
              btn-text="Send OTP"
            />
          </wd-cell-group>
        </wd-form>
      </view>
    </view>
    <fixed-bottom-bar>
      <view class="bottom_bar" @tap="submit">
        <view class="bottom_bar__btn">
          Next step
        </view>
      </view>
    </fixed-bottom-bar>
  </view>
</template>

<style scoped lang="scss">
@import '../style.scss'
</style>
