<script setup lang="ts">
import { gotoPage } from '@/utils/router'
</script>

<template>
  <page>
    <nav-bar-page title="Invite friends" />
    <view class="body">
      <view class="wrapper">
        <view class="info">
          <view class="info__left">
            <view class="info__left__title">
              Invite your friends and seize the opportunity for love!
            </view>
            <view class="info__left__desc">
              My invitation ID: 123334
            </view>
          </view>
          <view @click="gotoPage(`/subPackages/page-home/notice/index?type=generalprotocol&name=invite_friends`)">
            <zui-svg-icon color="#1570EF" width="40rpx" height="40rpx" icon="info-square-light" />
          </view>
        </view>
        <view class="wrapper__space" />

        <view class="wrapper__list">
          <view class="wrapper__list__title">
            My invited friends (5)
          </view>
          <view v-for="(item, index) in 5" :key="index" class="wrapper__list__item">
            <image src="/static/test/avatar.png" class="wrapper__list__item__img" mode="aspectFill" />
            <view class="wrapper__list__item__info">
              <view class="wrapper__list__item__info__name">
                Kaitile
              </view>
              <view class="wrapper__list__item__info__date">
                Registration time: 2025/01/23 12:14
              </view>
              <view class="wrapper__list__item__info__desc">
                The first order points reward has been enjoyed
              </view>
            </view>
          </view>
        </view>
      </view>

      <fixed-bottom-bar>
        <view class="bottom_bar">
          <view class="bottom_bar__btn">
            Confirm the conversation
          </view>
        </view>
      </fixed-bottom-bar>
    </view>
  </page>
</template>

<style  lang="scss" scoped>
@import './invite-friends.scss'
</style>
