.wrapper {
    padding: 0 32rpx;
    .info {
        padding: 24rpx;
        border-radius: 24rpx;
        background-color: #fff;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
        border-bottom: 1rpx solid #EAECF0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        &__left {
            flex: 1;
            margin-right: 36rpx;

            &__title {
                color: #000;
                font-size: 32rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
            }
            
            &__desc{
                margin-top: 32rpx;
                color: #717680;
                font-family: Alimama FangYuanTi VF;
                font-size: 24rpx;
                font-weight: 400;
            }
        }
    }

    &__space{
        margin: 32rpx 0;
        width: 100%;
        height: 2rpx;
        background-color: #F5F6F6;
    }
    
    &__list{
        
        &__title{
            font-family: Alimama FangYuanTi VF;
            font-size: 32rpx;
            font-weight: 400;
            color: #1D232E;
            margin-bottom: 32rpx;
        }
        
        &__item{
            padding: 24rpx;
            background-color: #fff;
            border-radius: 24rpx;
            box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
            border-bottom: 1rpx solid #EAECF0;
            margin-bottom: 32rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            
            &__img{
                width: 92rpx;
                height: 92rpx;
                border-radius: 100rpx;
                margin-right: 32rpx;
            }
            
            &__info{
                flex: 1;
                
                &__name{
                    color: #103A62;
                    font-size: 32rpx;
                    margin-bottom: 16rpx;
                    font-weight: 700;
                    font-family: Alimama FangYuanTi VF;
                }
                
                &__date{
                    font-size: 24rpx;
                    color: #A4A7AE;
                    font-weight: 400;
                    font-family: Alimama FangYuanTi VF;
                    margin-bottom: 16rpx;
                }
                
                &__desc{
                    background-color: #EDF1F6;
                    padding: 8rpx 20rpx;
                    border-radius: 10rpx;
                    color: #103A62;
                    font-family: Alimama FangYuanTi VF;
                    font-weight: 400;
                    font-size: 20rpx;
                }
            }
        }
    }
}

.bottom_bar{
    padding: 20rpx 32rpx 0 32rpx;
    width: 100%;
    &__btn{
        background-color: #103A62;
        border-radius: 24rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        line-height: 48rpx;
        font-weight: 500;
        padding: 24rpx  ;
    }
}