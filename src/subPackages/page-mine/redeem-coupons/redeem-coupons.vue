<script setup lang="ts">
import type { ICouponListType } from '@/model/mine'
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiCouponList } from '@/api/user'
import { useList } from '@/hooks/useList'
import { gotoPage } from '@/utils/router'
import couponCard from './components/coupon-card/coupon-card.vue'

const show = ref<boolean>(false)

// 店铺id
const storeId = ref<number>()
function confirm() {
  show.value = false
  gotoPage(`/subPackages/page-mine/real-information/real-information`)
}

function exchange() {
  show.value = true
}

const list = useList<ICouponListType>(
  ApiCouponList,
  {
    page_no: 1,
    page_size: 10,
  },
)
onLoad((option: any) => {
  storeId.value = option.id
  list.setParams({
    store_id: option.id,
  })
})

onPullDownRefresh(() => {
  list.refresh()
  uni.stopPullDownRefresh()
})

onReachBottom(() => {
  list.loadMore()
})
</script>

<template>
  <page>
    <nav-bar-page title="Redeem coupons" />

    <view class="body">
      <view style="padding: 0 32rpx;">
        <coupon-card v-for="item in list.list.value" :key="item.id" :item="item" @exchange="exchange" />
      </view>
    </view>

    <confirm-popup
      v-model="show"
      height="400rpx"
      title="Confirmation of Exchange Notice"
      content="Your current remaining points are 3895. This redemption requires 50 points. Please confirm the redemption."
      @confirm="confirm"
    />
  </page>
</template>

<style scoped lang="scss">
@import './redeem-coupons.scss'
</style>
