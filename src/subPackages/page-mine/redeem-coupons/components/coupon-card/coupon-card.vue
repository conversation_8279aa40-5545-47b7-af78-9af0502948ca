<script lang="ts" setup>
import type { ICouponListType } from '@/model/mine'

const props = defineProps({
  item: {
    type: Object as () => ICouponListType,
    default: () => {},
  },
})

const emits = defineEmits(['exchange'])

function exchange() {
  emits('exchange')
}
</script>

<template>
  <view class="card">
    <view class="coupon">
      <image class="coupon__bg" src="/static/mine/coupons.png" />
      <view class="coupon__body">
        <view class="coupon__body__left">
          <text class="coupon__body__left__price">
            <!-- 50 -->
            {{ props.item.integrate }}
          </text>
        </view>
        <view class="coupon__body__center">
          <view class="coupon__body__center__title">
            <!-- Day tours coupon for independent tourists in New Zealand -->
            {{ props.item.name }}
          </view>
          <view class="coupon__body__center__date">
            <!-- Expires on 31 July 2025 -->
            {{ props.item.use_time_end_text }}
          </view>
          <view class="coupon__body__center__tag">
            <image class="coupon__body__center__tag__bg" src="/static/mine/coupon-tag.png" />
            <view class="coupon__body__center__tag__content">
              <text class="coupon__body__center__tag__content__price">
                <!-- 50 -->
                {{ props.item.integrate }}
              </text>
              <text class="coupon__body__center__tag__content__unit">
                points
              </text>
            </view>
          </view>
        </view>
        <view class="coupon__body__right" @tap="exchange">
          <text class="coupon__body__right__text">
            Exchange
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './coupon-card.scss'
</style>
