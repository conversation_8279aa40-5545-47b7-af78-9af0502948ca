<script lang="ts" setup>
import type { TicketInfo } from '@/model/home/<USER>'
import { h, ref } from 'vue'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import payCard from '@/subPackages/page-home/components/ticket-info/pay-card.vue'
import selectTime from '@/subPackages/page-home/components/ticket-info/select-time.vue'
import ticketInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import ticketRadio from '@/subPackages/page-home/components/ticket-info/ticket-radio.vue'
import travelInfo from './components/muti-travel-info.vue'
import popupDetail from './components/popup-detail.vue'
import popupTimeTable from './components/popup-time-table.vue'
import popupTravelInfo from './components/popup-travel-info.vue'

const ticketInfo = ref<TicketInfo>({})

const showDetail = ref(false)
const showTravelInfo = ref(false)
const showTimeTable = ref(false)
const date = ref(null)

function handleCheckTravelInfo() {
  showTravelInfo.value = true
}

function handleCheckDetail() {
  showDetail.value = true
}

function back() {
  uni.navigateBack()
}

function handlePreviewTimeTable(idx: number) {
  showTimeTable.value = true
}
</script>

<template>
  <view class="buy-muti-city-ticket-container">
    <nav-bar-page title="Order fill out">
      <view class="buy-muti-city-ticket-container__nav-bar-left-container" />
    </nav-bar-page>
    <view
      class="buy-muti-city-ticket-container__content"
    >
      <travelInfo @click="handleCheckTravelInfo" @preview-time-table="handlePreviewTimeTable" />
      <view class="buy-muti-city-ticket-container__content__line" />
      <selectTime v-if="false" v-model="date" />
      <selectPassenger v-model="ticketInfo.adult" />
      <selectPassenger v-model="ticketInfo.children">
        <template #left>
          <view class="buy-muti-city-ticket-container__content__children">
            <image class="buy-muti-city-ticket-container__content__children__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-muti-city-ticket-container__content__children__text">
              <text class="buy-muti-city-ticket-container__content__children__text__title">
                Children
              </text>
              <text class="buy-muti-city-ticket-container__content__children__text__desc">
                (3-12Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <selectPassenger v-model="ticketInfo.baby">
        <template #left>
          <view class="buy-muti-city-ticket-container__content__baby">
            <image class="buy-muti-city-ticket-container__content__baby__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-muti-city-ticket-container__content__baby__text">
              <text class="buy-muti-city-ticket-container__content__baby__text__title">
                Baby
              </text>
              <text class="buy-muti-city-ticket-container__content__baby__text__desc">
                (0-2Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <view class="buy-muti-city-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.phoneNumber" />
      </view>
      <view class="buy-muti-city-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.email" label="Buyer E-mail" placeholder="zer0126@.com" />
      </view>
      <view class="buy-muti-city-ticket-container__content__reserve">
        <text class="buy-muti-city-ticket-container__content__reserve__label">
          Booking fee (default selected)
        </text>
        <text class="buy-muti-city-ticket-container__content__reserve__value">
          1 NZD
        </text>
      </view>
      <ticketRadio v-model="ticketInfo.rebookChecked" label="Flexi Price" show-tip />
      <ticketRadio v-model="ticketInfo.deductionChecked" label="Points deduction" />
    </view>
    <payCard @check-detail="handleCheckDetail" />
    <popupDetail v-model:visible="showDetail" />
    <popupTravelInfo v-model:visible="showTravelInfo" />
    <popupTimeTable v-model="showTimeTable" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
