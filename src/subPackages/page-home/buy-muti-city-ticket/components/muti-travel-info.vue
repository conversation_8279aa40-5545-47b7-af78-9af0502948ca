<script lang="ts" setup>
const eimt = defineEmits<{
  (e: 'previewTimeTable', idx: number): void
}>()
function handlePreviewTimeTable(idx: number) {
  eimt('previewTimeTable', idx)
}
</script>

<template>
  <view class="travel-info-container">
    <view class="travel-info-container__main">
      <view v-for="(item, idx) in 2" :key="idx" class="travel-info-container__main__item">
        <text class="travel-info-container__main__item__title ff">
          {{ idx === 0 ? 'Outbound' : 'Return' }}
        </text>
        <view class="travel-info-container__main__item__content">
          <view class="travel-info-container__main__item__content__left">
            <view class="travel-info-container__main__item__content__left__destination">
              <text class="travel-info-container__main__item__content__left__destination__from">
                Shenzhen S.
              </text>
              <text class="travel-info-container__main__item__content__left__destination__arrow">
                >
              </text>
              <text class="travel-info-container__main__item__content__left__destination__to">
                Shenzhen N.
              </text>
            </view>
            <view class="travel-info-container__main__item__content__left__time">
              <text class="travel-info-container__main__item__content__left__time__text ff">
                07/08 Tue 08:00 Departure
              </text>
            </view>
          </view>
          <view class="travel-info-container__main__item__content__preview-time" @click.stop="handlePreviewTimeTable(idx)">
            <image class="travel-info-container__main__item__content__preview-time__icon" src="/static/home/<USER>" mode="aspectFill" />
          </view>
        </view>
      </view>
    </view>
    <view class="travel-info-container_arrow-icon">
      <zui-svg-icon width="40rpx" height="40rpx" icon="arrow-right2-light" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.travel-info-container{
display:flex;
flex-direction: row;
align-items: center;
&__main{
width: 640rpx;
display:flex;
flex-direction: column;
&__item{
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 16rpx 12rpx;
&__title{
font-size: 24rpx;
color: #717680;
font-weight: 400;
}
&__content{
display: flex;
flex-direction: row;
align-items: center;
&__left{
margin-right: 26rpx;
&__destination {
display: flex;
flex-direction: row;
align-items: center;
margin-bottom: 10rpx;
&__arrow{
margin: 8rpx;
}

&__from, &__arrow, &__to {
font-family: "Alimama FangYuanTi VF";
font-size: 30rpx;
font-weight: 700;
}
}
&__time {
&__text{
font-size: 26rpx;
color: #000;
}
}
}

&__preview-time{
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
border-radius: 60rpx;
width: 60rpx;
height: 60rpx;
background-color: #edf1f6;
&__icon {
width: 40rpx;
height: 40rpx;
}
}
}
}
}
}
.ff {
font-family: "Alimama FangYuanTi VF";
}
</style>
