<script lang="ts" setup>
const visible = defineModel('visible', {
  default: false,
})

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="center" :z-index="9999" custom-style="border-radius: 40rpx">
    <view class="popup-notification-container__content">
      <view class="popup-notification-container__content__header">
        <text class="popup-notification-container__content__header__title">
          Notification
        </text>
      </view>
      <view class="popup-notification-container__content__body">
        <text class="popup-notification-container__content__body__text">
          The reservation information has been submitted successfully. We will arrange for staff to contact you to confirm the itinerary details. Please pay attention to the phone or email.
        </text>
      </view>
      <view class="popup-notification-container__content__footer" @click="handleClose">
        <text class="popup-notification-container__content__footer__text">
          Got it
        </text>
      </view>
      <view class="popup-notification-container__content__close" @click.stop="handleClose">
        <zui-svg-icon width="30rpx" height="30rpx" icon="cancel-light" />
        <!-- <l-svg style="width: 30rpx;height: 30rpx;" src="/static/icons/cancel-light.svg" /> -->
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@use './popup-notification.scss'
</style>
