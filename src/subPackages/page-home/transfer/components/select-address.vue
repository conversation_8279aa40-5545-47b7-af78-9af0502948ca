<script lang="ts" setup>
import { AddressEnum } from '@/enum/home'
import fromIcon from '@/static/home/<USER>'
import toIcon from '@/static/home/<USER>'

const props = withDefaults(defineProps<{ type: 'from' | 'to' }>(), {
  type: 'from',
})

const data = defineModel<string>({
  default: '',
})

const wayMap = {
  from: AddressEnum.DEPART_ADDRESS,
  to: AddressEnum.ARRIVE_ADDRESS,
}

function handleSelectAddress() {
  uni.navigateTo({
    url: `/subPackages/page-home/select-transfer-address/index?way=transfer&address=${wayMap[props.type]}`,
  })
}
</script>

<template>
  <view class="select-address-container" @click.stop="handleSelectAddress">
    <image class="select-address-container__icon" :src="type === 'from' ? fromIcon : toIcon" mode="aspectFill" />
    <text v-if="!data" class="select-address-container__text">
      {{ type === 'from' ? 'From' : 'To' }}
    </text>
    <text v-else class="select-address-container__text">
      {{ data }}
    </text>
  </view>
</template>

<style lang="scss" scoped>
@use './select-address.scss'
</style>
