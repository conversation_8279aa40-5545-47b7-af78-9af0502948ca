<script lang="ts" setup>
import type { CarType } from '../index.vue'
import { ref, watch } from 'vue'

const props = withDefaults(defineProps<{
  carType: CarType
}>(), {
  carType: () => ({
    shared_price: '0',
    single_price: '0',
  }),
})

interface CarTypeItem {
  id: number
  title: string
  price: string
}

const model = defineModel({
  default: 0,
})

const carTypeList = ref<CarTypeItem[]>([
  {
    id: 0,
    title: 'Private car (exclusive)', // 独享
    price: '21 NZD',
  },
  {
    id: 1,
    title: 'Private car (carpool)', // 拼车
    price: '15 NZD',
  },
])

watch(() => props.carType, (newValue: CarType) => {
  carTypeList.value[0].price = `${newValue.single_price} NZD`
  carTypeList.value[1].price = `${newValue.shared_price} NZD`
}, {
  immediate: true,
})

function handleSelectCar(item: CarTypeItem) {
  console.log(item, 'item====')
  model.value = item.id
}
</script>

<template>
  <view class="select-car-container">
    <view
      v-for="(item, idx) in carTypeList"
      :key="idx" class="select-car-container__item"
      :class="{ 'select-car-container__item--active': model === item.id }"
      @click.stop="handleSelectCar(item)"
    >
      <text class="select-car-container__item__title">
        {{ item?.title }}
      </text>
      <text class="select-car-container__item__price">
        {{ item?.price }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './select-card.scss'
</style>
