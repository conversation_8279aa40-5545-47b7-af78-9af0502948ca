<script lang="ts" setup>
import { ref } from 'vue'

interface CarTypeItem {
  id: number
  title: string
  price: string
}

const model = defineModel({
  default: 0,
})

const active = ref(1)
const carTypeList = ref<CarTypeItem[]>([
  {
    id: 1,
    title: 'Private car (exclusive)',
    price: '21 NZD',
  },
  {
    id: 2,
    title: 'Private car (carpool)',
    price: '15 NZD',
  },
])

function handleSelectCar(item: CarTypeItem, idx: number) {
  active.value = item.id
  model.value = item.id
}
</script>

<template>
  <view class="select-car-container">
    <view
      v-for="(item, idx) in carTypeList"
      :key="idx" class="select-car-container__item"
      :class="{ 'select-car-container__item--active': active === item.id }"
      @click.stop="handleSelectCar(item, idx)"
    >
      <text class="select-car-container__item__title">
        {{ item?.title }}
      </text>
      <text class="select-car-container__item__price">
        {{ item?.price }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './select-card.scss'
</style>
