.select-car-container{
		display:flex;
		flex-direction: row;
		justify-content: space-between;
		&__item{
			display: flex;
            flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 324rpx;
			height: 170rpx;
			border-radius: 20rpx;
			padding: 16rpx 12rpx;
			background-color: #ACBECF;
			&__title{
				display: flex;
                flex-direction: column;
				align-items: center;
				height: 48rpx;
				font-family: Alimama FangYuanTi VF;
				font-weight: 400;
				font-size: 28rpx;
				color: #fff;
				margin-bottom: 12rpx;
			}
			&__price{
				display: flex;
                flex-direction: column;
				align-items: center;
				height: 48rpx;
				font-family: Alimama FangYuanTi VF;
				font-weight: 400;
				font-size: 32rpx;
				color: #fff;
			}
			&--active{
				background-color: #103A62;
			}
		}
	}