<script lang="ts" setup>
import timeIcon from '@/static/home/<USER>'

const data = defineModel<string>({
  default: '',
})
</script>

<template>
  <view class="select-time-container">
    <image class="select-time-container__icon" :src="timeIcon" mode="aspectFill" />
    <text v-show="data === ''" class="select-time-container__text">
      Time
    </text>
    <text v-show="data !== ''">
      {{ data }}
    </text>
  </view>
</template>

<style lang="scss" scoped>
@use './select-time.scss'
</style>
