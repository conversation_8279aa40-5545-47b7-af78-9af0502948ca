<script lang="ts" setup>
import type { BaseSwiperItemType, DistType, PayResult, PayStatusResult, PayTypeInfo, PayTypeParams } from '@/model/common'
import type { Transfer } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref, watch } from 'vue'
import { ApiGetPayStatus, ApiGetPayType, ApiSendPay } from '@/api/common'
import { ApiGetTransferBananer, ApiGetTransferPreOrder, ApiPayTransferOrder } from '@/api/home/<USER>'
import { PayStatusEnum } from '@/enum/common'
import { AddressInfoEnum } from '@/enum/home'
import selectTime from '@/pages/page-home/components/calendar.vue'
import callIcon from '@/static/home/<USER>'
import emailIcon from '@/static/home/<USER>'
import { useConfigStore } from '@/stores/config'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import transferInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import { getStore, removeStore } from '@/utils/store'
import popupNotification from './components/popup-notification.vue'
import selectAddress from './components/select-address.vue'
import SelectCard from './components/select-card.vue'

type TransferInfo = Partial<Transfer>

export interface CarType {
  shared_price: string
  single_price: string
}

const configStore = useConfigStore()
const transferInfo = ref<TransferInfo>({
  depart: '',
  arrive: '',
  travel_time: null,
  traveler_count: 1,
  luggage_info: {
    large: 0,
    medium: 0,
    small: 0,
  },
  car_type: 0,
})

const payWayInfo = ref()
const showNotification = ref(false)
const statusMap = {
  [PayStatusEnum.UNPAID]: () => {
    uni.showToast({
      title: 'Unpaid',
      icon: 'none',
      duration: 2000,
    })
  },
  [PayStatusEnum.PAID]: () => {
    uni.showToast({
      title: 'Paid',
      icon: 'none',
      duration: 2000,
    })
  },
  [PayStatusEnum.PAYMENT_FAILED]: () => {
    uni.showToast({
      title: 'payment failed',
      icon: 'none',
      duration: 2000,
    })
  },
}

const cardType = ref<CarType>({
  shared_price: '0',
  single_price: '0',
})
const swiperList = ref<Partial<BaseSwiperItemType>[]>([
  // {
  //   id: 1,
  //   image: '/static/home/<USER>',
  // },
])

onShow(() => {
  const { way, ...data } = getStore(AddressInfoEnum.ADDRESS_INFO)
  transferInfo.value = {
    ...transferInfo.value,
    ...data,
  }
})

watch(() => transferInfo.value, (newValue: TransferInfo) => {
  if (newValue.departId && newValue.arriveId) {
    handleGetTransferPreOrder()
  }
})

async function handleGetTransferBananer() {
  const data = await ApiGetTransferBananer()
  swiperList.value = data.banner
  // console.log(data, 'data')
}

handleGetTransferBananer()

async function handleGetTransferPreOrder() {
  const params = {
    from_id: transferInfo.value.departId,
    to_id: transferInfo.value.arriveId,
    // traveler_count: transferInfo.value.passengers,
  }
  const data = await ApiGetTransferPreOrder(params)
  cardType.value = data
}

// handleGetTransferPreOrder()

function back() {
  removeStore(AddressInfoEnum.ADDRESS_INFO)
  uni.switchTab({ url: '/pages/page-home/index' })
}

function handleCheckMore() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=generalprotocol&name=pick_service`,
  })
}

// 获取支付方式
async function handleGetPayType(payload: PayTypeParams): Promise<PayTypeInfo> {
  const params: PayTypeParams = {
    ...payload,
  }
  const data = await ApiGetPayType(params)
  payWayInfo.value = data

  console.log(data, 'data')
  return data as Promise<PayTypeInfo>
}

async function handlePayTransferOrder() {
  let payTypeInfo: PayTypeInfo = {}
  let payInfo: PayResult = {}
  let timer: any = null
  let payStatusResult: PayStatusResult = {}
  const params = {
    ...transferInfo.value,
    from_id: transferInfo.value.departId,
    to_id: transferInfo.value.arriveId,
  }
  const data = await ApiPayTransferOrder(params)
  if (data.from) {
    payTypeInfo = await handleGetPayType({ from: data.from, order_id: data.order_id })
  }
  if (payTypeInfo) {
    payInfo = await ApiSendPay({ from: data.from, pay_way: payTypeInfo.pay_way!, order_id: data.order_id! })
    console.log(payInfo, 'payInfo')
  }
  const orderInfo = {
    customer: payInfo.config?.customer ?? '',
    ephemeralKey: payInfo.config?.ephemeralKey ?? '',
    isAllowDelay: true,
    merchantName: 'geekdance',
    paymentIntent: payInfo.config?.client_secret ?? '',
    publishKey: import.meta.env.VITE_APP_STRIPE_KEY ?? '',
  }

  // #ifdef H5
  window.open(payInfo.config?.url, '_blank')
  const startTime = new Date().getTime()
  let endTime: number = 0
  timer = setInterval(async () => {
    endTime = new Date().getTime()
    if (endTime - startTime > 1000 * 60 * 5) {
      clearInterval(timer)
      return
    }
    payStatusResult = await ApiGetPayStatus({ from: data.from, order_id: data.order_id })
  }, 1000)
  if (payStatusResult.pay_status === PayStatusEnum.PAID) {
    uni.showToast({
      icon: 'none',
      title: '支付成功',
    })
  }
  console.log(payStatusResult, 'payStatusResult')
  // statusMap[payStatusResult.status]()

  // #endif

  // #ifdef APP
  uni.getProvider({
    service: 'payment',
    success: (providerResult) => {
      if (!providerResult || !providerResult.provider || !(providerResult.provider as string[]).includes('stripe')) {
        return new Error('当前环境不支持 Stripe 支付')
      }

      uni.requestPayment({
        provider: 'stripe' as any,
        orderInfo,
      })
    },
  })

  // #endif
  console.log(data, payInfo, 'data == pay')
}

function handlePay() {
  handlePayTransferOrder()
  // showNotification.value = true
}
</script>

<template>
  <view class="transfer-container">
    <nav-bar-page is-custom>
      <view class="transfer-container__nav-bar-left-container">
        <zui-svg-icon class="transfer-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click="back" />
        <text class="transfer-container__nav-bar-left-container__title">
          Transfer
        </text>
        <text class="transfer-container__nav-bar-left-container__more" @click.stop="handleCheckMore">
          More
        </text>
      </view>
    </nav-bar-page>
    <view
      class="transfer-container__main"
    >
      <view class="transfer-container__main__loop">
        <base-swiper :list="swiperList" />
      </view>
      <view class="transfer-container__main__content">
        <selectAddress v-model="transferInfo.depart" type="from" />
        <selectAddress v-model="transferInfo.arrive" type="to" />
        <selectTime v-model="transferInfo.travel_time!" style="margin-bottom: 20rpx;" />
        <selectPassenger v-model="transferInfo.traveler_count" :min="1" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;">
          <template #left>
            <view class="transfer-container__main__content__passenger">
              <image class="transfer-container__main__content__passenger__icon" src="/static/home/<USER>" mode="aspectFill" />
              <text class="transfer-container__main__content__passenger__text">
                Number of passengers
              </text>
            </view>
          </template>
        </selectPassenger>
        <transferInput v-model="transferInfo.phone" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;" :icon="callIcon" label="" placeholder="Phone number" />
        <transferInput v-model="transferInfo.email" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;" :icon="emailIcon" label="" placeholder="Email" />
        <view class="transfer-container__main__content__luggage">
          <view class="transfer-container__main__content__luggage__title">
            <image class="transfer-container__main__content__luggage__title__icon" src="/static/home/<USER>" mode="aspectFill" />
            <text class="transfer-container__main__content__luggage__title__text">
              Luggage size
            </text>
          </view>
          <selectPassenger v-model="transferInfo.luggage_info!.large" :b-style="{ 'height': '48rpx', 'padding': '0', 'margin-bottom': '24rpx', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Large
              </text>
            </template>
          </selectPassenger>
          <selectPassenger v-model="transferInfo.luggage_info!.medium" :b-style="{ 'height': '48rpx', 'padding': 0, 'margin-bottom': '24rpx', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Medium
              </text>
            </template>
          </selectPassenger>
          <selectPassenger v-model="transferInfo.luggage_info!.small" :b-style="{ 'height': '48rpx', 'padding': 0, 'margin-bottom': '0', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Small
              </text>
            </template>
          </selectPassenger>
        </view>
        <SelectCard v-model="transferInfo.car_type" :car-type="cardType" style="margin-bottom: 100rpx;" />
        <view class="transfer-container__main__content__pay">
          <view class="transfer-container__main__content__pay__content" @click.stop="handlePay">
            <text class="transfer-container__main__content__pay__content__text">
              Pay
            </text>
          </view>
        </view>
      </view>
    </view>
    <popupNotification v-model:visible="showNotification" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
