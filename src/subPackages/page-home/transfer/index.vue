<script lang="ts" setup>
import type { BaseSwiperItemType } from '@/model/common'
import type { Transfer } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import selectTime from '@/pages/page-home/components/calendar.vue'
import callIcon from '@/static/home/<USER>'
import emailIcon from '@/static/home/<USER>'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import transferInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import { getStore } from '@/utils/store'
import popupNotification from './components/popup-notification.vue'
import selectAddress from './components/select-address.vue'
import SelectCard from './components/select-card.vue'

const transferInfo = ref<Transfer>({
  depart: '',
  arrive: '',
  time: null,
  luggageInfo: {
    largeNum: 0,
    smallNum: 0,
    mediumNum: 0,
  },
  type: 1,
})

onShow(() => {
  const { depart, arrive } = getStore(AddressInfoEnum.ADDRESS_INFO)
  console.log(depart, arrive, '====')
  transferInfo.value.depart = depart ?? ''
  transferInfo.value.arrive = arrive ?? ''
})

function back() {
  uni.navigateBack()
}

function handleCheckMore() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=pick_service`,
  })
}

const swiperList = ref<BaseSwiperItemType[]>([
  {
    id: 1,
    image: '/static/home/<USER>',
  },
  {
    id: 1,
    image: '/static/home/<USER>',
  },
  {
    id: 1,
    image: '/static/home/<USER>',
  },
])

const showNotification = ref(false)

function handlePay() {
  showNotification.value = true
//   console.log(transferInfo.value)
}
</script>

<template>
  <view class="transfer-container">
    <nav-bar-page is-custom>
      <view class="transfer-container__nav-bar-left-container">
        <zui-svg-icon class="transfer-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click="back" />
        <!-- <l-svg class="transfer-container__nav-bar-left-container__back-icon" src="/static/icons/arrow-left2-light.svg" @click="back" /> -->
        <text class="transfer-container__nav-bar-left-container__title">
          Transfer
        </text>
        <text class="transfer-container__nav-bar-left-container__more" @click.stop="handleCheckMore">
          More
        </text>
      </view>
    </nav-bar-page>
    <view
      class="transfer-container__main"
    >
      <view class="transfer-container__main__loop">
        <base-swiper :list="swiperList" />
      </view>
      <view class="transfer-container__main__content">
        <selectAddress v-model="transferInfo.depart" type="from" />
        <selectAddress v-model="transferInfo.arrive" type="to" />
        <selectTime v-model="transferInfo.time!" style="margin-bottom: 20rpx;" />
        <selectPassenger v-model="transferInfo.passengers" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;">
          <template #left>
            <view class="transfer-container__main__content__passenger">
              <image class="transfer-container__main__content__passenger__icon" src="/static/home/<USER>" mode="aspectFill" />
              <text class="transfer-container__main__content__passenger__text">
                Number of passengers
              </text>
            </view>
          </template>
        </selectPassenger>
        <transferInput v-model="transferInfo.phoneNumber" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;" :icon="callIcon" label="" placeholder="Phone number" />
        <transferInput v-model="transferInfo.email" :b-style="{ height: '88rpx' }" style="margin-bottom: 20rpx;" :icon="emailIcon" label="" placeholder="Email" />
        <view class="transfer-container__main__content__luggage">
          <view class="transfer-container__main__content__luggage__title">
            <image class="transfer-container__main__content__luggage__title__icon" src="/static/home/<USER>" mode="aspectFill" />
            <text class="transfer-container__main__content__luggage__title__text">
              Luggage size
            </text>
          </view>
          <selectPassenger v-model="transferInfo.luggageInfo.largeNum" :b-style="{ 'height': '48rpx', 'padding': '0', 'margin-bottom': '24rpx', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Large
              </text>
            </template>
          </selectPassenger>
          <selectPassenger v-model="transferInfo.luggageInfo.mediumNum" :b-style="{ 'height': '48rpx', 'padding': 0, 'margin-bottom': '24rpx', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Medium
              </text>
            </template>
          </selectPassenger>
          <selectPassenger v-model="transferInfo.luggageInfo.smallNum" :b-style="{ 'height': '48rpx', 'padding': 0, 'margin-bottom': '0', 'borderRadius': 0 }">
            <template #left>
              <text class="transfer-container__main__content__luggage__text">
                Small
              </text>
            </template>
          </selectPassenger>
        </view>
        <SelectCard v-model="transferInfo.type" style="margin-bottom: 100rpx;" />
        <view class="transfer-container__main__content__pay">
          <view class="transfer-container__main__content__pay__content" @click.stop="handlePay">
            <text class="transfer-container__main__content__pay__content__text">
              Pay
            </text>
          </view>
        </view>
      </view>
    </view>
    <popupNotification v-model:visible="showNotification" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
