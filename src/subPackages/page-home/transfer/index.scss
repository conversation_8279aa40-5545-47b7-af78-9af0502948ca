.transfer-container {
		&__nav-bar-left-container {
			display: flex;
			flex-direction: row;
			align-items: center;
			&__back-icon{
				width: 48rpx;
				height: 48rpx;
				margin-right: 20rpx;
			}
			&__title{
				font-size: 32rpx;
				color: #111b19;
				font-weight: 700;
				margin-right: 16rpx;
			}
			&__more{
				font-family: "Alimama FangYuanTi VF";
				color: #103A62;
				font-size: 26rpx;
				font-weight: 400;
				text-decoration-line: underline;
			}
		}
		&__main {
			padding: 24rpx 32rpx;
			&__loop{
				margin-bottom: 40rpx;
			}
			&__content{
				&__passenger{
					display: flex;
					flex-direction: row;
					align-items: center;
					&__icon{
						width: 40rpx;
						height: 40rpx;
						margin-right: 24rpx;
					}
					&__text{
						font-family: Alimama FangYuanTi VF;
						font-weight: 400;
						font-size: 24rpx;
						color: #717680;
					}
				}
				&__luggage{
					background-color: #fafafa;
					border-radius: 24rpx;
					padding: 24rpx ;
					margin-bottom: 40rpx;
					&__title{
						display: flex;
						flex-direction: row;
						align-items: center;
						margin-bottom: 24rpx;
						&__icon{
							width: 40rpx;
							height: 40rpx;
							margin-right: 24rpx;
						}
						&__text{
							font-family: Alimama FangYuanTi VF;
							font-weight: 400;
							font-size: 24rpx;
							color: #717680;
						}
					}
					&__text{
						font-family: Alimama FangYuanTi VF;
						font-weight: 400;
						font-size: 26rpx;
						color: #000;
					}
				}
				&__pay {
					display: flex;
					align-items: center;
					justify-content: center;
					&__content{
						width: 660rpx;
						height: 84rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 24rpx;
						padding: 24rpx 28rpx;
						background-color: #103A62;
						&__text{
							color: #fff;
						}
					}
				}
			}
		}
	}