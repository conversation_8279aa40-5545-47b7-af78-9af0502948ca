<script lang="ts" setup>
const visible = defineModel('visible', {
  default: false,
})

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="bottom" :z-index="9999" custom-style="border-radius:40rpx 40rpx 0 0;">
    <view class="popup-detail-container__content">
      <view class="popup-detail-container__content__header">
        <text class="popup-detail-container__content__header__title">
          Price details
        </text>
      </view>
      <view class="popup-detail-container__content__body">
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Outbound ticket price:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            21 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Return ticket price:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            21 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Booking fee:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            2 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Point deduction:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            -1 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Total:
          </text>
          <text class="popup-detail-container__content__body__item__value total">
            20 NZD
          </text>
        </view>
      </view>
      <view class="popup-detail-container__content__close" @click.stop="handleClose">
        <!-- <l-svg style="width: 40rpx;height: 40rpx;" src="/static/icons/cancel-light.svg" /> -->
        <zui-svg-icon width="40rpx" height="40rpx" icon="cancel-light" />
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@use './popup-detail.scss'
</style>
