<script setup lang="ts">
import { reactive, ref } from 'vue'
import Tabs from '@/subPackages/page-home/components/ticket-info/tabs.vue'

const visible = defineModel({
  default: false,
})

const tabList = [
  {
    name: 'Outbound',
    title: 'Outbound',
  },
  {
    name: 'Return',
    title: 'Return',
  },
]

const coloumns = ref([
  {
    prop: 'name',
    label: 'Stopover station',
    width: '212rpx',
    align: 'center',
    slot: 'site',
  },
  {
    prop: 'school',
    label: 'Arrive',
    width: '156rpx',
    align: 'center',
  },
  {
    prop: 'major',
    label: 'Departure',
    width: '174rpx',
    align: 'center',
  },
  {
    prop: 'stay',
    label: 'Stay',
    width: '156rpx',
    align: 'center',
  },
])

const dataList = reactive([
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '2min',
    food: true,
  },
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '3min',
  },
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '1min',
  },
])

function handleTabChange(name: string) {
  console.log(name, 'name')
}
</script>

<template>
  <BasePopup v-model="visible" :style="{ padding: '40rpx 24rpx', height: '100%' }" title="Time Table">
    <view style="margin-top: 32rpx;">
      <Tabs :tab-list="tabList" @change="handleTabChange" />
      <BaseTable style="margin-top: 32rpx;" :data="dataList" :coloumns="coloumns">
        <template #site="{ row }">
          <view class="site-column">
            <text class="site-column__text">
              {{ row.name }}
            </text>
            <image v-if="row.food" class="site-column__food-icon" src="/static/home/<USER>" mode="aspectFill" />
          </view>
        </template>
      </BaseTable>
    </view>
  </BasePopup>
</template>

<style lang="scss" scoped>
.popup-time-table-container {
  padding: 40rpx;
}
.site-column{
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    &__text{
        margin-right: 34rpx;
    }
    &__food-icon{
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -34rpx;
        width: 28rpx;
        height: 28rpx;
}
}
</style>
