.popup-travel-info-container__content{
		/*  #ifdef APP */
			padding: 50rpx 24rpx 0;
		/* #endif */
		&__item{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			height: 188rpx;
			padding: 16rpx 24rpx;
			&__title{
				font-family: "Alimama FangYuanTi VF";
				font-size: 24rpx;
				color: #717680;
			}
			&__section{
				&__info{
					display: flex;
					flex-direction: row;
					margin-bottom: 20rpx;
					&__plate-number{
						font-family: "Alimama FangYuanTi VF";
						font-size: 30rpx;
						color: #000;
						font-weight: 700;
						margin-right: 12rpx;
					}
					&__desc{
						font-family: "Alimama FangYuanTi VF";
						font-size: 26rpx;
						color: #000;
						font-weight: 400;
					}
				}
				&__time{
					font-family: "Alimama FangYuanTi VF";
					font-size: 26rpx;
					color: #000;
					font-weight: 400;
				}
			}
		}
	}
	.bb {
		border-bottom: 2rpx solid #cfd6dd;
	}