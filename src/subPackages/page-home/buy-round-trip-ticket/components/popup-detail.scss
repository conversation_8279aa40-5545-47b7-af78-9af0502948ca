.popup-detail-container__content{
	position: relative;
	padding: 40rpx 24rpx  56rpx;
	&__header{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 52rpx;
		margin-bottom: 32rpx;
		&__title{
			font-family: "Alimama FangYuanTi VF";
			font-weight: 700;
			font-size: 32rpx;
			color: #111b19;
		}
	}
	&__body{
		width: 702rpx;
		padding: 32rpx 32rpx 0;
		&__item{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;
			&__label{
				font-family: "Alimama FangYuanTi VF";
				color:'#161616';
				font-weight: 700;
				font-size: 28rpx;
			}
			&__value{
				font-family: "Alimama FangYuanTi VF";
				font-weight: 400;
				color: #717680;
				font-size: 28rpx;
			}
		}
	}
	&__close{
		position: absolute;
		top: 46rpx;
		right: 46rpx;
	}
}
.total {
	font-weight: 700;
	color: #912018;
}