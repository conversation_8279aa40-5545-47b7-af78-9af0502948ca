<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import type { TicketInfo } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import payCard from '@/subPackages/page-home/components/ticket-info/pay-card.vue'
import ticketInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import ticketRadio from '@/subPackages/page-home/components/ticket-info/ticket-radio.vue'
import travelInfo from '@/subPackages/page-home/components/ticket-info/travel-info.vue'
import { getStore } from '@/utils/store'
import popupDetail from './components/popup-detail.vue'
import popupTimeTable from './components/popup-time-table.vue'
import popupTravelInfo from './components/popup-travel-info.vue'

interface RoundTripTickerInfo extends TicketInfo {
  rebookOutboundChecked?: boolean
  rebookReturnChecked?: boolean
}

const ticketInfo = ref<RoundTripTickerInfo>({})
const showDetail = ref(false)
const showTravelInfo = ref(false)
const showTimeTable = ref(false)
const addressInfo = ref<AddressInfo>({
  depart: '',
  arrive: '',
})

function back() {
  uni.navigateBack()
}

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
})

function handleCheckTravelInfo() {
  showTravelInfo.value = true
}

function handleCheckDetail() {
  showDetail.value = true
}

function handlePayment() {
  console.log(ticketInfo.value, 'ticketInfo')
}

function handlePreviewTimeTable() {
  showTimeTable.value = true
}
</script>

<template>
  <view class="buy-round-trip-ticket-container">
    <nav-bar-page is-custom>
      <view class="buy-round-trip-ticket-container__nav-bar-left-container">
        <zui-svg-icon class="buy-round-trip-ticket-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <text class="buy-round-trip-ticket-container__nav-bar-left-container__from">
          {{ addressInfo.depart }}
        </text>
        <text class="buy-round-trip-ticket-container__nav-bar-left-container__arrow">
          >
        </text>
        <text class="buy-round-trip-ticket-container__nav-bar-left-container__to">
          {{ addressInfo.arrive }}
        </text>
      </view>
    </nav-bar-page>
    <view
      class="buy-round-trip-ticket-container__content"
    >
      <travelInfo @click="handleCheckTravelInfo" @preview-time-table="handlePreviewTimeTable" />
      <view class="buy-round-trip-ticket-container__content__line" />
      <selectPassenger v-model="ticketInfo.adult" />
      <selectPassenger v-model="ticketInfo.children">
        <template #left>
          <view class="buy-round-trip-ticket-container__content__children">
            <image class="buy-round-trip-ticket-container__content__children__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-round-trip-ticket-container__content__children__text">
              <text class="buy-round-trip-ticket-container__content__children__text__title">
                Children
              </text>
              <text class="buy-round-trip-ticket-container__content__children__text__desc">
                (3-12Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <selectPassenger v-model="ticketInfo.baby">
        <template #left>
          <view class="buy-round-trip-ticket-container__content__baby">
            <image class="buy-round-trip-ticket-container__content__baby__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-round-trip-ticket-container__content__baby__text">
              <text class="buy-round-trip-ticket-container__content__baby__text__title">
                Baby
              </text>
              <text class="buy-round-trip-ticket-container__content__baby__text__desc">
                (0-2Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <view class="buy-round-trip-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.phoneNumber" />
      </view>
      <view class="buy-round-trip-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.email" label="Buyer E-mail" placeholder="zer0126@.com" />
      </view>
      <view class="buy-round-trip-ticket-container__content__reserve">
        <text class="buy-round-trip-ticket-container__content__reserve__label">
          Booking fee (default selected)
        </text>
        <text class="buy-round-trip-ticket-container__content__reserve__value">
          1 NZD
        </text>
      </view>
      <ticketRadio v-model="ticketInfo.rebookOutboundChecked" label="Flexi Price（outbound）" show-tip />
      <ticketRadio v-model="ticketInfo.rebookReturnChecked" label="Flexi Price（return）" show-tip />
      <ticketRadio v-model="ticketInfo.deductionChecked" label="Points deduction" />
    </view>
    <payCard @check-detail="handleCheckDetail" @pay="handlePayment" />
    <popupDetail v-model:visible="showDetail" />
    <popupTravelInfo v-model:visible="showTravelInfo" />
    <popupTimeTable v-model="showTimeTable" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
