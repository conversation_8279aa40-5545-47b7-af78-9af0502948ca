<script lang="ts" setup>
import { ref } from 'vue'

const current = ref(-1)

function handleCheckDetail() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=generalprotocol&name=package_ticket`,
  })
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/order-fill-out/index',
  })
}
</script>

<template>
  <view class="scenic-spot-container">
    <view class="scenic-spot-container__header">
      <view class="scenic-spot-container__header__left">
        <text class="scenic-spot-container__header__left__title">
          South island linkage
        </text>
        <view style="width: 36rpx;height: 36rpx;" @click.stop="handleCheckDetail">
          <zui-svg-icon width="36rpx" height="36rpx" icon="danger-circle-light" />
        </view>
      </view>
      <!-- <view class="scenic-spot-container__header__right">
        <text class="scenic-spot-container__header__right__text mr">
          15%
        </text>
        <text class="scenic-spot-container__header__right__text">
          0ff
        </text>
      </view> -->
    </view>
    <view class="scenic-spot-container__step">
      <wd-steps
        :current="current"
        dot
        vertical
      >
        <wd-step v-for="(item, idx) in 3" :key="idx">
          <template #description>
            <view class="scenic-spot-container__step__item">
              <view class="scenic-spot-container__step__item__address">
                <text class="scenic-spot-container__step__item__address__name">
                  Shenzhen
                </text>
                <!-- <view class="scenic-spot-container__step__item__address__time">
                  <text class="scenic-spot-container__step__item__address__time__text">
                    04:00
                  </text>
                </view> -->
              </view>
              <view class="scenic-spot-container__step__item__time">
                <text class="scenic-spot-container__step__item__time__hours">
                  3h
                </text>
                <text class="scenic-spot-container__step__item__time__min">
                  25min
                </text>
              </view>
            </view>
          </template>
        </wd-step>
      </wd-steps>
    </view>
    <view class="scenic-spot-container__price">
      <text class="scenic-spot-container__price__text">
        199 NZD
      </text>
      <text class="scenic-spot-container__price__desc">
        (256.55 NZD)
      </text>
    </view>
    <view v-if="true" class="scenic-spot-container__btn" @click="handleBuyTicket">
      <text class="scenic-spot-container__btn__text">
        Buy ticket
      </text>
    </view>
    <view v-else class="scenic-spot-container__btn-sold-out">
      <text class="scenic-spot-container__btn__text">
        Sold out
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './scenic-spot-card.scss'
</style>
