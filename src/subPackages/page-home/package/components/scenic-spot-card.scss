.scenic-spot-container{
		background-color: #fafafa;
		border-radius: 24rpx;
		padding: 40rpx 24rpx;
		margin-bottom: 56rpx;
		&__header{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;
			&__left{
				display: flex;
				flex-direction: row;
				align-items: center;
				&__title{
					font-family: "Alimama FangYuanTi VF";
					font-size: 28rpx;
					color: #000;
					font-weight: 700;
					margin-right: 10rpx;
				}
			}
			&__right{
				width: 116rpx;
				height: 52rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				background-color: #edf1f6;
				border-radius: 10rpx;
				&__text{
					font-family: "Alimama FangYuanTi VF";
					color: #103A62;
					font-size: 24rpx;
					font-weight: 400;
				}
			}
		}
		&__step{
			margin-bottom: 30rpx;
			&__item {
				display: flex;
                flex-direction: column;
				&__address{
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
                    margin-bottom: 32rpx;
					&__name{
						font-family: "Alimama FangYuanTi VF";
						color: #000;
						font-size: 26rpx;
						font-weight: 400;
					}
					&__time{
						display: flex;
                        flex-direction: column;
						align-items: center;
						justify-content: center;
						width: 92rpx;
						height: 52rpx;
						border-radius: 10rpx;
						background-color: #d5d7da;
						&__text{
							font-family: "Alimama FangYuanTi VF";
							color: #161616;
							font-size: 24rpx;
							font-weight: 400;
						}
					}
				}
				&__time{
					display: flex;
					flex-direction: row;
					align-items: center;
					width: 168rpx;
					&__hours{
						margin-right: 12rpx;
					}
					&__hours,&__min{
						font-family: "Alimama FangYuanTi VF";
						font-size: 24rpx;
						color: #717680;
						font-weight: 400;
					}
				}
			}
		}
		&__price{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			margin-bottom: 32rpx;
			&__text{
				font-family: "Alimama FangYuanTi VF";
				color: #103A62;
				font-size: 30rpx;
				font-weight: 700;
				margin-right: 6rpx;
			}
			&__desc{
				font-family: "Alimama FangYuanTi VF";
				color: #717680;
				font-size: 30rpx;
				font-weight: 700;
				text-decoration-line: line-through;
			}
		}
		&__btn,&__btn-sold-out{
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			height: 64rpx;
			border-radius: 32rpx;
			padding: 24rpx 28rpx;
			&__text{
				font-family: "Alimama FangYuanTi VF";
				font-size: 28rpx;
				color: #fff;
				font-weight: 400;
			}
		}
		&__btn{
			background-color: #103A62;
		}
		&__btn-sold-out{
			background-color: #91a8be;
		}
        :deep(.wd-step__title){
            display: none;
        }
        :deep(.wd-step.is-vertical .wd-step__line){
            width: 2px;
            transform: scaleX(1);
        }
        :deep(.wd-step.is-process .wd-step__dot){
            background-color: #000;
        }
        :deep(.wd-step__icon.is-dot) {
            width: 8px;
            height: 8px;
            top: -6px;
            background-color: #000;
        }
        // :deep(.wd-step.is-process .wd-step__icon.is-dot::after){
        //     background-color: #000 !important;
        // }
        :deep(.wd-step.is-process .wd-step__icon){
            width: 8px;
            height: 8px;
            background-color: #000 !important;
        }
	}
	.mr{
		margin-right: 6rpx;
	}