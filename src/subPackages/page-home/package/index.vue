<script lang="ts" setup>
import type { BaseSwiperItemType } from '@/model/common'
import { onReachBottom } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetTicketPackageList } from '@/api/home/<USER>'
import { PageInfoEnum } from '@/enum/home'
import scenicSpotCard from './components/scenic-spot-card.vue'

const current = ref(0)
const pageInfo = ref({
  [PageInfoEnum.PAGE_NO]: 1,
  [PageInfoEnum.PAGE_SIZE]: 10,
})
const swiperList = ref<Partial<BaseSwiperItemType>[]>([
  {
    id: 1,
    image: '/static/test/guide-swiper.png',
  },
  {
    id: 2,
    image: '/static/test/guide-swiper.png',
  },
  {
    id: 3,
    image: '/static/test/guide-swiper.png',
  },
])

function back() {
  uni.navigateBack()
}

function handleCheckNotice() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=generalprotocol&name=package_ticket`,
  })
}

async function handleGetTicketPackageList() {
  const params = {
    ...pageInfo.value,
  }
  const data = await ApiGetTicketPackageList(params)
  console.log(data, 'data')
}

handleGetTicketPackageList()

onReachBottom(() => {
  // uni.showLoading()
  pageInfo.value[PageInfoEnum.PAGE_NO] += 1
  handleGetTicketPackageList()
})

function onChange(e: any) {
  current.value = e.detail.current
}
</script>

<template>
  <view class="package-container">
    <nav-bar-page is-custom>
      <view class="package-container__nav-bar-left-container">
        <zui-svg-icon class="package-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <text class="package-container__nav-bar-left-container__from">
          Package
        </text>
        <text class="package-container__nav-bar-left-container__notice" @click="handleCheckNotice">
          More
        </text>
      </view>
    </nav-bar-page>
    <view
      class="package-container__main"
    >
      <view class="package-container__main__loop">
        <BaseSwiper :list="swiperList" />
      </view>
      <scenicSpotCard v-for="item in 2" :key="item" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
