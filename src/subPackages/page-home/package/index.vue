<script lang="ts" setup>
import type { BaseSwiperItemType } from '@/model/common'
import { ref } from 'vue'
import scenicSpotCard from './components/scenic-spot-card.vue'

const current = ref(0)
const swiperList = ref<BaseSwiperItemType[]>([
  {
    id: 1,
    image: '/static/test/guide-swiper.png',
  },
  {
    id: 2,
    image: '/static/test/guide-swiper.png',
  },
  {
    id: 3,
    image: '/static/test/guide-swiper.png',
  },
])

function back() {
  uni.navigateBack()
}

function handleCheckNotice() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=package_ticket`,
  })
}

function onChange(e: any) {
  current.value = e.detail.current
}
</script>

<template>
  <view class="package-container">
    <nav-bar-page is-custom>
      <view class="package-container__nav-bar-left-container">
        <zui-svg-icon class="package-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <text class="package-container__nav-bar-left-container__from">
          Package
        </text>
        <text class="package-container__nav-bar-left-container__notice" @click="handleCheckNotice">
          More
        </text>
      </view>
    </nav-bar-page>
    <view
      class="package-container__main"
    >
      <view class="package-container__main__loop">
        <!-- <swiper style="height: 300rpx" :current="current" :autoplay="true" :interval="1000" @change="onChange">
          <swiper-item>
            <image class="package-container__main__loop__img" src="/static/test/guide-swiper.png" mode="aspectFill" />
          </swiper-item>
          <swiper-item>
            <image class="package-container__main__loop__img" src="/static/test/guide-swiper.png" mode="aspectFill" />
          </swiper-item>
          <swiper-item>
            <image class="package-container__main__loop__img" src="/static/test/guide-swiper.png" mode="aspectFill" />
          </swiper-item>
        </swiper> -->
        <BaseSwiper :list="swiperList" />
      </view>
      <scenicSpotCard v-for="item in 2" :key="item" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
