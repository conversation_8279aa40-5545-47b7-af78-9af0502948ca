<script lang="ts" setup>
import type { ParamsInfo, StationInfo } from '@/model/home/<USER>'
import { onLoad } from '@dcloudio/uni-app'
import { ref, watch } from 'vue'
import { ApiGetRouteList } from '@/api/home/<USER>'
import Empty from '@/components/empty/empty.vue'
import { AddressInfoEnum } from '@/enum/home'
import { debounce } from '@/utils/index'
import { getStore, setStore } from '@/utils/store'

const addressKey = ref('')

const paramsInfo = ref<ParamsInfo>({
  address: '',
  way: 'oneWay',
})

onLoad((params: any) => {
  paramsInfo.value = params
})

onLoad((params: any) => {
  addressKey.value = params.address
})

const list = ref<StationInfo[]>([])

const keyWord = ref('')

function back() {
  uni.navigateBack()
}

function switchMap() {
  uni.navigateTo({
    url: '/subPackages/page-home/map/index',
  })
}

const handleGetSearchRouteList = debounce(async (keyword: string) => {
  const res = await ApiGetRouteList({ keyword })
  list.value = res.data as StationInfo[]
  // console.log(data)
}, 500)

watch(() => keyWord.value, (val: string) => {
  if (!val) {
    list.value = []
  }
  val && handleGetSearchRouteList(val)
})

function handleSelectAddress(stationInfo: StationInfo) {
  console.warn(stationInfo, 'select address')
  const storeAddress = getStore(AddressInfoEnum.ADDRESS_INFO)
  const { way, address } = paramsInfo.value
  const id = `${address}Id`
  if (way === 'transfer') {
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      id: stationInfo.id,
      [address]: stationInfo.name,
    })
    uni.navigateTo({
      url: '/subPackages/page-home/transfer/index',
    })
    return
  }
  if (way === 'mutiCity') {
    if (storeAddress && Array.isArray(storeAddress) && storeAddress.length > 0) {
      const originAddress = storeAddress[paramsInfo.value.idx!]
      storeAddress[paramsInfo.value.idx!] = {
        ...originAddress,
        way,
        [id]: stationInfo.id,
        [address]: stationInfo.name,
      }
      setStore(AddressInfoEnum.ADDRESS_INFO, storeAddress)
    }
    else {
      setStore(AddressInfoEnum.ADDRESS_INFO, [{
        way,
        [id]: stationInfo.id,
        [address]: stationInfo.name,
      }])
    }
  }
  else {
    console.log({
      ...storeAddress,
      way,
      [id]: stationInfo.id,
      [address]: stationInfo.name,
    }, 'storeAddress')
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      [id]: stationInfo.id,
      [address]: stationInfo.name,
    })
  }
  uni.switchTab({
    url: '/pages/page-home/index',
  })
}
</script>

<template>
  <view class="search-address-container">
    <nav-bar-page is-custom :nav-bar-height="108">
      <view class="search-address-container__nav-bar-left-container">
        <zui-svg-icon class="search-address-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
        <!-- <l-svg class="search-address-container__nav-bar-left-container__back-icon" src="/static/icons/arrow-left2-light.svg" @click="back" /> -->
        <text class="search-address-container__nav-bar-left-container__title">
          Select Site
        </text>
        <view class="search-address-container__nav-bar-left-container__transport" @click.stop="switchMap">
          <image class="search-address-container__nav-bar-left-container__transport__icon" src="/static/home/<USER>" />
          <text class="search-address-container__nav-bar-left-container__transport__operate">
            Map
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="search-address-container__content">
      <search-input v-model="keyWord" text="" class="search-input-container" focus placeholder="common.placeholder.search" />
      <view
        class="search-address-container__content__main"
      >
        <view v-if="list.length > 0" class="search-address-container__content__main__list">
          <view
            v-for="(item, idx) in list"
            :key="idx"
            class="search-address-container__content__main__list__item"
            :class="{ 'search-address-container__content__main__list__item__one': list.length === 1 }"
            @click.stop="handleSelectAddress(item)"
          >
            <text class="search-address-container__content__main__list__item__text">
              {{ item.name }}
            </text>
          </view>
        </view>
        <Empty v-else description="The content cannot be found." />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-address-container {
    &__nav-bar-left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__back-icon{
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }
        &__title{
            font-size: 32rpx;
            color: #111b19;
            font-weight: 700;
            margin-right: 12rpx;
        }
        &__transport{
            display: flex;
            flex-direction: row;
            align-items: center;
            &__icon {
                width: 40rpx;
                height: 40rpx;
            }

            &__operate{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 68rpx;
                font-size: 26rpx;
                font-weight: 400;
                color: #103a62;
            }
        }

    }
    &__content {
        padding: 0 32rpx;
        &__main {
            margin-top: 48rpx;
            &__list {
                &__item {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    height: 76rpx;
                    padding: 24rpx;
                    border-bottom: 2rpx solid #eaecf0;
                    &:last-child{
                        border: none;
                    }
                    &__one{
                      border-bottom: 2rpx solid #eaecf0 !important;
                    }
                    &__text{
                        font-family: "Alimama FangYuanTi VF";
                        font-size: 24rpx;
                        font-weight: 500;
                        color: #1d232e;
                    }
                }
            }
        }
    }
}
</style>
