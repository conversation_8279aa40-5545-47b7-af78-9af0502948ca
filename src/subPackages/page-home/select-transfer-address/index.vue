<script lang="ts" setup>
import type { ParamsInfo, TrasnferStationInfo } from '@/model/home/<USER>'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetTransferAddressList } from '@/api/home'
import lineCollapse from '@/subPackages/page-home/components/line-collapse/index.vue'

const transferAddressList = ref<TrasnferStationInfo[]>()

const paramsInfo = ref<ParamsInfo>({
  address: '',
  way: 'transfer',
})

onLoad((params: any) => {
  paramsInfo.value = params
})

function back() {
  uni.navigateBack()
}

async function handleGetTransferAddressList() {
  const data = await ApiGetTransferAddressList()
  transferAddressList.value = data
}

handleGetTransferAddressList()
</script>

<template>
  <view class="select-address-container">
    <nav-bar-page is-custom>
      <view class="select-address-container__nav-bar-left-container">
        <zui-svg-icon class="select-address-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
        <text class="select-address-container__nav-bar-left-container__title">
          {{ $t('home.selectSite') }}
        </text>
      </view>
    </nav-bar-page>
    <view class="select-address-container__content">
      <view
        class="select-address-container__content__main"
      >
        <view class="select-address-container__content__main__line-area">
          <lineCollapse :list="transferAddressList" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.select-address-container {
    &__nav-bar-left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__back-icon{
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }
        &__title{
            font-size: 32rpx;
            color: #111b19;
            font-weight: 700;
            margin-right: 12rpx;
        }

    }
    &__content {
        padding: 0 32rpx;
        &__main {
            margin-top: 48rpx;
            // border: 1px solid red;
        }
    }
}
</style>
