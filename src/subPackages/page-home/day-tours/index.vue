<script lang="ts" setup>
import type { BaseSwiperItemType } from '@/model/common'
import type { DayToursItem } from '@/model/home/<USER>'
import { ref } from 'vue'
import dayToursCard from './components/card.vue'

function back() {
  uni.navigateBack()
}

function handleCheckMore() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=one_daytour`,
  })
}

const swiperList = ref<BaseSwiperItemType[]>([
  {
    id: 1,
    image: '/static/home/<USER>',
  },
  {
    id: 1,
    image: '/static/home/<USER>',
  },
  {
    id: 1,
    image: '/static/home/<USER>',
  },
])

const list = ref<DayToursItem[]>([
  {
    img: '/static/test/guide-swiper.png',
    desc: 'Spring trip - Shenzhen Dameisha sea view and picnic, the most beautiful coastal trip',
    tags: ['Romantic sea view1', 'week trip', 'Plan one'],
    price: 41,
    discount: 5,
  },
  {
    img: '/static/test/guide-swiper.png',
    desc: 'Spring trip - Shenzhen Dameisha sea view and picnic, the most beautiful coastal trip',
    tags: ['Romantic sea view2', 'Day trip', 'Plan two'],
    price: 31,
    discount: 7,
  },
  {
    img: '/static/test/guide-swiper.png',
    desc: 'Spring trip - Shenzhen Dameisha sea view and picnic, the most beautiful coastal trip',
    tags: ['Romantic sea view', 'Day trip', 'Plan'],
    price: 21,
    discount: 10,
  },
])
</script>

<template>
  <view class="day-tours-container">
    <nav-bar-page is-custom>
      <view class="day-tours-container__nav-bar-left-container">
        <zui-svg-icon class="day-tours-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <!-- <l-svg class="day-tours-container__nav-bar-left-container__back-icon" src="/static/icons/arrow-left2-light.svg" @click="back" /> -->
        <text class="day-tours-container__nav-bar-left-container__title">
          Day tours
        </text>
        <text class="day-tours-container__nav-bar-left-container__more" @click.stop="handleCheckMore">
          More
        </text>
      </view>
    </nav-bar-page>
    <view
      class="day-tours-container__main"
    >
      <view class="day-tours-container__main__loop">
        <base-swiper :list="swiperList" />
      </view>
      <view class="day-tours-container__main__card">
        <!-- <dayToursCard v-for="item in 4" :key="item" /> -->
        <dayToursCard v-for="(item, idx) in list" :key="idx" :data="item" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
