<script lang="ts" setup>
import detailCard from './components/detail-card.vue'

function handleShare() {

}

function handlePreviewImage() {
  // uni.previewImage({
  // 	urls:['/static/test/view-detail-content.png']
  // })
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/day-order-fill-out/index',
  })
}
</script>

<template>
  <view class="day-tours-detail">
    <nav-bar-page title="Details">
      <!-- #ifdef H5 -->
      <template #right>
        <image class="day-tours-detail__nav-bar-share" src="/static/home/<USER>" mode="aspectFill" @click.stop="handleShare" />
      </template>
      <!-- #endif -->
    </nav-bar-page>
    <view
      class="day-tours-detail__main"
    >
      <detailCard />
      <view class="day-tours-detail__main__desc-wrap">
        <text class="day-tours-detail__main__desc-wrap__text">
          Day trip introduction
        </text>
        <image class="day-tours-detail__main__desc-wrap__img" src="/static/test/view-detail-content.png" mode="aspectFill" @click="handlePreviewImage" />
      </view>
      <view v-if="true" class="day-tours-detail__main__buy" @click.stop="handleBuyTicket">
        <text class="day-tours-detail__main__buy__text">
          Buy tickets
        </text>
      </view>
      <view v-else class="day-tours-detail__main__buy day-tours-detail__main__buy--disabled">
        <text class="day-tours-detail__main__buy__text">
          Buy tickets
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './detail.scss'
</style>
