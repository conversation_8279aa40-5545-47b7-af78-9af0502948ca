<script lang="ts" setup>
import type { DayToursItem } from '@/model/home/<USER>'
// import { ref } from 'vue'

defineProps<{ data?: DayToursItem }>()
// const tagList = ref(['Romantic sea view', 'Day trip', 'Plan'])

function handleCheckDetail() {
  uni.navigateTo({
    url: '/subPackages/page-home/day-tours/detail',
  })
}
</script>

<template>
  <view class="day-tours-card-container">
    <image class="day-tours-card-container__img" :src="data?.img" mode="aspectFill" />
    <!-- <image class="day-tours-card-container__img" :src="data?.img" mode="aspectFill"></image> -->
    <view class="day-tours-card-container__content">
      <text class="day-tours-card-container__content__desc">
        {{ data?.desc }}
      </text>
      <view class="day-tours-card-container__content__tag">
        <view v-for="item in data?.tags" :key="item" class="day-tours-card-container__content__tag__item">
          <text class="day-tours-card-container__content__tag__item__text">
            {{ item }}
          </text>
        </view>
      </view>
      <view class="day-tours-card-container__content__price">
        <text class="day-tours-card-container__content__price__value">
          {{ data?.price }} NZD
        </text>
        <text class="day-tours-card-container__content__price__label">
          /person
        </text>
        <base-discount :discount="data?.discount" :dstyle="{ position: 'static', width: '116rpx', height: '52rpx' }" />
      </view>
      <view class="day-tours-card-container__content__btn" @click.stop="handleCheckDetail">
        <text class="day-tours-card-container__content__btn__text">
          Details
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './card.scss'
</style>
