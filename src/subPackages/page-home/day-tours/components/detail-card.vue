<script lang="ts" setup>
import { ref } from 'vue'

defineProps<{ data?: any }>()
const tagList = ref(['Romantic sea view', 'Day trip', 'Plan'])

function handleCheckDetail() {
  uni.navigateTo({
    url: '/subPackages/page-home/day-tours/detail',
  })
}
</script>

<template>
  <view class="day-tours-detail-card-container">
    <image class="day-tours-detail-card-container__img" src="/static/test/guide-swiper.png" mode="aspectFill" />
    <!-- <image class="day-tours-detail-card-container__img" :src="data?.img" mode="aspectFill"></image> -->
    <view class="day-tours-detail-card-container__content">
      <text class="day-tours-detail-card-container__content__desc">
        Spring trip - Shenzhen Dameisha sea view and picnic, the most beautiful coastal trip
      </text>
      <view class="day-tours-detail-card-container__content__tag">
        <view v-for="item in tagList" :key="item" class="day-tours-detail-card-container__content__tag__item">
          <text class="day-tours-detail-card-container__content__tag__item__text">
            {{ item }}
          </text>
        </view>
      </view>
      <view class="day-tours-detail-card-container__content__price-wrap">
        <view class="day-tours-detail-card-container__content__price-wrap__item">
          <text class="day-tours-detail-card-container__content__price-wrap__item__value">
            {{ `Adult: 21 NZD` }}
          </text>
          <text class="day-tours-detail-card-container__content__price-wrap__item__label">
            /person
          </text>
        </view>
        <view class="day-tours-detail-card-container__content__price-wrap__item">
          <text class="day-tours-detail-card-container__content__price-wrap__item__value">
            {{ `Children: 21 NZD` }}
          </text>
          <text class="day-tours-detail-card-container__content__price-wrap__item__label">
            /person
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './detail-card.scss'
</style>
