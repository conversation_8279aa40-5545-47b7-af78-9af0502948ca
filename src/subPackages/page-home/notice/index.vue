<script setup lang="ts">
import type { NoticeInfo, NoticeType } from '@/model/common'
import { onLoad } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'
import { ApiGetNoticeInfo } from '@/api/common'
import { ApiGetDayToursDetail } from '@/api/day-tours'

withDefaults(defineProps<{ title?: string }>(), {
  title: 'notice',
})

interface ParamsInfo {
  type: NoticeType
  detail?: string
}

const paramsInfo = ref<ParamsInfo>({
  type: 'privacy_policy',
})

onLoad((params: any) => {
  paramsInfo.value = params
})

const noticeInfo = ref<NoticeInfo>({
  name: '',
  content: '',
  type: 'privacy_policy',
})

async function handleGetNoticeInfo() {
  const data = await ApiGetNoticeInfo(paramsInfo.value.type)
  noticeInfo.value = data
}

onMounted(() => {
  paramsInfo.value.type && handleGetNoticeInfo()
})
</script>

<template>
  <page>
    <NavBarPage :title="noticeInfo.name" />
    <view class="notice-container">
      <NoticeList :data="noticeInfo.content" />
    </view>
  </page>
</template>

<style lang="scss" scoped>
.notice-container {
    padding: 24rpx 32rpx;
}
</style>
