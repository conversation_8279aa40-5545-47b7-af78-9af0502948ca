<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetSearchTicketList } from '@/api/home'
import { AddressInfoEnum } from '@/enum/home'
import busInfo from '@/subPackages/page-home/components/bus-info/index.vue'
import { getStore } from '@/utils/store'
import busTip from './components/bus-tip.vue'

const date = ref<number | null>(null)
const addressInfo = ref <Partial <AddressInfo>> ({
  depart: '',
  arrive: '',
})
function back() {
  uni.navigateBack()
}

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
  date.value = addressInfo.value.startTime!
  handleGetSearchTicketList()
  console.log(addressInfo.value, date.value, 'date.value')
})

function handleCheckNotice() {
  uni.navigateTo({
    url: `/subPackages/page-home/notice/index?type=ticket_instructions`,
  })
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/buy-ticket/index',
  })
}

async function handleGetSearchTicketList() {
  const params = {
    trips: [{
      from_id: addressInfo.value.departId,
      to_id: addressInfo.value.arriveId,
      date: addressInfo.value.startTime! / 1000,
    }],
  }
  const data = await ApiGetSearchTicketList(params)
  console.log(data, 'data')
}
</script>

<template>
  <view class="search-ticket-container">
    <nav-bar-page is-custom>
      <view class="search-ticket-container__nav-bar-left-container">
        <zui-svg-icon class="search-ticket-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
        <text class="search-ticket-container__nav-bar-left-container__from">
          {{ addressInfo.depart }}
        </text>
        <text class="search-ticket-container__nav-bar-left-container__arrow">
          >
        </text>
        <text class="search-ticket-container__nav-bar-left-container__to">
          {{ addressInfo.arrive }}
        </text>
        <text class="search-ticket-container__nav-bar-left-container__notice" @click="handleCheckNotice">
          Notice
        </text>
      </view>
    </nav-bar-page>
    <view
      class="search-ticket-container__main"
    >
      <view class="search-ticket-container__main__calendar">
        <BaseCalendar v-model="date" prop="startTime" />
      </view>
      <view class="search-ticket-container__main__bus-info-content">
        <busInfo :list="[]" />
        <busTip />
      </view>
    </view>
    <view class="search-ticket-container__buy">
      <view class="search-ticket-container__buy__btn" @click="handleBuyTicket">
        <text class="search-ticket-container__buy__btn__text">
          Buy ticket
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-ticket-container{
    &__nav-bar-left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__back-icon{
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }

        &__arrow{
            margin: 0 8rpx;
        }
        &__to{
            margin-right: 16rpx;
        }
        &__from, &__arrow, &__to {
            font-family: "Alimama FangYuanTi VF";
            font-size: 32rpx;
            color: #111B19;
            font-weight: 600;
        }
        &__notice{
            font-size: 26rpx;
            text-decoration-line: underline;
            color: #103a62;
        }
    }
    &__main{
        // border: 1px solid red;
        padding-bottom: 112rpx;
        &__calendar{
            padding: 0 32rpx;
            margin-bottom: 50rpx;
        }
        &__bus-info-content{
            padding: 0 32rpx;
        }
    }
    &__buy{
        position: fixed;
        left: 0;
        bottom: 0;
        width: 750rpx;
        height: 112rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        padding: 24rpx 28rpx;
        &__btn{
            width: 660rpx;
            height: 84rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #103a62;
            border-radius: 24rpx;
            &__text{
                font-family: "Alimama FangYuanTi VF";
                color: #fff;
                font-size: 28rpx;
            }
        }
    }
}
</style>
