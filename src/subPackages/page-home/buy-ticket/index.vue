<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import type { TicketInfo } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import payCard from '@/subPackages/page-home/components/ticket-info/pay-card.vue'
import ticketInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import ticketRadio from '@/subPackages/page-home/components/ticket-info/ticket-radio.vue'
import { getStore } from '@/utils/store'
import oneTravelInfo from './components/one-travel-info.vue'
import popupDetail from './components/popup-detail.vue'
import popupTimeTable from './components/popup-time-table.vue'

const showDetail = ref(false)
const showTimeTable = ref(false)
const ticketInfo = ref<TicketInfo>({})
const addressInfo = ref<AddressInfo>({
  depart: '',
  arrive: '',
})

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
})

function handleCheckDetail() {
  showDetail.value = true
}

function back() {
  uni.navigateBack()
}

function handlePreviewTimeTable() {
  showTimeTable.value = true
}

function handlePayment() {

}
</script>

<template>
  <view class="buy-ticket-container">
    <nav-bar-page is-custom>
      <view class="buy-ticket-container__nav-bar-left-container">
        <zui-svg-icon class="buy-ticket-container__nav-bar-left-container__back-icon" width="40rpx" height="40rpx" icon="arrow-left2-light" @click="back" />
        <text class="buy-ticket-container__nav-bar-left-container__from">
          {{ addressInfo.depart }}
        </text>
        <text class="buy-ticket-container__nav-bar-left-container__arrow">
          >
        </text>
        <text class="buy-ticket-container__nav-bar-left-container__to">
          {{ addressInfo.arrive }}
        </text>
      </view>
    </nav-bar-page>
    <view
      class="buy-ticket-container__content"
    >
      <oneTravelInfo @preview-time-table="handlePreviewTimeTable" />
      <view class="buy-ticket-container__content__line" />
      <selectPassenger v-model="ticketInfo.adult" />
      <selectPassenger v-model="ticketInfo.children">
        <template #left>
          <view class="buy-ticket-container__content__children">
            <image class="buy-ticket-container__content__children__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-ticket-container__content__children__text">
              <text class="buy-ticket-container__content__children__text__title">
                Children
              </text>
              <text class="buy-ticket-container__content__children__text__desc">
                (3-12Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <selectPassenger v-model="ticketInfo.baby">
        <template #left>
          <view class="buy-ticket-container__content__baby">
            <image class="buy-ticket-container__content__baby__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="buy-ticket-container__content__baby__text">
              <text class="buy-ticket-container__content__baby__text__title">
                Baby
              </text>
              <text class="buy-ticket-container__content__baby__text__desc">
                (0-2Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <view class="buy-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.phoneNumber" />
      </view>
      <view class="buy-ticket-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.email" label="Buyer E-mail" placeholder="zer0126@.com" />
      </view>
      <view class="buy-ticket-container__content__reserve">
        <text class="buy-ticket-container__content__reserve__label">
          Booking fee (default selected)
        </text>
        <text class="buy-ticket-container__content__reserve__value">
          1 NZD
        </text>
      </view>
      <ticketRadio v-model="ticketInfo.rebookChecked" label="Flexi Price" show-tip />
      <ticketRadio v-model="ticketInfo.deductionChecked" label="Points deduction" />
    </view>
    <payCard @check-detail="handleCheckDetail" @pay="handlePayment" />
    <popupDetail v-model:visible="showDetail" />
    <popupTimeTable v-model="showTimeTable" />
  </view>
</template>

<style lang="scss" scoped>
.buy-ticket-container{
    &__nav-bar-left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__back-icon{
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }

        &__arrow{
            margin: 0 8rpx;
        }
        &__from, &__arrow, &__to {
            font-size: 32rpx;
        }
    }
    &__content {
        padding: 0 32rpx 24rpx;
        &__line{
            height: 4rpx;
            background-color: #f5f6f6;
            margin-bottom: 32rpx;
        }
        &__children, &__baby {
            display: flex;
            flex-direction: row;
            align-items: center;
            &__icon {
                width: 40rpx;
                height: 40rpx;
                margin-right: 20rpx;
            }
            &__text {
                display: flex;
                flex-direction: row;
                &__title {
                    margin-right: 12rpx;
                }
                &__title, &__desc {
                    font-family: "Alimama FangYuanTi VF";
                    font-size: 28rpx;
                    color: #161616;
                    font-weight: 700;
                }
            }
        }
        &__ticket-input{
            margin-bottom: 32rpx;
        }
        &__reserve, &__rebook{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 24rpx;
            height: 90rpx;
            background-color: #fafafa;
            border-radius: 24rpx;
            margin-bottom: 32rpx;
            &__label, &__value{
                font-family: "Alimama FangYuanTi VF";
                font-size: 26rpx;
                color: #161616;
            }

            &__label{
                font-weight: 700;
            }
                &__value{
                    font-weight: 400;
                }
        }
        &__rebook{
            &__text{
                display: flex;
                flex-direction: row;
                align-items: center;
                &__val {
                    font-size: 40rpx;
                    margin-right: 12rpx;
                    font-weight: 400;
                    color: #103a62;
                }
                &__unit{
                    font-size: 26rpx;
                    color: #103a62;
                }
            }
        }
    }
}
</style>
