<script setup lang="ts">
import type { TicketTimetableItem } from '@/model/home/<USER>'
import { reactive, ref } from 'vue'

withDefaults(defineProps<{ data: TicketTimetableItem[] }>(), {
  data: () => [
    {
      name: 'site',
      arrive_time: '8:00',
      major: '8:00',
      stay: '2min',
      is_meal: true,
    },
    {
      name: 'site',
      arrive_time: '8:00',
      departure_time: '8:00',
      stay_time: '3min',
    },
  ],
})

const visible = defineModel({
  default: false,
})

const coloumns = ref([
  {
    prop: 'name',
    label: 'Stopover station',
    width: '212rpx',
    align: 'center',
    slot: 'site',
  },
  {
    prop: 'arrive_time',
    label: 'Arrive',
    width: '156rpx',
    align: 'center',
  },
  {
    prop: 'departure_time',
    label: 'Departure',
    width: '174rpx',
    align: 'center',
  },
  {
    prop: 'stay_time',
    label: 'Stay',
    width: '156rpx',
    align: 'center',
  },
])

const dataList = reactive([
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '2min',
    food: true,
  },
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '3min',
  },
  {
    name: 'site',
    school: '8:00',
    major: '8:00',
    stay: '1min',
  },
])
</script>

<template>
  <BasePopup v-model="visible" :style="{ padding: '40rpx 24rpx', height: '100%' }" title="Time Table">
    <BaseTable style="margin-top: 32rpx;" :data :coloumns="coloumns">
      <template #site="{ row }">
        <view class="site-column">
          <text class="site-column__text">
            {{ row.name }}
          </text>
          <image v-if="row.is_meal" class="site-column__food-icon" src="/static/home/<USER>" mode="aspectFill" />
        </view>
      </template>
    </BaseTable>
  </BasePopup>
</template>

<style lang="scss" scoped>
.popup-time-table-container {
  padding: 40rpx;
  border: 1px solid red;
}
.site-column{
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    &__text{
        margin-right: 34rpx;
    }
    &__food-icon{
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -26rpx;
        width: 28rpx;
        height: 28rpx;
}
}
</style>
