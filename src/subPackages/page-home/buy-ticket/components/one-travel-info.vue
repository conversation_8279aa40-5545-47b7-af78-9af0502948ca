<script lang="ts" setup>
const emit = defineEmits<{
  (e: 'previewTimeTable'): void
}>()
function handlePreviewTimeTable() {
  emit('previewTimeTable')
}
</script>

<template>
  <view class="one-travel-info-container">
    <view class="one-travel-info-container__left">
      <text class="one-travel-info-container__left__info">
        (ABC4567) 07/08 Tue 08:00 Departure (about 3 hours)
      </text>
      <view class="one-travel-info-container__left__destination">
        <text class="one-travel-info-container__left__destination__from">
          Shenzhen S.
        </text>
        <text class="one-travel-info-container__left__destination__arrow">
          >
        </text>
        <text class="one-travel-info-container__left__destination__to">
          Shenzhen N.
        </text>
      </view>
    </view>
    <view class="one-travel-info-container__right" @click.stop="handlePreviewTimeTable">
      <image class="one-travel-info-container__right__time-icon" src="/static/home/<USER>" mode="aspectFill" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.one-travel-info-container{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    padding: 16rpx 12rpx;
    &__left{
        width: 592rpx;
        margin-right: 10rpx;
        &__info{
            font-family: "Alimama FangYuanTi VF";
            font-size: 26rpx;
            font-weight: 400;
            line-height: 48rpx;
            margin-bottom: 10rpx;
        }
        &__destination {
            display: flex;
            flex-direction: row;
            align-items: center;
            &__arrow{
                margin: 8rpx;
            }

            &__from, &__arrow, &__to {
                font-size: 30rpx;
                font-weight: 700;
            }
        }
    }
    &__right{
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 60rpx;
        width: 60rpx;
        height: 60rpx;
        background-color: #edf1f6;
        &__time-icon{
            width: 40rpx;
            height: 40rpx;
        }
    }
}
</style>
