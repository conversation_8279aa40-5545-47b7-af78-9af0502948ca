<script lang="ts" setup>
const visible = defineModel('visible', {
  default: false,
})

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="bottom" :z-index="9999" custom-style="border-radius:40rpx 40rpx 0 0;">
    <view class="popup-detail-container__content">
      <view class="popup-detail-container__content__header">
        <text class="popup-detail-container__content__header__title">
          Price details
        </text>
      </view>
      <view class="popup-detail-container__content__body">
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Ticket price:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            20 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Booking fee:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            2 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Point deduction:
          </text>
          <text class="popup-detail-container__content__body__item__value">
            -1 NZD
          </text>
        </view>
        <view class="popup-detail-container__content__body__item">
          <text class="popup-detail-container__content__body__item__label">
            Total:
          </text>
          <text class="popup-detail-container__content__body__item__value total">
            21 NZD
          </text>
        </view>
      </view>
      <view class="popup-detail-container__content__close" @click.stop="handleClose">
        <zui-svg-icon width="40rpx" height="40rpx" icon="cancel-light" />
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.popup-detail-container__content{
    position: relative;
    padding: 40rpx 24rpx  56rpx;
    &__header{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 52rpx;
        margin-bottom: 32rpx;
        &__title{
            font-family: "Alimama FangYuanTi VF";
            font-weight: 700;
            font-size: 32rpx;
            color: #111b19;
        }
    }
    &__body{
        width: 702rpx;
        padding: 32rpx 32rpx 0;
        &__item{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32rpx;
            &__label{
                font-family: "Alimama FangYuanTi VF";
                color:'#161616';
                font-weight: 700;
                font-size: 28rpx;
            }
            &__value{
                font-family: "Alimama FangYuanTi VF";
                font-weight: 400;
                color: #717680;
                font-size: 28rpx;
            }
        }
    }
    &__close{
        position: absolute;
        top: 46rpx;
        right: 46rpx;
    }
}
.total {
    font-weight: 700;
    color: #912018;
}
</style>
