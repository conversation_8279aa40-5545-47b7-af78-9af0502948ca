<script lang="ts" setup>
import { ref } from 'vue'
import busInfo from '@/subPackages/page-home/components/bus-info/index.vue'
import previewTrip from './preview-trip.vue'

withDefaults(defineProps<{
  title?: string
  list?: any[]
}>(), {
  title: 'Outbound',
  list: () => [
    {
      time: '08:00',
      busNumber: 'ABC123',
      desc: '31 min, 5 tickets left',
      adultPrice: '21NZD',
      childPrice: '16NZD',
      discount: 15,
    },
  ],
})

const date = ref(null)

defineExpose({
  date,
})
</script>

<template>
  <view class="trip-card-container">
    <previewTrip :title="title" />
    <view class="trip-card-container__calendar">
      <BaseCalendar v-model="date" />
    </view>
    <view class="trip-card-container__bus-info-outbound">
      <busInfo :show-expand="true" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './trip-card.scss';
</style>
