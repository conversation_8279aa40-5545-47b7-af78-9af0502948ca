<script lang="ts" setup>
withDefaults(defineProps<{ title?: string }>(), {
  title: 'Outbound',
})

function handleCheckMap() {

}
</script>

<template>
  <view class="preview-trip-container" @click="handleCheckMap">
    <text class="preview-trip-container__text">
      {{ title }}
    </text>
    <zui-svg-icon class="preview-trip-container__icon" width="40rpx" height="40rpx" color="#103a62" icon="show-light" />
  </view>
</template>

<style lang="scss" scoped>
@use './preview-trip.scss';
</style>
