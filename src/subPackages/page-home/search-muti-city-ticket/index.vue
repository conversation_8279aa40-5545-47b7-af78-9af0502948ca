<script lang="ts" setup>
import type { TicketInfo } from '@/model/ticket-info'
import { ref } from 'vue'
import tripCard from './components/trip-card.vue'

const tripCardRef = ref(null)
const ticketList = ref<TicketInfo[]>([
  {
    title: 'First trip (Shenzhen N. > Shenzhen S.)',
    busList: [],
  },
  {
    title: 'Second trip (Shenzhen N. > Shenzhen S.)',
    busList: [],
  },
])

function back() {
  uni.navigateBack()
}

function handleCheckNotice() {
  uni.navigateTo({
    url: '/subPackages/page-home/notice/index',
  })
}

interface TripCard {
  date: string
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/buy-muti-city-ticket/index',
  })
}
</script>

<template>
  <view class="search-round-trip-container">
    <nav-bar-page is-custom>
      <view class="search-round-trip-container__nav-bar-left-container">
        <zui-svg-icon class="search-round-trip-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <text class="search-round-trip-container__nav-bar-left-container__from">
          Select Site
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__notice" @click.stop="handleCheckNotice">
          Notice
        </text>
      </view>
    </nav-bar-page>
    <view
      class="search-round-trip-container__main"
    >
      <view v-for="item in ticketList" :key="item.title" class="search-round-trip-container__main__ticket">
        <tripCard ref="tripCardRef" :title="item.title" :list="item.busList" />
      </view>
    </view>
    <view class="search-round-trip-container__buy">
      <view class="search-round-trip-container__buy__btn" @click.stop="handleBuyTicket">
        <text class="search-round-trip-container__buy__btn__text">
          Buy ticket
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss';
</style>
