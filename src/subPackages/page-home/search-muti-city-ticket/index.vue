<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import type { TicketInfo } from '@/model/ticket-info'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum, SequenceEnum } from '@/enum/home'
import { getStore } from '@/utils/store'
import tripCard from './components/trip-card.vue'

const tripCardRef = ref(null)
const addressInfo = ref()
const ticketList = ref<TicketInfo[]>([
  {
    title: 'First trip (Shenzhen N. > Shenzhen S.)',
    date: null,
    busList: [],
  },
  // {
  //   title: 'Second trip (Shenzhen N. > Shenzhen S.)',
  //   busList: [],
  // },
])

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
  addressInfo.value.length && addressInfo.value.forEach((item: AddressInfo, idx: number) => {
    ticketList.value[idx] = {
      title: `${SequenceEnum[idx]} trip (${item.depart} > ${item.arrive})`,
      date: item.startTime,
      busList: [],
    }
  })
  // handleGetSearchTicketList()
  console.log(addressInfo.value, ticketList.value, 'muti-city-date.value')
})

function back() {
  uni.navigateBack()
}

function handleCheckNotice() {
  uni.navigateTo({
    url: '/subPackages/page-home/notice/index',
  })
}

interface TripCard {
  date: string
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/buy-muti-city-ticket/index',
  })
}
</script>

<template>
  <view class="search-round-trip-container">
    <nav-bar-page is-custom>
      <view class="search-round-trip-container__nav-bar-left-container">
        <zui-svg-icon class="search-round-trip-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" icon="arrow-left2-light" @click.stop="back" />
        <text class="search-round-trip-container__nav-bar-left-container__from">
          Select Site
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__notice" @click.stop="handleCheckNotice">
          Notice
        </text>
      </view>
    </nav-bar-page>
    <view
      class="search-round-trip-container__main"
    >
      <view v-for="(item, idx) in ticketList" :key="item.title" class="search-round-trip-container__main__ticket">
        <tripCard ref="tripCardRef" :title="item.title" :idx :date="item.date" :list="item.busList" />
      </view>
    </view>
    <view class="search-round-trip-container__buy">
      <view class="search-round-trip-container__buy__btn" @click.stop="handleBuyTicket">
        <text class="search-round-trip-container__buy__btn__text">
          Buy ticket
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss';
</style>
