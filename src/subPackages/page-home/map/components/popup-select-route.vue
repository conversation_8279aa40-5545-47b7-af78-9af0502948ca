<script lang="ts" setup>
import { reactive, ref } from 'vue'

withDefaults(defineProps<{ list: any[] }>(), {
  list: () => [],
})

const emit = defineEmits<{
  (e: 'change', data: RouteItem): void
}>()

export interface RouteItem {
  name: string
  color: string
  id: number
}

const visible = defineModel('visible', {
  default: false,
})

const selectIdx = ref(0)
// const routeList = reactive<RouteItem[]>([
//   {
//     name: 'East line ZQN-DUN-CHC1',
//     color: '#fafafa',
//   },
//   {
//     name: 'East line ZQN-DUN-CHC2',
//     color: '#edf1f6',
//   },
//   {
//     name: 'East line ZQN-DUN-CHC3',
//     color: '#f6f6ed',
//   },
//   {
//     name: 'East line ZQN-DUN-CHC4',
//     color: '#edf6f0',
//   },
// ])

function handleSelectRoute(item: RouteItem, idx: number) {
  emit('change', item)
  selectIdx.value = idx
  visible.value = false
}

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="bottom" :z-index="9999" custom-style="border-radius: 40rpx 40rpx 0 0;">
    <view class="popup-select-route-container">
      <view class="popup-select-route-container__header">
        <text class="popup-select-route-container__header__title">
          Select route
        </text>
      </view>
      <view class="popup-select-route-container__body">
        <view
          v-for="(item, idx) in list"
          :key="idx"
          class="popup-select-route-container__body__item"
          :style="{
            'background-color': item.color,
          }"
          @click.stop="handleSelectRoute(item, idx)"
        >
          <view class="popup-select-route-container__body__item__left">
            <image
              class="popup-select-route-container__body__item__left__icon"
              :style="{
                'background-color': item.color,
              }"
              src="/static/home/<USER>"
            />
            <text class="popup-select-route-container__body__item__left__text">
              {{ item.name }}
            </text>
          </view>
          <image v-show="selectIdx === idx" class="popup-select-route-container__body__item--active" src="/static/home/<USER>" mode="aspectFill" />
        </view>
      </view>
      <view class="popup-select-route-container__close" @click.stop="handleClose">
        <zui-svg-icon width="40rpx" height="40rpx" icon="cancel-light" />
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@use './popup-select-route.scss';
</style>
