<script lang="ts" setup>
const emit = defineEmits<{
  (e: 'change'): void
}>()

const model = defineModel<string>({
  default: 'Ease line ZQN-DUN-CHC',
})

function handleChange() {
  emit('change')
}
</script>

<template>
  <view class="select-route-container" @click.stop="handleChange">
    <view class="select-route-container__left">
      <zui-svg-icon class="select-route-container__left__route-icon" width="40rpx" height="40rpx" icon="number-of-steps-light" />
      <text class="select-route-container__left__text">
        {{ model }}
      </text>
      <zui-svg-icon class="select-route-container__left__arrow" width="40rpx" height="36rpx" icon="arrow-down2-light" />
    </view>
    <zui-svg-icon class="select-route-container__right" width="48rpx" height="48rpx" icon="share-light" />
  </view>
</template>

<style lang="scss" scoped>
@use './select-route.scss';
</style>
