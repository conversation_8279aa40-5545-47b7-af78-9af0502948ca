<script lang="ts" setup>
import type { DetailStationInfo } from '@/model/home/<USER>'
import { ref } from 'vue'

withDefaults(defineProps<{ list?: DetailStationInfo[] }>(), {
  list: () => [
    {
      id: 1,
      image: '/static/home/<USER>',
      name: 'OCT',
      type: 'Temple',
      route_type: 'Bus',
      is_meal: true,
    },
  ],
})

interface AttractionItem {
  title: string
  food: boolean
  desc: string
  img: string
  min: string
  km: string
}

const attractionList = ref<AttractionItem []>([
  {
    title: 'OCT',
    food: false,
    desc: 'Temple｜Nanshan District',
    img: '/static/home/<USER>',
    min: '14',
    km: '20',
  },
  {
    title: 'OC',
    food: true,
    desc: 'Temple｜Nanshan District',
    img: '/static/home/<USER>',
    min: '18',
    km: '24',
  },
  {
    title: 'OT',
    food: true,
    desc: 'Temple｜Nanshan District',
    img: '/static/home/<USER>',
    min: '14',
    km: '20',
  },
  {
    title: 'OCT',
    food: false,
    desc: 'Temple｜Nanshan District',
    img: '/static/home/<USER>',
    min: '16',
    km: '21',
  },
])

const current = ref(-1)
</script>

<template>
  <view class="card-step-container">
    <wd-steps
      v-model="current"
      dot
      vertical
    >
      <wd-step v-for="(item, idx) in list" :key="idx">
        <template #description>
          <view class="card-step-container__item">
            <view class="card-step-container__item__wrap">
              <view class="card-step-container__item__wrap__left">
                <view class="card-step-container__item__wrap__left__title">
                  <view class="card-step-container__item__wrap__left__title__number">
                    <text class="card-step-container__item__wrap__left__title__number__text">
                      {{ idx + 1 }}
                    </text>
                  </view>
                  <text class="card-step-container__item__wrap__left__title__text">
                    {{ item.name }}
                  </text>
                  <image v-if="item?.is_meal" class="card-step-container__item__wrap__left__title__icon" src="/static/home/<USER>" mode="aspectFill" />
                </view>
                <text class="card-step-container__item__wrap__left__desc">
                  {{ `${item.type} | ${item.route_type}` }}
                </text>
              </view>
              <view class="card-step-container__item__wrap__right">
                <image class="card-step-container__item__wrap__right__img" :src="item.image" alt="" mode="aspectFill" />
              </view>
            </view>
            <view v-if="item.steer_time || item.distance" class="card-step-container__item__distance">
              <view class="card-step-container__item__distance__time">
                <text class="card-step-container__item__distance__time__num">
                  {{ item.steer_time }}
                </text>
                <text class="card-step-container__item__distance__time__unit">
                  ,
                </text>
              </view>
              <view class="card-step-container__item__distance__km">
                <text class="card-step-container__item__distance__km__num">
                  {{ item.distance }}
                </text>
                <text class="card-step-container__item__distance__km__unit" />
              </view>
            </view>
          </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
</template>

<style lang="scss" scoped>
@use './card-step.scss';
</style>
