.card-step-container{
		margin-bottom: 32rpx;
		&__item{
			&__wrap {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				height: 136rpx;
				background-color: #fafafa;
				padding: 24rpx;
				border-radius: 24rpx;
				&__left{
					display: flex;
                    flex-direction: column;
					justify-content: space-between;
					width: 296rpx;
					&__title{
						display: flex;
						flex-direction: row;
						align-items: center;
						&__number{
							display: flex;
                            flex-direction: column;
							align-items: center;
							justify-content: center;
							width: 40rpx;
							height: 40rpx;
							border-radius: 40rpx;
							background-color: #103a62;
							margin-right: 18rpx;
							&__text{
								font-size: 22rpx;
								color: #fff;
								font-weight: 700;
							}
						}
						&__text{
							font-family: "Alimama FangYuanTi VF";
							color: #000;
							font-size: 24rpx;
							font-weight: 700;
							margin-right: 16rpx;
						}
						&__icon{
							width: 28rpx;
							height: 28rpx;
						}
					}
					&__desc{
						font-size: 24rpx;
					}
				}
				&__right{
					display: flex;
					flex-direction: row;
					align-items: center;
					&__img{
						width: 140rpx;
						height: 80rpx;
					}

				}
			}
			&__distance{
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				width: 332rpx;
				height: 60rpx;
				border: 2rpx solid #d5d7da;
				border-radius: 20rpx;
				margin-top: 32rpx;
				&__time,&__km{
					display: flex;
					flex-direction: row;
					&__num{
						font-size: 26rpx;
						color: #161616;
					}
					&__unit{
						font-size: 26rpx;
						color: #717680;
						margin-left: 6rpx;
					}
				}
				&__km{
					margin-left: 16rpx;
				}
			}
		}
        :deep(.wd-step__title){
            display: none;
        }
        :deep(.wd-step.is-vertical .wd-step__line){
            width: 2px;
            transform: scaleX(1);
        }
        :deep(.wd-step.is-process .wd-step__dot){
            background: #000;

        }
        :deep(.wd-step__icon.is-dot) {
            width: 8px;
            height: 8px;
            top: -6px;
            background-color: #000;
        }
        :deep(.wd-step.is-process .wd-step__icon){
            width: 8px;
            height: 8px;
            background-color: #000;
        }
        
	}