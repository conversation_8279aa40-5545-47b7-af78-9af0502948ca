<script lang="ts" setup>
import type { DetailRoutteInfo } from '@/model/home/<USER>'

withDefaults(defineProps<{ data?: Partial<DetailRoutteInfo> }>(), {

})
</script>

<template>
  <view class="map-container">
    <view class="map-container__title">
      <text class="map-container__title__text map-container__title__from">
        {{ data?.start_station }}
      </text>
      <text class="map-container__title__text map-container__title__arrow">
        >
      </text>
      <text class="map-container__title__text map-container__title__to">
        {{ data?.end_station }}
      </text>
    </view>
    <view class="map-container__desc">
      <text class="map-container__desc__text">
        <!-- The total distance is 198 km and the estimated time is 2 hours and 46 minutes. -->
        {{ data?.route_detail }}
      </text>
    </view>
    <view class="map-container__time">
      <text class="map-container__time__text">
        {{ data?.departure_time }}
        <!-- Departure time: Monday to Sunday 09:00, 11:30, 15:00 -->
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './map-detail.scss';
</style>
