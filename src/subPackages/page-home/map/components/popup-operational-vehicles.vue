<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'
import { ref, watch } from 'vue'
import vehicleInfo from './vehicle-info.vue'

const props = withDefaults(defineProps<{ list?: BusInfo[] }>(), {
  list: () => [
    {
      id: 1,
      image: '/static/test/view-detail-more.png',
      color: '红色',
      max_passengers: 10,
      sn: 'ABC123',
      type: '小型客车',
    },
  ],
})

const visible = defineModel('visible', {
  default: false,
})
const currentCarInfo = ref<BusInfo>(props.list[0])

watch(() => props.list, (list: BusInfo[]) => {
  currentCarInfo.value = list[0]
})

const selectIdx = ref(0)

function handleSelectPlateNumber(idx: number) {
  selectIdx.value = idx
  currentCarInfo.value = props.list[idx]
}

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="bottom" :z-index="9999" custom-style="border-radius: 40rpx 40rpx 0 0;">
    <view class="popup-select-route-container">
      <view class="popup-select-route-container__header">
        <text class="popup-select-route-container__header__title">
          Operational vehicles
        </text>
      </view>
      <view class="popup-select-route-container__body">
        <view class="popup-select-route-container__body__header">
          <view
            v-for="(item, idx) in list"
            :key="idx"
            class="popup-select-route-container__body__header__item"
            :class="{ 'popup-select-route-container__body__header__item--active': selectIdx === idx }"
            @click.stop="handleSelectPlateNumber(idx)"
          >
            <text class="popup-select-route-container__body__header__item__text" :class="{ 'popup-select-route-container__body__header__item__text--active': selectIdx === idx }">
              {{ item.sn }}
            </text>
          </view>
        </view>
        <vehicleInfo :data="currentCarInfo" />
      </view>
      <view class="popup-select-route-container__close" @click.stop="handleClose">
        <zui-svg-icon width="40rpx" height="40rpx" icon="cancel-light" />
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@use './popup-operational-vehicles.scss';
</style>
