<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'
import { ref, watch } from 'vue'

const props = defineProps<{ data?: BusInfo }>()

const swiperList = ref([
  {
    id: 1,
    image: '/static/test/view-detail-more.png',
  },
  {
    id: 2,
    image: '/static/test/view-detail-more.png',
  },
])

watch(() => props.data!, (list: BusInfo) => {
  swiperList.value = list.image_lists.map((img: string, idx: number) => ({ image: img, id: idx }))
}, {
  immediate: true,
  deep: true,
})
</script>

<template>
  <view class="vehicle-info-container">
    <text class="vehicle-info-container__desc">
      {{ `Large passenger bus, white, ${data?.max_passengers} people maximum` }}
    </text>
    <view class="vehicle-info-container__loop">
      <base-swiper :list="swiperList" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use './vehicle-info.scss'
</style>
