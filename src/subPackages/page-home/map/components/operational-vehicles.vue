<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'

withDefaults(defineProps<{ data?: BusInfo[] }>(), {
  data: () => [],
})
const emit = defineEmits<{
  (e: 'change'): void
}>()
function handleChange() {
  emit('change')
}
</script>

<template>
  <view class="operational-vehicles-container" @click.stop="handleChange">
    <view class="operational-vehicles-container__left">
      <zui-svg-icon class="operational-vehicles-container__left__bus-icon" width="48rpx" height="48rpx" icon="bus" />
      <text v-if="data.length > 2" class="operational-vehicles-container__left__text">
        {{ `Operational vehicles: ${data?.slice(0, 2)?.map(item => item?.sn)}...` }}
      </text>
      <text v-else class="operational-vehicles-container__left__text">
        {{ `Operational vehicles: ${data?.map(item => item?.sn)}` }}
      </text>
    </view>
    <zui-svg-icon class="operational-vehicles-container__right" width="40rpx" height="40rpx" icon="arrow-right2-light" />
  </view>
</template>

<style lang="scss" scoped>
@use './operational-vehicles.scss'
</style>
