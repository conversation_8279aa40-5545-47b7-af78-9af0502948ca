<script lang="ts" setup>
import type { RouteItem } from './components/popup-select-route.vue'
import type { HotInfo as LineItem, TicketRouteDetail } from '@/model/home/<USER>'
import { ref } from 'vue'
import { ApiGetTicketRouteDetail, ApiGetTicketRouteList } from '@/api/home/<USER>'
import cardStep from './components/card-step.vue'
import kindlyReminder from './components/kindly-reminder.vue'
import mapDetail from './components/map-detail.vue'
import operationalVehicles from './components/operational-vehicles.vue'
import popupOperationalVehicles from './components/popup-operational-vehicles.vue'
import popupSelectRoute from './components/popup-select-route.vue'
import selectRoute from './components/select-route.vue'

const showSelectRoute = ref(false)
const currentRoute = ref('Ease line ZQN-DUN-CHC1')
const detailInfo = ref<TicketRouteDetail>({})
interface SelectRouteItem extends LineItem {
  color?: string
}
const selectRouteList = ref<SelectRouteItem[]>([])

const colors = ['#fafafa', '#edf1f6', '#f6f6ed', '#edf6f0']
function handleSelectRoute() {
  showSelectRoute.value = true
}

const showOperationalVehicles = ref(false)
function handleSelectOperationalVehicles() {
  showOperationalVehicles.value = true
}

function handleSelectRouteChange(item: RouteItem) {
  currentRoute.value = item.name
}

async function handleGetTicketRouteDetail(id?: number) {
  const data = await ApiGetTicketRouteDetail(id ?? 1)
  detailInfo.value = data
  // console.log(data, 'data detail')
}

handleGetTicketRouteDetail()

async function handleGetTicketRouteList() {
  const data = await ApiGetTicketRouteList()
  selectRouteList.value = data.map((item, idx) => ({ ...item, color: colors[idx % colors.length] }))
  currentRoute.value = data[0].name
}

handleGetTicketRouteList()
</script>

<template>
  <view class="map-container">
    <nav-bar-page title="Map" />
    <view>
      <view class="map-container__select-route">
        <selectRoute v-model="currentRoute" @change="handleSelectRoute" />
      </view>
      <view class="map-container__main">
        <mapDetail :data="detailInfo.route" />
        <operationalVehicles :data="detailInfo.coachs" @change="handleSelectOperationalVehicles" />
        <cardStep :list="detailInfo.stations" />
        <kindlyReminder />
      </view>
    </view>
    <popupSelectRoute v-model:visible="showSelectRoute" :list="selectRouteList" @change="handleSelectRouteChange" />
    <popupOperationalVehicles v-model:visible="showOperationalVehicles" :list="detailInfo.coachs" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss';
</style>
