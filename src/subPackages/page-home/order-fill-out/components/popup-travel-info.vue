<script lang="ts" setup>
const visible = defineModel('visible', {
  default: false,
})

function handleClose() {
  visible.value = false
}
</script>

<template>
  <wd-popup v-model="visible" position="top" :z-index="9999" custom-style="border-radius: 0 0 40rpx 40rpx;">
    <view class="popup-travel-info-container__content">
      <view class="popup-travel-info-container__content__item bb">
        <text class="popup-travel-info-container__content__item__title">
          First trip
        </text>
        <view class="popup-travel-info-container__content__item__section">
          <view class="popup-travel-info-container__content__item__section__info">
            <text class="popup-travel-info-container__content__item__section__info__plate-number">
              EFG123
            </text>
            <text class="popup-travel-info-container__content__item__section__info__desc">
              07/08 Tue 08:00 Departure
            </text>
          </view>
          <text class="popup-travel-info-container__content__item__section__time">
            (about 3 hours)
          </text>
        </view>
      </view>
      <view class="popup-travel-info-container__content__item">
        <text class="popup-travel-info-container__content__item__title">
          Second trip
        </text>
        <view class="popup-travel-info-container__content__item__section">
          <view class="popup-travel-info-container__content__item__section__info">
            <text class="popup-travel-info-container__content__item__section__info__plate-number">
              ABC456
            </text>
            <text class="popup-travel-info-container__content__item__section__info__desc">
              07/08 Tue 08:00 Departure
            </text>
          </view>
          <text class="popup-travel-info-container__content__item__section__time">
            (about 3 hours)
          </text>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@use './popup-travel-info.scss'
</style>
