<script lang="ts" setup>
import type { TicketInfo } from '@/model/home/<USER>'
import { ref } from 'vue'
import popupTimeTable from '@/subPackages/page-home/buy-muti-city-ticket/components/popup-time-table.vue'
import selectPassenger from '@/subPackages/page-home/components/ticket-info/passenger.vue'
import payCard from '@/subPackages/page-home/components/ticket-info/pay-card.vue'
import selectTime from '@/subPackages/page-home/components/ticket-info/select-time.vue'
import ticketInput from '@/subPackages/page-home/components/ticket-info/ticket-input.vue'
import ticketRadio from '@/subPackages/page-home/components/ticket-info/ticket-radio.vue'
import travelInfo from '@/subPackages/page-home/components/ticket-info/travel-info.vue'
import popupDetail from './components/popup-detail.vue'
import popupTravelInfo from './components/popup-travel-info.vue'

interface OrderTicketInfo extends TicketInfo {
  time?: null | number
}

const ticketInfo = ref<OrderTicketInfo>({})
const showDetail = ref(false)
const showTravelInfo = ref(false)
const showPreviewTimeTable = ref(false)

function handleCheckTravelInfo() {
  showTravelInfo.value = true
}

function handleCheckDetail() {
  showDetail.value = true
}

function back() {
  uni.navigateBack()
}

function handlePreviewTimeTable() {
  showPreviewTimeTable.value = true
}

function handlePayment() {
  console.log(ticketInfo.value)
}
</script>

<template>
  <view class="order-fill-out-container">
    <nav-bar-page title="Order fill out">
      <view class="order-fill-out-container__nav-bar-left-container" />
    </nav-bar-page>
    <view
      class="order-fill-out-container__content"
    >
      <travelInfo @click.stop="handleCheckTravelInfo" @preview-time-table="handlePreviewTimeTable" />
      <view class="order-fill-out-container__content__line" />
      <selectTime v-model="ticketInfo.time" />
      <selectPassenger v-model="ticketInfo.adult" />
      <selectPassenger v-model="ticketInfo.children">
        <template #left>
          <view class="order-fill-out-container__content__children">
            <image class="order-fill-out-container__content__children__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="order-fill-out-container__content__children__text">
              <text class="order-fill-out-container__content__children__text__title">
                Children
              </text>
              <text class="order-fill-out-container__content__children__text__desc">
                (3-12Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <selectPassenger v-model="ticketInfo.baby">
        <template #left>
          <view class="order-fill-out-container__content__baby">
            <image class="order-fill-out-container__content__baby__icon" src="/static/home/<USER>" mode="aspectFill" />
            <view class="order-fill-out-container__content__baby__text">
              <text class="order-fill-out-container__content__baby__text__title">
                Baby
              </text>
              <text class="order-fill-out-container__content__baby__text__desc">
                (0-2Y)
              </text>
            </view>
          </view>
        </template>
      </selectPassenger>
      <view class="order-fill-out-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.phoneNumber" />
      </view>
      <view class="order-fill-out-container__content__ticket-input">
        <ticketInput v-model="ticketInfo.email" label="Buyer E-mail" placeholder="zer0126@.com" />
      </view>
      <view class="order-fill-out-container__content__reserve">
        <text class="order-fill-out-container__content__reserve__label">
          Booking fee (default selected)
        </text>
        <text class="order-fill-out-container__content__reserve__value">
          1 NZD
        </text>
      </view>
      <ticketRadio v-model="ticketInfo.rebookChecked" label="Flexi Price" show-tip />
      <ticketRadio v-model="ticketInfo.deductionChecked" label="Points deduction" />
    </view>
    <payCard @check-detail="handleCheckDetail" @pay="handlePayment" />
    <popupDetail v-model:visible="showDetail" />
    <popupTravelInfo v-model:visible="showTravelInfo" />
    <popupTimeTable v-model="showPreviewTimeTable" />
  </view>
</template>

<style lang="scss" scoped>
@use './index.scss'
</style>
