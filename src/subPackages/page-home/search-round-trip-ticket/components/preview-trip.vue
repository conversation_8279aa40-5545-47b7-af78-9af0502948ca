<script lang="ts" setup>
withDefaults(defineProps<{ title: string }>(), {
  title: 'Outbound',
})

function handleCheckMap() {

}
</script>

<template>
  <view class="preview-trip-container" @click="handleCheckMap">
    <text class="preview-trip-container__text">
      {{ title }}
    </text>
    <zui-svg-icon class="preview-trip-container__icon" width="48rpx" height="48rpx" color="#103a62" icon="show-light" />
  </view>
</template>

<style lang="scss" scoped>
.preview-trip-container{
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
height: 82rpx;
padding: 24rpx;
border-radius: 24rpx;
background-color: #edf1f6;
margin-bottom: 24rpx;
&__text{
font-family: "Alimama FangYuanTi VF";
font-size: 28rpx;
font-weight: 700;
color: #103a62;
}
&__icon{
width: 40rpx;
height: 40rpx;
}
}
</style>
