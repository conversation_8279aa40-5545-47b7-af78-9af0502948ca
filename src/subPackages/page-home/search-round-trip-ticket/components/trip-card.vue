<script lang="ts" setup>
import { ref } from 'vue'
import busInfo from '@/subPackages/page-home/components/bus-info/index.vue'
import previewTrip from './preview-trip.vue'

withDefaults(defineProps<{
  title?: string
}>(), {
  title: 'Outbound',
})

const date = ref(null)

defineExpose({

})
</script>

<template>
  <view class="trip-card-container">
    <previewTrip :title="title" />
    <view class="trip-card-container__calendar">
      <BaseCalendar v-model="date" />
    </view>
    <view class="trip-card-container__bus-info-outbound">
      <busInfo :show-expand="true" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trip-card-container{
&__calendar{
margin-bottom: 24rpx;
}
&__bus-info-outbound{

}
}
</style>
