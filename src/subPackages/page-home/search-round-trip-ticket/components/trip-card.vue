<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import busInfo from '@/subPackages/page-home/components/bus-info/index.vue'
import { getStore } from '@/utils/store'
import previewTrip from './preview-trip.vue'

const props = withDefaults(defineProps<{
  title?: string
  prop: string
}>(), {
  title: 'Outbound',
})

const date = ref(null)
const addressInfo = ref()

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
  date.value = addressInfo.value[props.prop]
  // handleGetSearchTicketList()
  console.log(addressInfo.value, date.value, 'date.value')
})

defineExpose({

})
</script>

<template>
  <view class="trip-card-container">
    <previewTrip :title="title" />
    <view class="trip-card-container__calendar">
      <BaseCalendar v-model="date" :prop />
    </view>
    <view class="trip-card-container__bus-info-outbound">
      <busInfo :show-expand="true" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trip-card-container{
&__calendar{
margin-bottom: 24rpx;
}
&__bus-info-outbound{

}
}
</style>
