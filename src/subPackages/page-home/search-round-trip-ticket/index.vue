<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import { getStore } from '@/utils/store'
import tripCard from './components/trip-card.vue'

const addressInfo = ref<AddressInfo>({
  depart: '',
  arrive: '',
  startTime: 0,
  arriveId: 0,
  departId: 0,
  way: 'roundTrip',
})

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
})

function back() {
  uni.navigateBack()
}

function handleCheckNotice() {
  uni.navigateTo({
    url: '/subPackages/page-home/notice/index',
  })
}

function handleBuyTicket() {
  uni.navigateTo({
    url: '/subPackages/page-home/buy-round-trip-ticket/index',
  })
}
</script>

<template>
  <view class="search-round-trip-container">
    <nav-bar-page is-custom :nav-bar-height="108">
      <view class="search-round-trip-container__nav-bar-left-container">
        <zui-svg-icon class="search-round-trip-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
        <text class="search-round-trip-container__nav-bar-left-container__from">
          {{ addressInfo.depart }}
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__left-arrow">
          <
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__arrow">
          -
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__right-arrow">
          >
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__to">
          {{ addressInfo.arrive }}
        </text>
        <text class="search-round-trip-container__nav-bar-left-container__notice" @click.stop="handleCheckNotice">
          Notice
        </text>
      </view>
    </nav-bar-page>
    <view
      class="search-round-trip-container__main"
    >
      <view class="search-round-trip-container__main__outbound">
        <tripCard prop="startTime" title="Outbound" />
      </view>
      <view class="search-round-trip-container__main__return">
        <tripCard prop="endTime" title="Return" />
      </view>
    </view>
    <view class="search-round-trip-container__buy">
      <view class="search-round-trip-container__buy__btn" @click.stop="handleBuyTicket">
        <text class="search-round-trip-container__buy__btn__text">
          Buy ticket
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-round-trip-container{
&__nav-bar-left-container {
display: flex;
flex-direction: row;
align-items: center;
&__back-icon{
width: 48rpx;
height: 48rpx;
margin-right: 20rpx;
}
&__left-arrow, &__right-arrow,&__arrow{
font-size: 32rpx;
font-weight: 700;
text-align: center;
}
&__left-arrow{
font-size: 32rpx;
}
&__right-arrow{

}
&__from, &__to {
font-family: "Alimama FangYuanTi VF";
font-size: 32rpx;
color: #111B19;
font-weight: 600;
}
&__from{
margin-right: 20rpx;
}
&__to {
margin-left: 20rpx;
}
&__notice{
font-size: 26rpx;
color: #103a62;
margin-left: 20rpx;
text-decoration-line: underline;
}
}
&__main{
padding: 24rpx 32rpx 112rpx;
&__outbound, &__return {
margin-bottom: 32rpx;
}
}
&__buy{
position: fixed;
left: 0;
bottom: 0;
width: 750rpx;
height: 112rpx;
display: flex;
justify-content: center;
align-items: center;
background-color: #fff;
padding: 24rpx 28rpx;
&__btn{
width: 660rpx;
height: 84rpx;
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
background-color: #103a62;
border-radius: 24rpx;
&__text{
font-family: "Alimama FangYuanTi VF";
color: #fff;
font-size: 28rpx;
}
}
}
}
</style>
