<script lang="ts" setup>
import type { ParamsInfo, RouteInfo } from '@/model/home/<USER>'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiGetRouteList } from '@/api/home'
import lineCollapse from '@/subPackages/page-home/components/line-collapse/index.vue'
import popularSite from './components/popular-site.vue'

const keyWord = ref('')
const routeInfo = ref<RouteInfo>({
})

const paramsInfo = ref<ParamsInfo>({
  address: '',
  way: 'oneWay',
})

onLoad((params: any) => {
  paramsInfo.value = params
})

function back() {
  uni.navigateBack()
}

function switchMap() {
  uni.navigateTo({
    url: '/subPackages/page-home/map/index',
  })
}

function handleSearch() {
  let url = `/subPackages/page-home/search-address/index?way=${paramsInfo.value.way}&address=${paramsInfo.value.address}`
  if (paramsInfo.value.way === 'mutiCity') {
    url = `/subPackages/page-home/search-address/index?way=${paramsInfo.value.way}&idx=${paramsInfo.value.idx}&address=${paramsInfo.value.address}`
  }
  uni.navigateTo({
    url,
  })
}

async function handleGetRouteList() {
  const data = await ApiGetRouteList({})
  routeInfo.value = data as RouteInfo
}

handleGetRouteList()
</script>

<template>
  <view class="select-address-container">
    <nav-bar-page is-custom :nav-bar-height="108">
      <view class="select-address-container__nav-bar-left-container">
        <zui-svg-icon class="select-address-container__nav-bar-left-container__back-icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
        <text class="select-address-container__nav-bar-left-container__title">
          {{ $t('home.selectSite') }}
        </text>
        <view class="select-address-container__nav-bar-left-container__transport" @click.stop="switchMap">
          <image class="select-address-container__nav-bar-left-container__transport__icon" src="/static/home/<USER>" />
          <text class="select-address-container__nav-bar-left-container__transport__operate">
            {{ $t('home.lineMap') }}
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="select-address-container__content">
      <search-input v-model="keyWord" text="" disabled :placeholder="$t('common.inputPlaceholder')" @tap="handleSearch" />
      <view
        class="select-address-container__content__main"
      >
        <view v-if="routeInfo.hot?.length" class="select-address-container__content__main__popular-site-area">
          <popularSite :list="routeInfo.hot" />
        </view>
        <view class="select-address-container__content__main__line-area">
          <lineCollapse :list="routeInfo.data" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.select-address-container {
    &__nav-bar-left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__back-icon{
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }
        &__title{
            font-size: 32rpx;
            color: #111b19;
            font-weight: 700;
            margin-right: 12rpx;
        }
        &__transport{
            display: flex;
            flex-direction: row;
            align-items: center;
            &__icon {
                width: 40rpx;
                height: 40rpx;
            }

            &__operate{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 68rpx;
                font-size: 26rpx;
                font-weight: 400;
                color: #103a62;
            }
        }

    }
    &__content {
        padding: 0 32rpx;
        &__search{

        }
        &__main {
            margin-top: 48rpx;
            // border: 1px solid red;
            &__popular-site-area{
                margin-bottom: 48rpx;
            }
        }
    }
}
</style>
