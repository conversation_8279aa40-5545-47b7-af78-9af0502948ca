<script lang="ts" setup>
import type { HotInfo, ParamsInfo } from '@/model/home/<USER>'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import { getStore, setStore } from '@/utils/store'

withDefaults(defineProps<{ list?: HotInfo[] }>(), {
  list: () => [
    {
      id: 1,
      name: 'Shenzhen N.',
    },
    {
      id: 2,
      name: 'Baoan Airport',
    },
    {
      id: 3,
      name: 'Shenzhen S.',
    },
    {
      id: 4,
      name: 'Baoan Center',
    },
  ],
})

const paramsInfo = ref<ParamsInfo>({
  address: '',
  way: 'oneWay',
})
const active = ref<number>(-1)

onLoad((params: any) => {
  paramsInfo.value = params
})

onShow(() => {
  const { address } = paramsInfo.value
  const { departId, arriveId } = getStore(AddressInfoEnum.ADDRESS_INFO)
  if (address === 'depart') {
    active.value = departId
  }
  else {
    active.value = arriveId
  }
})

function handleSelectPopularSite(item: HotInfo) {
  const { way, address } = paramsInfo.value
  const id = `${address}Id`
  active.value = item.id
  const storeAddress = getStore(AddressInfoEnum.ADDRESS_INFO)
  if (way === 'transfer') {
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      [id]: item.id,
      [address]: item.name,
    })
    uni.navigateTo({
      url: '/subPackages/page-home/transfer/index',
    })
    return
  }
  if (way === 'mutiCity') {
    if (storeAddress && Array.isArray(storeAddress) && storeAddress.length > 0) {
      const originAddress = storeAddress[paramsInfo.value.idx!]
      storeAddress[paramsInfo.value.idx!] = {
        ...originAddress,
        way,
        [id]: item.id,
        [address]: item.name,
      }
      setStore(AddressInfoEnum.ADDRESS_INFO, storeAddress)
    }
    else {
      setStore(AddressInfoEnum.ADDRESS_INFO, [{
        way,
        [id]: item.id,
        [address]: item.name,
      }])
    }
  }
  else {
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      [id]: item.id,
      [address]: item.name,
    })
  }

  uni.switchTab({
    url: '/pages/page-home/index',
  })
}
</script>

<template>
  <view class="popular-site-container">
    <text class="popular-site-container__title">
      {{ $t('home.popularSite') }}
    </text>
    <view class="popular-site-container__content">
      <view
        v-for="(item, idx) in list"
        :key="idx"
        class="popular-site-container__content__site-item"
        :class="{
          'popular-site-container__content__site-item--active': item.id === active,
          'mr-0': idx % 2 === 1,
        }"
        @click="handleSelectPopularSite(item)"
      >
        <text
          class="popular-site-container__content__site-item__text"
          :class="{ 'popular-site-container__content__site-item__text--active': item.id === active }"
        >
          {{ item.name }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.popular-site-container {
    &__title {
        height: 36rpx;
        font-size: 30rpx;
        font-weight: 700;
        color: #161616;
    }
    &__content{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        // gap: 24rpx;
        margin-top: 32rpx;
        &__site-item{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 330rpx;
            height: 68rpx;
            padding: 18rpx 24rpx;
            border-radius: 20rpx;
            background-color: #f7f7f7;
            margin:0 24rpx 24rpx 0;

            &--active {
                background-color: #161616;
            }
            &__text {
                font-size:26rpx;
                color: #161616;
                &--active{
                    color: #fff;
                }
            }
        }
    }
}

.mr-0{
    margin-right: 0;
}
</style>
