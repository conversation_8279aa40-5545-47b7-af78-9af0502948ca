<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'
import { ref, watch } from 'vue'
import busCard from './components/bus-card.vue'

const props = withDefaults(defineProps<{
  showExpand?: boolean
  list?: BusInfo[]
}>(), {
  showExpand: false,
  list: () => [{
    time: '08:00',
    busNumber: 'ABC123',
    desc: '31 min, 5 tickets left',
    adultPrice: '21NZD',
    childPrice: '16NZD',
    discount: 15,
  }, {
    time: '08:30',
    busNumber: 'ABC124',
    desc: '31 min, 5 tickets left',
    adultPrice: '21NZD',
    childPrice: '16NZD',
    discount: 10,
  }, {
    time: '09:00',
    busNumber: 'ABC125',
    desc: '31 min, 5 tickets left',
    adultPrice: '21NZD',
    childPrice: '16NZD',
    discount: 12,
  }],
})

const isExpand = ref(false)
const activeIdx = ref(0)
const renderList = ref(props.list)
const foldList = ref(props.list.slice(0, 2))
const unfoldList = ref(props.list)

watch((): boolean => {
  return isExpand.value
}, (val: boolean) => {
  if (!props.showExpand) {
    return
  }
  if (!val) {
    renderList.value = foldList.value
  }
  else {
    renderList.value = unfoldList.value
  }
}, {
  immediate: true,
})
function handleSelectBus(idx: number) {
  activeIdx.value = idx
}

function handleExpand() {
  isExpand.value = !isExpand.value
}
</script>

<template>
  <view class="bus-info-container">
    <busCard
      v-for="(item, idx) in renderList"
      :key="idx"
      :data="item"
      :active="activeIdx === idx"
      @click.stop="handleSelectBus(idx)"
    />
    <view v-if="showExpand && list.length > 2" class="bus-info-container__expand">
      <view class="bus-info-container__expand__content" @click.stop="handleExpand">
        <text class="bus-info-container__expand__content__text">
          {{ isExpand ? 'Fold' : 'Unfold' }}
        </text>
        <zui-svg-icon class="bus-info-container__expand__content__icon" width="35rpx" height="50rpx" color="#103a62" :icon="isExpand ? 'arrow-up2-light' : 'arrow-down2-light'" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.bus-info-container{
    // margin-bottom: 32rpx;
    &__expand{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        &__content{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            &__text{
                font-family: "Alimama FangYuanTi VF";
                font-size: 24rpx;
                font-weight: 400;
                color: #103a62;
                margin-right: 10rpx;
            }
            &__icon{
                width: 35rpx;
                height: 50rpx;
            }
        }
    }
}
</style>
