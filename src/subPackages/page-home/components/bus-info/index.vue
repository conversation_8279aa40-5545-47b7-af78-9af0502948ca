<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'
import { ref, watch } from 'vue'
import busCard from './components/bus-card.vue'

const props = withDefaults(defineProps<{
  showExpand?: boolean
  list?: BusInfo[]
}>(), {
  showExpand: false,
  list: () => [{
    id: 1,
    departure_time: '08:00',
    coach_sn: 'ABC123',
    travel_time: '31 min, 5 tickets left',
    adult_non_changeable_price: '21NZD',
    child_non_changeable_price: '16NZD',
    is_fest_discount: 1,
    fest_discount_rate: 15,
  }, {
    id: 2,
    departure_time: '08:30',
    coach_sn: 'ABC124',
    travel_time: '31 min, 5 tickets left',
    adult_non_changeable_price: '21NZD',
    child_non_changeable_price: '16NZD',
    is_fest_discount: 1,
    fest_discount_rate: 10,
  }],
})

const isExpand = ref(false)
// const activeBus = ref(props.list[0].id)
const renderList = ref(props.list)
const foldList = ref(props.list.slice(0, 2))
const unfoldList = ref(props.list)

watch((): boolean => isExpand.value, (val: boolean) => {
  if (!props.showExpand) {
    return
  }
  if (!val) {
    renderList.value = foldList.value
  }
  else {
    renderList.value = unfoldList.value
  }
}, {
  immediate: true,
})

watch(() => props.list, (newList: BusInfo[]) => {
  if (!props.showExpand) {
    renderList.value = newList
    return
  }

  renderList.value = props.showExpand ? newList : newList.slice(0, 2)
})

const activeBus = defineModel('active')

function handleSelectBus(id: number) {
  activeBus.value = id
}

function handleExpand() {
  isExpand.value = !isExpand.value
}
</script>

<template>
  <view class="bus-info-container">
    <busCard
      v-for="(item, idx) in renderList"
      :key="idx"
      :data="item"
      :active="activeBus === item.id"
      @click.stop="handleSelectBus(item.id)"
    />
    <view v-if="showExpand && list.length > 2" class="bus-info-container__expand">
      <view class="bus-info-container__expand__content" @click.stop="handleExpand">
        <text class="bus-info-container__expand__content__text">
          {{ isExpand ? 'Fold' : 'Unfold' }}
        </text>
        <zui-svg-icon class="bus-info-container__expand__content__icon" width="35rpx" height="50rpx" color="#103a62" :icon="isExpand ? 'arrow-up2-light' : 'arrow-down2-light'" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.bus-info-container{
    // margin-bottom: 32rpx;
    &__expand{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        &__content{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            &__text{
                font-family: "Alimama FangYuanTi VF";
                font-size: 24rpx;
                font-weight: 400;
                color: #103a62;
                margin-right: 10rpx;
            }
            &__icon{
                width: 35rpx;
                height: 50rpx;
            }
        }
    }
}
</style>
