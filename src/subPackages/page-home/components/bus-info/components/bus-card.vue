<script lang="ts" setup>
import type { BusInfo } from '@/model/home/<USER>'

defineProps<{
  active: boolean
  data: BusInfo
}>()
</script>

<template>
  <view class="bus-card-container">
    <view class="bus-card-container__left">
      <text class="bus-card-container__left__time ff">
        {{ data.time }}
      </text>
      <view class="bus-card-container__left__bus-info">
        <text class="bus-card-container__left__bus-info__num ff">
          {{ data.busNumber }}
        </text>
        <text class="bus-card-container__left__bus-info__desc ff">
          {{ data.desc }}
        </text>
      </view>
    </view>
    <view class="bus-card-container__right">
      <view class="bus-card-container__right__passenger">
        <text class="bus-card-container__right__passenger__text ff">
          Adult: {{ data.adultPrice }}
        </text>
        <text class="bus-card-container__right__passenger__text ff">
          Children: {{ data.childPrice }}
        </text>
      </view>
      <image
        class="bus-card-container__right__icon"
        :class="{ 'bus-card-container__right__icon--active': active }"
        src="/static/home/<USER>"
        mode="aspectFill"
      />
    </view>
    <base-discount :discount="data.discount" />
  </view>
</template>

<style lang="scss" scoped>
.bus-card-container{
    position: relative;
    height: 150rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: #fafafa;
    border-radius: 24rpx;
    margin-bottom: 32rpx;
    &__left{
        display: flex;
        flex-direction: row;
        align-items: center;
        &__time{
            font-size: 30rpx;
            font-weight: 700;
            color: #000;
            margin-right: 32rpx;
        }
        &__bus-info{
          display: flex;
          flex-direction: column;
            &__num{
                font-size: 26rpx;
                font-weight: 700;
                margin-bottom: 12rpx;
            }
            &__desc{
                font-size: 24rpx;
                color: #717680;
            }
        }
    }
    &__right{
        display: flex;
        flex-direction: row;
        align-items: center;
        &__passenger{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-right: 10rpx;
            &__text{
                font-size: 24rpx;
                font-weight: 400;
                color: #103a62;
            }
        }
        &__icon{
            width: 48rpx;
            height: 48rpx;
            visibility: hidden;
            &--active{
                visibility: visible;
            }
        }
    }
}
.ff {
    font-family: "Alimama FangYuanTi VF";
}
</style>
