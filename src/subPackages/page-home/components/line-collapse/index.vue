<script lang="ts" setup>
import type { CollapseInstance } from 'wot-design-uni/components/wd-collapse/types'
import type { LineItem, ParamsInfo, StationInfo } from '@/model/home/<USER>'
import { onLoad } from '@dcloudio/uni-app'
import { nextTick, ref } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import { getStore, setStore } from '@/utils/store'

withDefaults(defineProps<{ list?: LineItem[] }>(), {
  list: () => [
    {
      id: 1,
      name: 'East line ZQN-DUN-CHC',
      stations: [
        {
          id: 1,
          name: 'East site 1',
        },
        {
          id: 1,
          name: 'East site 2',
        },
        {
          id: 1,
          name: 'East site 3',
          food: true,
        },
        {
          id: 1,
          name: 'East site 4',
        },
      ],
    },
    {
      id: 1,
      name: 'West line ZQN-DUN-CHC',
      stations: [
        {
          id: 1,
          name: 'West site 1',
          food: true,
        },
        {
          id: 1,
          name: 'West site 2',
        },
        {
          id: 1,
          name: 'West site 3',
        },
        {
          id: 1,
          name: 'West site 4',
        },
      ],
    },
  ],
})

const paramsInfo = ref<ParamsInfo>({
  address: '',
  way: 'oneWay',
})

onLoad((params: any) => {
  paramsInfo.value = params
})

const collapseRef = ref<CollapseInstance>()

const value = ref([])

function handleSelectAddress(stationInfo: StationInfo) {
  const storeAddress = getStore(AddressInfoEnum.ADDRESS_INFO)
  const { way, address } = paramsInfo.value
  const id = `${address}Id`
  if (way === 'transfer') {
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      [id]: stationInfo.id,
      [address]: stationInfo.name,
    })
    uni.navigateTo({
      url: '/subPackages/page-home/transfer/index',
    })
    return
  }
  if (way === 'mutiCity') {
    if (storeAddress && Array.isArray(storeAddress) && storeAddress.length > 0) {
      const originAddress = storeAddress[paramsInfo.value.idx!]
      storeAddress[paramsInfo.value.idx!] = {
        ...originAddress,
        way,
        [id]: stationInfo.id,
        [address]: stationInfo.name,
      }
      setStore(AddressInfoEnum.ADDRESS_INFO, storeAddress)
    }
    else {
      setStore(AddressInfoEnum.ADDRESS_INFO, [{
        way,
        [id]: stationInfo.id,
        [address]: stationInfo.name,
      }])
    }
  }
  else {
    setStore(AddressInfoEnum.ADDRESS_INFO, {
      ...storeAddress,
      way,
      [id]: stationInfo.id,
      [address]: stationInfo.name,
    })
  }
  uni.switchTab({
    url: '/pages/page-home/index',
  })
}

nextTick(() => {
  collapseRef.value?.toggleAll(true)
})
</script>

<template>
  <view class="line-collapse-container">
    <wd-collapse ref="collapseRef" v-model="value">
      <wd-collapse-item v-for="(item, idx) in list" :key="idx" title="title" :name="`${idx}`">
        <template #title="{ expanded }">
          <view class="line-collapse-container__header">
            <text class="line-collapse-container__header__title">
              {{ item.name }}
            </text>
            <zui-svg-icon style="margin-right: 24rpx;" width="38rpx" height="36rpx" :icon="expanded ? 'arrow-down2-light' : 'arrow-right2-light'" />
          </view>
        </template>
        <view class="line-collapse-container__content">
          <view
            v-for="(line, index) in item.stations"
            :key="index"
            class="line-collapse-container__content__item"
            @click.stop="handleSelectAddress(line)"
          >
            <text class="line-collapse-container__content__item__text">
              {{ line.name }}
            </text>
            <image v-if="line?.is_meal" class="line-collapse-container__content__item__icon" src="/static/home/<USER>" mode="aspectFill" />
          </view>
        </view>
      </wd-collapse-item>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
.line-collapse-container{
     &__header{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        &__title{
            font-family: Alimama FangYuanTi VF;
            font-weight: 600;
            font-size: 30rpx;
            color: #161616;
        }
    }
    &__content{
        &__item{
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 76rpx;
            padding: 24rpx;
            border-bottom:2rpx solid #EAECF0;
            font-family: Alimama FangYuanTi VF;
            font-weight: 500;
            font-size: 24rpx;
            color: #1D232E;
            &:last-child{
                border: none;
            }
            &__icon{
              width: 28rpx;
              height: 28rpx;
              margin-left: 24rpx;
            }
        }
    }
    :deep(.wd-collapse-item.is-border::after){
        display: none;
        }
    :deep(.wd-collapse-item__header.is-border::after){
        display: none;
    }
    :deep(.wd-collapse-item__header.is-expanded::after){
        display: none;
    }
    :deep(.wd-collapse-item__header){
        padding: 0;
         margin-bottom: 32rpx;
    }
    :deep(.wd-collapse-item__body){
        padding: 0;
    }
    :deep(.wd-collapse-item){
        margin-bottom: 48rpx;
    }
}
</style>
