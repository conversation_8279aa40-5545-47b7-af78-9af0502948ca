<script lang="ts" setup>
import defaultIcon from '@/static/home/<USER>'

withDefaults(defineProps<{ label?: string, placeholder?: string, icon?: string, bStyle?: object }>(), {
  label: 'Buyer phone number',
  placeholder: 'Enter phone number',
  icon: defaultIcon,
  bStyle: () => ({}),
})

const inputValue = defineModel({
  default: '',
})

function handleInput(event: any) {
  inputValue.value = event.detail.value
}
</script>

<template>
  <view class="ticket-input-container" :style="bStyle">
    <view v-if="label !== ''" class="ticket-input-container__label">
      <text class="ticket-input-container__label__text">
        {{ label }}
      </text>
    </view>
    <slot v-else name="icon">
      <image class="ticket-input-container__icon" :src="icon" mode="aspectFill" />
    </slot>
    <view class="ticket-input-container__value">
      <input
        class="ticket-input-container__value__input"
        placeholder-class="ticket-input-placeholder"
        type="text"
        :value="inputValue"
        :placeholder="placeholder"
        @input="handleInput"
      >
    </view>
  </view>
</template>

<style lang="scss" scoped>
.ticket-input-container{
display: flex;
flex-direction: row;
align-items: center;
height: 90rpx;
border-radius: 24rpx;
padding: 24rpx;
background-color: #fafafa;
&__label{
width: 270rpx;
margin-right: 12rpx;
&__text{
display: flex;
align-items: center;
font-family: "Alimama FangYuanTi VF";
font-size: 26rpx;
color: #161616;
font-weight: 700;
}
}
&__icon{
width: 40rpx;
height: 40rpx;
margin-right: 24rpx;
}
&__value{
&__input {
}
}
}
.ticket-input-placeholder{
display: flex;
flex-direction: row;
align-items: center;
font-family: "Alimama FangYuanTi VF";
font-size: 24rpx;
color: #717680;
}
</style>
