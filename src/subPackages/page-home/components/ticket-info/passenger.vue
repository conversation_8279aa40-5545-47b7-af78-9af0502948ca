<script lang="ts" setup>
import { ref } from 'vue'

const props = withDefaults(defineProps<{ bStyle?: object }>(), {
  bStyle: () => ({
    borderRadius: '24rpx',
  }),
})

const defaultStyle = {
  borderRadius: '24rpx',
  ...props?.bStyle,
}

const value = defineModel({
  default: 0,
})
</script>

<template>
  <view class="select-passenger-container" :style="defaultStyle">
    <slot name="left">
      <view class="select-passenger-container__user">
        <image class="select-passenger-container__user__icon" src="/static/home/<USER>" mode="aspectFill" />
        <view class="select-passenger-container__user__text">
          <text class="select-passenger-container__user__text__title">
            Adult
          </text>
          <text class="select-passenger-container__user__text__desc">
            (13Y+)
          </text>
        </view>
      </view>
    </slot>
    <wd-input-number v-model="value" :min="0" />
  </view>
</template>

<style lang="scss" scoped>
.select-passenger-container{
    display:flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height:90rpx;
    background-color: #fafafa;
    // border-radius: 24rpx;
    padding: 24rpx;
    margin-bottom: 32rpx;
    &__user{
        display: flex;
        flex-direction: row;
        align-items: center;
        // background-color: #fafafa;
        &__icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 20rpx;
        }
        &__text {
            display: flex;
            flex-direction: row;
            &__title {
                margin-right: 12rpx;
            }
            &__title, &__desc {
                font-family: "Alimama FangYuanTi VF";
                font-size: 28rpx;
                color: #161616;
                font-weight: 700;
            }
        }
    }
}
</style>
