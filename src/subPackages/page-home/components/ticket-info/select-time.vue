<script lang="ts" setup>
import type { CalendarInstance } from 'wot-design-uni/components/wd-calendar/types'
import { computed, ref } from 'vue'
import { dayjs } from 'wot-design-uni'

const props = defineProps<{ modelValue?: number | null }>()
const emit = defineEmits<{
  (e: 'update:modelValue', date: number): void
}>()

const TIME_TYPE = 1000
const calendarRef = ref<CalendarInstance | null>(null)

const date = computed<null | number>({
  get() {
    return props.modelValue! * TIME_TYPE
  },
  set(val: number | null) {
    const value = val! / TIME_TYPE
    emit('update:modelValue', value)
  },
})

const dateText = computed(() => {
  return date.value ? dayjs(date.value).format('YYYY/MM/DD') : ''
})

function handleSelectCalendar() {
  calendarRef.value?.open()
}

function handleConfirm(dateValue: { value: number, type: string }) {
  date.value = dateValue.value
}
</script>

<template>
  <view class="select-time-container" @click.stop="handleSelectCalendar">
    <view class="select-time-container__left">
      <text class="select-time-container__left__label">
        Select travel time
      </text>
      <text class="select-time-container__left__time">
        {{ dateText }}
      </text>
    </view>
    <view class="select-time-container__right">
      <image class="select-time-container__right__icon" src="/static/home/<USER>" mode="aspectFill" />
      <wd-calendar ref="calendarRef" v-model="date" :z-index="999" :root-portal="true" :with-cell="false" @confirm="handleConfirm" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.select-time-container{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  border-radius: 24rpx;
  padding: 24rpx;
  background-color: #fafafa;
  margin-bottom: 32rpx;
  &__left{
    display: flex;
    flex-direction: row;
    align-items: center;
    &__label,&__time{
      font-family: "Alimama FangYuanTi VF";
      font-size: 26rpx;
      color: #161616;
    }
    &__label{
      font-weight: 700;
      margin-right: 40rpx;
    }
    &__time{
      font-weight: 400;
    }
  }
  &__right{
    &__icon{
      width: 42rpx;
      height: 46rpx;
    }
  }
}
</style>
