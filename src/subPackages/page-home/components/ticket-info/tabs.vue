<script setup lang="ts">
import type { ExtractPropTypes } from 'vue'
import { ref } from 'vue'
import wdTab from 'wot-design-uni/components/wd-tab/wd-tab.vue'
import wdTabs from 'wot-design-uni/components/wd-tabs/wd-tabs.vue'

type TabItem = ExtractPropTypes<typeof wdTab>
type TabProps = ExtractPropTypes<typeof wdTabs>

withDefaults(defineProps<{
  tabProps?: TabProps
  tabList?: TabItem[]
}>(), {
  tabProps: () => ({}),
  tabList: () => [
    {
      name: 'Firset trip',
      title: 'First trip',
    },
    {
      name: 'Second trip',
      title: 'Second trip',
    },
  ],
})

const emit = defineEmits<{
  (e: 'change', name: string): void
}>()

const tab = ref<number>(0)

function handleTabChange(event: { index: number, name: string }) {
  emit('change', event.name)
}
</script>

<template>
  <wd-tabs
    v-model="tab"
    :slidable-num="3"
    v-bind="tabProps"
    style="border-bottom: 2rpx solid #667085;"
    @change="handleTabChange"
  >
    <block v-for="item in tabList" :key="item.name">
      <wd-tab v-bind="item" />
    </block>
  </wd-tabs>
</template>

<style lang="scss" scoped>
:deep(.wd-tabs__nav){
    height: 60rpx;
}
:deep(.wd-tabs__nav-item){
    height: 60rpx;
    font-family: Alimama FangYuanTi VF;
    font-weight: 500;
    font-size: 24rpx;
    color: #667085;

}
:deep(.wd-tabs__nav-item.is-active) {
 background: #103A62;
 color: #fff;
}
:deep(.wd-tabs__line) {
    display: none;
}
</style>
