<script lang="ts" setup>
withDefaults(defineProps<{
  label?: string
  showTip?: boolean
  price?: number
}>(), {
  label: 'Flexi Price',
  showTip: false,
  price: 50,
})

const checked = defineModel<boolean>({
  default: false,
})

function handleCheck() {
  checked.value = !checked.value
}

function handlePreviewTip() {
  uni.navigateTo({
    url: '/subPackages/page-home/notice/index',
  })
}
</script>

<template>
  <view class="ticket-radio-container" @click.stop="handleCheck">
    <view class="ticket-radio-container__left">
      <wd-radio-group v-model="checked" inline shape="dot" checked-color="#103a62">
        <wd-radio :value="true">
          <text class="ticket-radio-container__left__label" :class="{ 'ticket-radio-container__left__label--active': checked }">
            {{ label }}
          </text>
        </wd-radio>
      </wd-radio-group>
      <image
        v-if="showTip"
        class="ticket-radio-container__left__icon"
        src="/static/home/<USER>"
        mode="aspectFill"
        @click.stop="handlePreviewTip"
      />
    </view>
    <view class="ticket-radio-container__text">
      <text class="ticket-radio-container__text__val" :class="{ 'ticket-radio-container__text__val--active': checked }">
        +{{ price }}
      </text>
      <text class="ticket-radio-container__text__unit" :class="{ 'ticket-radio-container__text__unit--active': checked }">
        NZD
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.ticket-radio-container{
display: flex;
flex-direction: row;
align-items: center;
justify-content: space-between;
padding: 24rpx;
height: 90rpx;
background-color: #fafafa;
border-radius: 24rpx;
margin-bottom: 32rpx;
&__left{
display: flex;
flex-direction: row;
align-items: center;
:deep(.wd-radio-group){
pointer-events: none;
}
&__label{
font-family: "Alimama FangYuanTi VF";
color: #a4a7ae;
font-size: 26rpx;
font-weight: 700;
&--active{
color: #161616;
}
}
&__icon {
width: 40rpx;
height: 40rpx;
margin-left: 24rpx;
}
}

&__text{
display: flex;
flex-direction: row;
align-items: center;
&__val {
font-size: 40rpx;
margin-right: 12rpx;
font-weight: 400;
}
&__unit{
font-size: 26rpx;
}
&__val , &__unit {
color: #a4a7ae;
}
&__val , &__unit {
&--active{
color: #103a62;
}
}
}
:deep(.wd-radio-group){
background-color: transparent;
}
}
</style>
