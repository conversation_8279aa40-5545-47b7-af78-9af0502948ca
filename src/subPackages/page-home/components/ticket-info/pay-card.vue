<script lang="ts" setup>
const emit = defineEmits<{
  (e: 'check-detail'): void
  (e: 'pay'): void
}>()

function handleCheckDetail() {
  emit('check-detail')
}

function handlePayment() {
  emit('pay')
}
</script>

<template>
  <view class="buy-card-container">
    <text class="buy-card-container__left">
      Total: 21 NZD
    </text>
    <view class="buy-card-container__right">
      <text class="buy-card-container__right__detail-btn" @click.stop="handleCheckDetail">
        Details
      </text>
      <view class="buy-card-container__right__pay-btn" @click.stop="handlePayment">
        <text class="buy-card-container__right__pay-btn__text">
          pay
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.buy-card-container{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  width: 750rpx;
  height: 112rpx;
  padding: 12rpx 32rpx 0 32rpx;
  background-color: #fff;
&__left{
  font-size: 32rpx;
  color: #103a62;
  font-weight: 700;
}
&__right{
  width: 340rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  &__detail-btn{
    font-family: "Alimama FangYuanTi VF";
    flex: 1;
    height: 56rpx;
    font-weight: 400;
    line-height: 56rpx;
    text-align: center;
    font-size: 24rpx;
    color: #717680;
  }
  &__pay-btn{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 222rpx;
    height: 56rpx;
    border-radius: 10rpx;
    background-color: #103a62;
    &__text{
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 24rpx;
      font-weight: 700;
    }
  }
}
}
</style>
