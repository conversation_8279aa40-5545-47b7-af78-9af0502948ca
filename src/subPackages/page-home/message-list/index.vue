<script lang="ts" setup>
import { provide, ref } from 'vue'
import Card from './components/card.vue'

function back() {
  uni.navigateBack()
}

const allRead = ref(false)

provide('allRead', allRead)

function readAll() {
  allRead.value = true
}
</script>

<template>
  <view class="message">
    <nav-bar-page is-custom :nav-bar-height="108">
      <view class="message-nav-bar">
        <view class="message-nav-bar__left-container">
          <zui-svg-icon class="message-nav-bar__left-container__icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
          <!-- <l-svg class="message-nav-bar__left-container__icon" src="/static/icons/arrow-left2-light.svg" @click="back" /> -->
          <text class="message-nav-bar__left-container__title">
            Message
          </text>
          <text class="message-nav-bar__left-container__operate ff fs-13 fw-400" @click.stop="readAll">
            Read all
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view
      class="message-container"
    >
      <Card />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.message-nav-bar {
    &__left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__icon {
            margin-right: 20rpx;
        }

        &__title {
            font-size: 32rpx;
            font-weight: 700;
            margin-right: 12rpx;
        }

        &__operate {
            font-size: 26rpx;
            color: #103a62;
            text-decoration-line: underline;
        }

    }
}
.message-container {
    // border: 1px solid red;
    padding: 24rpx 32rpx;
}
</style>
