<script lang="ts" setup>
import type { MessageCard, MessageCardItem } from '@/model/home/<USER>'
import { onReachBottom } from '@dcloudio/uni-app'
import { provide, ref } from 'vue'
import { ApiGetMessageList, ApiReadedMessage } from '@/api/home'
import { PageInfoEnum } from '@/enum/home'
import { setToken } from '@/utils/token'
import Card from './components/card.vue'

const pageInfo = ref({
  [PageInfoEnum.PAGE_NO]: 1,
  [PageInfoEnum.PAGE_SIZE]: 10,
})

/**
 * 格式化时间部分（时:分 或 时:分:秒）
 */
function formatTime(date: Date, showSeconds: boolean, separator: string): string {
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  if (showSeconds) {
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}${separator}${minutes}${separator}${seconds}`
  }

  return `${hours}${separator}${minutes}`
}

/**
 * 判断两个日期是否为同一天
 */
function isToday(date: Date, now: Date): boolean {
  return (
    date.getFullYear() === now.getFullYear()
    && date.getMonth() === now.getMonth()
    && date.getDate() === now.getDate()
  )
}

/**
 * 格式化消息时间显示
 * 当天的消息显示 "时:分"，超出一天的消息显示 "年-月-日 时:分:秒"
 * @param timestamp 时间戳或Date对象或可被Date解析的字符串
 * @param options 配置选项
 * @returns 格式化后的时间字符串
 */
function formatMessageTime(
  timestamp: number | Date | string,
  options: {
    showSeconds?: boolean // 是否显示秒数（对于当天消息）
    separator?: string // 日期分隔符
    timeSeparator?: string // 时间分隔符
  } = {},
): string {
  const {
    showSeconds = false,
    separator = '/',
    timeSeparator = ':',
  } = options

  // 转换为Date对象
  const date = new Date(timestamp)
  const now = new Date()

  // 判断是否为同一天
  const isSameDay = isToday(date, now)

  if (isSameDay) {
    // 当天消息：显示 时:分 或 时:分:秒
    return formatTime(date, showSeconds, timeSeparator)
  }
  return timestamp.toString()
}

function back() {
  uni.navigateBack()
}

const allRead = ref(false)
const messageList = ref<MessageCard[]>([])

provide('allRead', allRead)

function handleReadedMessage() {
  const params = {
    identity: 0,
  }
  ApiReadedMessage(params).finally(() => {
    handleGetMessageList()
  })
}

function readAll() {
  handleReadedMessage()
}

async function handleGetMessageList() {
  const params = {
    identity: 0,
    ...pageInfo.value,
  }
  // uni.showLoading()
  const data = await ApiGetMessageList(params).finally(() => {
    // uni.hideLoading()
  })
  messageList.value = data.lists.map((item: MessageCard) => {
    return { ...item, create_date: formatMessageTime(item.create_date) }
  })
  // console.log(data, messageList.value, 'data===')
}

setToken('9ef411759058826147d0eca5238bd0db')
handleGetMessageList()

function handleReload() {
  pageInfo.value[PageInfoEnum.PAGE_NO] += 1
  handleGetMessageList()
}

onReachBottom(() => {
  handleReload()
})
</script>

<template>
  <view class="message">
    <nav-bar-page is-custom :nav-bar-height="108">
      <view class="message-nav-bar">
        <view class="message-nav-bar__left-container">
          <zui-svg-icon class="message-nav-bar__left-container__icon" width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" @click.stop="back" />
          <!-- <l-svg class="message-nav-bar__left-container__icon" src="/static/icons/arrow-left2-light.svg" @click="back" /> -->
          <text class="message-nav-bar__left-container__title">
            Message
          </text>
          <text class="message-nav-bar__left-container__operate ff fs-13 fw-400" @click.stop="readAll">
            Read all
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view
      class="message-container"
    >
      <Card :list="messageList" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.message-nav-bar {
    &__left-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        &__icon {
            margin-right: 20rpx;
        }

        &__title {
            font-size: 32rpx;
            font-weight: 700;
            margin-right: 12rpx;
        }

        &__operate {
            font-size: 26rpx;
            color: #103a62;
            text-decoration-line: underline;
        }

    }
}
.message-container {
    // border: 1px solid red;
    padding: 24rpx 32rpx;
}
</style>
