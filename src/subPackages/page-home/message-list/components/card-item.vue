<script lang="ts" setup>
import type { MessageCard, MessageCardItem } from '@/model/home/<USER>'

withDefaults(defineProps<{ data?: MessageCard }>(), {
  data: () => ({
    time: '20：23',
    list: [
      {
        title: 'Vehicle delays',
        desc: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: false,
      },
    ],
  }),

})
function handleReadMessage(item: MessageCardItem) {
  item.read = true
}
</script>

<template>
  <view class="card-item-container">
    <view class="card-item-container__top">
      <text class="card-item-container__top__time ff">
        {{ data.time }}
      </text>
    </view>
    <view v-for="(item, idx) in data.list" :key="idx" class="card-item-container__content" @click="handleReadMessage(item)">
      <text class="card-item-container__content__title ff">
        {{ item.title }}
      </text>
      <text class="card-item-container__content__text ff">
        {{ item.desc }}
      </text>
      <view v-if="!item.read" class="card-item-container__content__unread" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.card-item-container{
    width:686rpx;
    &__top{
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: center;
        &__time {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: center;
            height: 26rpx;
            font-size: 22rpx;
            color: rgba(60, 60, 67, 0.60);
        }
    }
    &__content {
        position: relative;
        display: flex;
        flex-direction: column;
        margin: 32rpx 0;
        background-color: #fafafa;
        border-radius: 6rpx;
        padding: 30rpx;
        &__title {
            font-size: 30rpx;
            color: #161616;
            font-weight: 700;
            margin-bottom: 20rpx;
        }
        &__text{
            font-size: 22rpx;
            font-weight: 400;
            color: #161616;
        }
        &__unread{
            position: absolute;
            top: 10rpx;
            right: 10rpx;
            width: 12rpx;
            height: 12rpx;
            border-radius: 12rpx;
            background-color: #103a62;

        }
    }
}
</style>
