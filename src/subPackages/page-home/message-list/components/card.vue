<script lang="ts" setup>
import type { MessageCard, MessageCardItem } from '@/model/home/<USER>'
import { inject, ref, watchEffect } from 'vue'
import cardItem from './card-item.vue'

const props = withDefaults(defineProps<{ list?: MessageCard[] }>(), {})

const messageList = ref<MessageCard[]>([
  {
    create_date: '20:23',
    lists: [
      {
        title: 'Vehicle delays',
        content: '1The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
      {
        title: 'Vehicle delays',
        content: '2The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: false,
      },
      {
        title: 'Vehicle delays',
        content: '3The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
    ],
  },
  {
    create_date: '20:24',
    lists: [
      {
        title: 'Vehicle delays',
        content: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
      {
        title: 'Vehicle delays',
        content: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: false,
      },
      {
        title: 'Vehicle delays',
        content: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
    ],
  },
])

const allRead = inject('allRead')

watchEffect(() => {
  allRead?.value && messageList.value.forEach((item: MessageCard) => {
    item.lists.forEach((item: MessageCardItem) => {
      item.read = true
    })
  })
})
</script>

<template>
  <view class="card-container">
    <cardItem v-for="(item, idx) in list" :key="idx" :data="item" />
  </view>
</template>

<style lang="scss" scoped>
.card-container{
    // border: 1px solid red;
}
</style>
