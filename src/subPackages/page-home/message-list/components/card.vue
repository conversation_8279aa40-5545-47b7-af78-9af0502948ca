<script lang="ts" setup>
import type { MessageCard, MessageCardItem } from '@/model/home/<USER>'
import { inject, ref, watchEffect } from 'vue'
import cardItem from './card-item.vue'

const messageList = ref<MessageCard[]>([
  {
    time: '20:23',
    list: [
      {
        title: 'Vehicle delays',
        desc: '1The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
      {
        title: 'Vehicle delays',
        desc: '2The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: false,
      },
      {
        title: 'Vehicle delays',
        desc: '3The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
    ],
  },
  {
    time: '20:24',
    list: [
      {
        title: 'Vehicle delays',
        desc: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
      {
        title: 'Vehicle delays',
        desc: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: false,
      },
      {
        title: 'Vehicle delays',
        desc: 'The Qiandao Lake Town line in Shenzhen City, Guangdong Province is temporarily closed. After adjustment, you need to transfer to another line. We apologize for any inconvenience caused.',
        read: true,
      },
    ],
  },
])

const allRead = inject('allRead')

watchEffect(() => {
  allRead?.value && messageList.value.forEach((item: MessageCard) => {
    item.list.forEach((item: MessageCardItem) => {
      item.read = true
    })
  })
})
</script>

<template>
  <view class="card-container">
    <cardItem v-for="(item, idx) in messageList" :key="idx" :data="item" />
  </view>
</template>

<style lang="scss" scoped>
.card-container{
    // border: 1px solid red;
}
</style>
