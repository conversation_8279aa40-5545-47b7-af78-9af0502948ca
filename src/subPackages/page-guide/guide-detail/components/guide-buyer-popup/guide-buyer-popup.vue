<script lang="ts" setup>
import type { GuideDetailType } from '@/model/guide'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
// import { ApiSendPay } from '@/api/common'
import { ApiSaveOrder } from '@/api/guide'
import { useUserStore } from '@/stores/user'
import { gotoPage } from '@/utils/router'

// 价格列表
const props = withDefaults(defineProps<{
  detail?: GuideDetailType
}>(), {
  detail: () => {
    return {} as GuideDetailType
  },
})
const emit = defineEmits(['detail', 'submit'])
const { t } = useI18n()
const userStore = useUserStore()
const show = defineModel<boolean>('modelValue', { default: false })

const active = ref<number>(0)
const point = ref<number>() // 积分

const price = computed(() => {
  return props.detail.price[active.value].price
})

function selectModel(i: number) {
  active.value = i
}

async function sendOrder(type: 'prepare' | 'pay') {
  if (!userStore.token) {
    return gotoPage(`/subPackages/page-mine/login/login`)
  }
  // 积分校验
  if (point.value && userStore.userInfo?.points && point.value > userStore.userInfo?.points) {
    return uni.showToast({ title: t('guide.pointTips') })
  }
  const params = [{ id: props.detail.info.id, guide_price_id: props.detail.price[active.value].id, num: 1 }]
  if (type === 'prepare') {
    emit('detail', {
      data: params,
      point: point.value || 0,
    })
  }
  else {
    await ApiSaveOrder({ data: JSON.stringify(params) })
    // // 支付逻辑
  // const payInfo = await ApiSendPay({
  //   form: 'guide',
  //   order_id: orderInfo.order_id,
  //   pay_way: 4,
  // })
  }
}
</script>

<template>
  <base-popup v-model="show" is-footer :title="t('guide.buyRadio')" height="600rpx">
    <view class="popup__buyer">
      <view class="popup__buyer__top">
        <view
          v-for="(item, index) in detail.price" :key="index" class="popup__buyer__top__item"
          :class="{ active: active === index }" @tap="selectModel(index)"
        >
          <text class="popup__buyer__top__item__title fs--14--400">
            {{ item.day }} {{ t('guide.days') }}
          </text>
          <text class="popup__buyer__top__item__price fs--14--400">
            {{ item.price }} {{ t('guide.unit') }}
          </text>
        </view>
      </view>
      <view class="popup__buyer__bottom">
        <text class="popup__buyer__bottom__text fs--12--400">
          points deduction (current remaining: {{ userStore.userInfo?.points || 0 }})
        </text>
        <input
          v-model="point" type="number" class="popup__buyer__bottom__input"
          placeholder-class="popup__buyer__bottom__input__text" placeholder="Enter amount (maximum 20 points)"
        >
      </view>
    </view>
    <template #footer>
      <view class="popup__buyer__footer">
        <view class="popup__buyer__footer__btn left" @click.stop="sendOrder('prepare')">
          <text class="fs--12--400 popup__buyer__footer__btn__text">
            {{ t('guide.detail') }}
          </text>
        </view>
        <view class="popup__buyer__footer__btn right" @click.stop="sendOrder('pay')">
          <text class="fs--12--400 popup__buyer__footer__btn__text">
            {{ t('guide.purchases') }} ({{ price }} {{ t('guide.unit') }} )
          </text>
        </view>
      </view>
    </template>
  </base-popup>
</template>

<style lang="scss" scoped>
@import './guide-buyer-popup.scss'
</style>
