<script lang="ts" setup>
import type { GuideBuyerItemType } from '@/model/guide'
import { ref } from 'vue'

const emit = defineEmits(['detail', 'submit'])

const show = defineModel<boolean>('modelValue', { default: false })

const active = ref<number>(0)
const point = ref<number>(0) // 积分

const list = ref<GuideBuyerItemType[]>([
  { id: 1, title: '3 days', price: 42, unit: 'NZD' },
  { id: 2, title: '10 days', price: 142, unit: 'NZD' },
  { id: 3, title: '30 days', price: 242, unit: 'NZD' },
])

function selectModel(i: number) {
  active.value = i
}

function openDetail() {
  emit('detail')
}

function submit() {
  emit('submit')
}
</script>

<template>
  <base-popup v-model="show" is-footer title="Switch audio" height="550rpx">
    <view class="popup__buyer">
      <view class="popup__buyer__top">
        <view
          v-for="(item, index) in list" :key="index" class="popup__buyer__top__item"
          :class="{ active: active === index }" @tap="selectModel(index)"
        >
          <text class="popup__buyer__top__item__title fs--14--400">
            {{ item.title }}
          </text>
          <text class="popup__buyer__top__item__price fs--16--400">
            {{ item.price }} {{ item.unit }}
          </text>
        </view>
      </view>
      <view class="popup__buyer__bottom">
        <text class="popup__buyer__bottom__text fs--12--400">
          points deduction (current remaining: 145)
        </text>
        <input
          v-model="point" type="number" class="popup__buyer__bottom__input"
          placeholder-class="popup__buyer__bottom__input__text" placeholder="Enter amount (maximum 20 points)"
        >
      </view>
    </view>
    <template #footer>
      <view class="popup__buyer__footer">
        <view class="popup__buyer__footer__btn left" @tap.stop="openDetail">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            Details
          </text>
        </view>
        <view class="popup__buyer__footer__btn right" @tap.stop="submit">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            Purchase (42 NZD)
          </text>
        </view>
      </view>
    </template>
  </base-popup>
</template>

<style lang="scss" scoped>
@import './guide-buyer-popup.scss'
</style>
