<script lang="ts" setup>
import type { GuideDetailType } from '@/model/guide'
import { computed, ref } from 'vue'

// 价格列表
const props = withDefaults(defineProps<{
  detail?: GuideDetailType
}>(), {
  detail: () => {
    return {} as GuideDetailType
  },
})

const emit = defineEmits(['detail', 'submit'])

const show = defineModel<boolean>('modelValue', { default: false })

const active = ref<number>(0)
const point = ref<number>() // 积分

const price = computed(() => {
  return props.detail.price[active.value].price
})

function selectModel(i: number) {
  active.value = i
}

function openDetail() {
  emit(
    'detail',
    {
      id: props.detail.info.id,
      guide_price_id: props.detail.price[active.value].id,
      num: 1,
    },
  )
}

function submit() {
  emit('submit')
}
</script>

<template>
  <base-popup v-model="show" is-footer title="Switch audio" height="600rpx">
    <view class="popup__buyer">
      <view class="popup__buyer__top">
        <view
          v-for="(item, index) in detail.price" :key="index" class="popup__buyer__top__item"
          :class="{ active: active === index }" @tap="selectModel(index)"
        >
          <text class="popup__buyer__top__item__title fs--14--400">
            {{ item.day }} days
          </text>
          <text class="popup__buyer__top__item__price fs--14--400">
            {{ item.price }} NZD
          </text>
        </view>
      </view>
      <view class="popup__buyer__bottom">
        <text class="popup__buyer__bottom__text fs--12--400">
          points deduction (current remaining: 145)
        </text>
        <input
          v-model="point" type="number" class="popup__buyer__bottom__input"
          placeholder-class="popup__buyer__bottom__input__text" placeholder="Enter amount (maximum 20 points)"
        >
      </view>
    </view>
    <template #footer>
      <view class="popup__buyer__footer">
        <view class="popup__buyer__footer__btn left" @click.stop="openDetail">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            Details
          </text>
        </view>
        <view class="popup__buyer__footer__btn right" @click.stop="submit">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            Purchase ({{ price }} NZD)
          </text>
        </view>
      </view>
    </template>
  </base-popup>
</template>

<style lang="scss" scoped>
@import './guide-buyer-popup.scss'
</style>
