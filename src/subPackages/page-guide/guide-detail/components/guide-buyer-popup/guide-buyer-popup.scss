	.popup__buyer {
		padding: 32rpx 0;

		&__top {
			display: grid;
			grid-template-columns: repeat(3,1fr);
			gap: 32rpx;

			&__item {
				padding: 16rpx 12rpx;
				background-color: #ACBECF;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				&__title {
					line-height: 48rpx;
					color: #fff;
				}

				&__price {
					color: #fff;
					line-height: 48rpx;
				}

				&.active {
					background-color: #103A62;
				}
			}
		}

		&__bottom {
			padding: 24rpx;
			background-color: #FAFAFA;
			margin-top: 32rpx;

			&__text {
				color: #161616;
				
			}

			&__input {
				color: #717680;
				font-family: Alimama FangYuanTi VF !important;
				font-size: 24rpx !important;
				font-weight: 400 !important;
                margin-top: 24rpx;
			}

			&__input_text {
				color: #717680;
				font-family: Alimama FangYuanTi VF !important;
				font-size: 24rpx !important;
				font-weight: 400 !important;
			}
		}

		&__footer {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: row;
			width: 100%;

			&__btn {
				height: 60rpx;
				width: 48%;
				border-radius: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: row;

				&.left {
					border: 1px solid #103A62;

					.popup__buyer__footer__btn__text {
						color: #103A62;
					}
				}

				&.right {
					border: 1px solid transparent;
					background-color: #103A62;

					.popup__buyer__footer__btn__text {
						color: #fff;
					}
				}
			}

		}

	}