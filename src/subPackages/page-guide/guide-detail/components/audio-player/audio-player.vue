<script lang="ts" setup>
import type { AudioItemType } from '@/model/guide'
import { ref, watch } from 'vue'
import { useAudioStore } from '@/stores/audio'
import audioPlayList from '../audio-play-list/audio-play-list.vue'

// 播放列表
const props = withDefaults(defineProps<{
  list?: AudioItemType[]
}>(), {
  list: () => [],
})

const emits = defineEmits(['onTrialEnd'])

const audioStore = useAudioStore()
const isinit = ref<boolean>(false)
//  监听有数据开始播放
// #ifdef APP-PLUS
watch(() => props.list, () => {
  if (props.list.length && !isinit.value) {
    audioStore.playAudio(props.list[0])
    isinit.value = true
  }
})
// #endif

watch(() => audioStore.status.isPreview, (val: boolean) => {
  if (val) {
    emits('onTrialEnd', true)
  }
})

// 播放和暂停
let isToggling: boolean = false
function player() {
  // 正在切换中
  if (isToggling)
    return
  isToggling = true
  setTimeout(() => {
    if (audioStore.status.isPreview) {
      emits('onTrialEnd', true)
    }
    else {
      audioStore.status.isPlaying ? audioStore.pause() : audioStore.resume()
    }
    isToggling = false
  }, 50)
}

// 拖动开始
function dragStart() {
  audioStore.onDragStart()
}

// 拖动结束
function dragEnd(e: any) {
  audioStore.onDragEnd(e.value)
}

// 上一首或下一首
function onNext() {
  audioStore.next()
}
function onPrev() {
  audioStore.prev()
}

// 播放列表
const playListShow = ref<boolean>(false)
</script>

<template>
  <view class="audio">
    <view>
      <wd-slider
        v-model="audioStore.playInfo.progressPercent"
        hide-label
        hide-min-max
        active-color="#103A62"
        inactive-color="#E9EAEB"
        @dragstart="dragStart"
        @dragend="dragEnd"
      />
    </view>
    <view class="audio__time">
      <text class="audio__time__text fs--12--400">
        {{ audioStore.playInfo.formatCurrentTime }}
      </text>
      <text class="audio__time__text fs--12--400">
        {{ audioStore.playInfo.formatDuration }}
      </text>
    </view>
    <view class="audio__icon">
      <zui-svg-icon
        width="48rpx"
        height="48rpx"
        color="#000"
        icon="cropping1-light"
      />
      <view class="audio__icon__center">
        <view @click.stop="onPrev">
          <zui-svg-icon
            width="48rpx"
            height="48rpx"
            color="#000"
            icon="arrow-left6-light"
          />
        </view>
        <view class="audio__icon__center__space">
          <wd-loading
            v-if="audioStore.status.isLoading"
            color="#000000"
            custom-class="loading-black"
            size="24px"
          />
          <zui-svg-icon
            v-else
            width="48rpx"
            height="48rpx"
            color="#000"
            :icon="audioStore.status.isPlaying ? 'player-light' : 'play-light' "
            @tap="player"
          />
        </view>
        <view @click.stop="onNext">
          <zui-svg-icon
            width="48rpx"
            height="48rpx"
            color="#000"
            icon="arrow-left7-light"
          />
        </view>
      </view>
      <zui-svg-icon
        width="48rpx"
        height="48rpx"
        color="#000"
        icon="chart1-light"
        @tap="playListShow = true"
      />
    </view>
    <!-- 播放列表 -->
    <audio-play-list v-model="playListShow" />
  </view>
</template>

<style scoped lang="scss">
@import './audio-player.scss'
</style>
