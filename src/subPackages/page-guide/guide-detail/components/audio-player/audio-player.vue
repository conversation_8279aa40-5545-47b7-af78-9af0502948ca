<script lang="ts" setup>
import type { AudioItem } from '@/stores/audio'
import { onMounted, ref, watch } from 'vue'
import priceDetailPopup from '@/pages/page-guide/components/price-detail-popup/price-detail-popup.vue'
import { useAudioStore } from '@/stores/audio'
import audioPlayList from '../audio-play-list/audio-play-list.vue'
import guideBuyerPopup from '../guide-buyer-popup/guide-buyer-popup.vue'

const audioStore = useAudioStore()

const list = ref<AudioItem[]>([
  { id: '2', title: 'OST', coverImgUrl: '/static/test/guide-item.png', previewDuration: 80, src: 'https://web-ext-storage.dcloud.net.cn/uni-app/ForElise.mp3' },
  { id: '1', title: 'Lotus Mountain', coverImgUrl: '/static/test/guide-item.png', previewDuration: 90, src: 'https://www.cambridgeenglish.org/images/153149-movers-sample-listening-test-vol2.mp3' },
])

// 确认购买
const buyerShow = ref<boolean>(false)
const priceShow = ref<boolean>(false)
function handlePriceDetail() {
  buyerShow.value = false
  priceShow.value = true
}

// 试听结束弹窗
const prevviewShow = ref<boolean>(false)
watch(() => audioStore.status.isPreview, (val: boolean) => {
  if (val) {
    prevviewShow.value = true
  }
})
function handlePurchase() {
  console.warn('我要购买')
  prevviewShow.value = false
  buyerShow.value = true
}

// 播放和暂停
let isToggling: boolean = false
function player() {
  // 正在切换中
  if (isToggling)
    return
  isToggling = true
  setTimeout(() => {
    if (audioStore.status.isPreview) {
      prevviewShow.value = true
    }
    else {
      audioStore.status.isPlaying ? audioStore.pause() : audioStore.resume()
    }
    isToggling = false
  }, 50)
}

// 拖动开始
function dragStart() {
  audioStore.onDragStart()
}

// 拖动结束
function dragEnd(e: any) {
  audioStore.onDragEnd(e.value)
}

function handleClose() {
  // 拖动结束后， 当前比例小于试听比例， 则不打开限制
  if (audioStore.playInfo.progressPercent < audioStore.playInfo?.previewRatio) {
    audioStore.status.isPreview = false
  }
}

// 播放列表
const playListShow = ref<boolean>(false)

// 确认购买
function handleBuy() {
  console.warn('确认要购买，提交订单')
}

onMounted(() => {
  // #ifdef APP-PLUS
  audioStore.setPlayList(list.value)
  audioStore.playAudio(0)
  // #endif
})
</script>

<template>
  <view class="audio">
    <view>
      <wd-slider
        v-model="audioStore.playInfo.progressPercent"
        hide-label
        hide-min-max
        active-color="#103A62"
        inactive-color="#E9EAEB"
        @dragstart="dragStart"
        @dragend="dragEnd"
      />
    </view>
    <view class="audio__time">
      <text class="audio__time__text fs--12--400">
        {{ audioStore.playInfo.formatCurrentTime }}
      </text>
      <text class="audio__time__text fs--12--400">
        {{ audioStore.playInfo.formatDuration }}
      </text>
    </view>
    <view class="audio__icon">
      <zui-svg-icon
        width="48rpx"
        height="48rpx"
        color="#000"
        icon="cropping1-light"
      />
      <view class="audio__icon__center">
        <zui-svg-icon
          width="48rpx"
          height="48rpx"
          color="#000"
          icon="arrow-left6-light"
          @tap="audioStore.prev()"
        />
        <view class="audio__icon__center__space">
          <wd-loading
            v-if="audioStore.status.isLoading"
            color="#000000"
            custom-class="loading-black"
            size="24px"
          />
          <zui-svg-icon
            v-else
            width="48rpx"
            height="48rpx"
            color="#000"
            :icon="audioStore.status.isPlaying ? 'player-light' : 'play-light' "
            @tap="player"
          />
        </view>
        <zui-svg-icon
          width="48rpx"
          height="48rpx"
          color="#000"
          icon="arrow-left7-light"
          @tap="audioStore.next()"
        />
      </view>
      <zui-svg-icon
        width="48rpx"
        height="48rpx"
        color="#000"
        icon="chart1-light"
        @tap="playListShow = true"
      />
    </view>
    <!-- 试听到期提示 -->
    <confirm-popup
      v-model="prevviewShow"
      title="Audition end notification"
      content="Your trial has ended. Purchase this audio to listen to the full version."
      confirm-text="Purchase"
      cancel-text="Not yet"
      height="350rpx"
      @confirm="handlePurchase"
      @close="handleClose"
    />
    <!-- 播放列表 -->
    <audio-play-list v-model="playListShow" />
    <!-- 确认购买 -->
    <guide-buyer-popup v-model="buyerShow" @detail="handlePriceDetail" @submit="handleBuy" />
    <!-- 价格详情 -->
    <price-detail-popup v-model="priceShow" />
  </view>
</template>

<style scoped lang="scss">
@import './audio-player.scss'
</style>
