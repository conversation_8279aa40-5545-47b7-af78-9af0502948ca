.audio {
		border-radius: 24rpx;
		background-color: #FAFAFA;
		padding: 12rpx 24rpx 24rpx 24rpx;

        :deep(.wd-slider){
            .wd-slider__bar-wrapper{
                height: auto;
            }
            .wd-slider__bar{
                height: 10rpx;
                border-radius: 10rpx;
            }
            .wd-slider__button-wrapper{
                top: 50%;
                right: -10rpx;
            }
            .wd-slider__button{
                width: 20rpx;
                height: 20rpx;
                background: #103A62;
                border: #103A62;
            }
        }

		&__time {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: row;

			&__text {
				color: #717680;
			}
		}

		&__icon {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: row;
			margin-top: 12rpx;

			&__center {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: row;

				&__space {
					margin: 0 28rpx;
				}
			}
		}
	}