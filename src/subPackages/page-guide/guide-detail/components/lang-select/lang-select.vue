<script setup lang="ts">
import type { AudioItemType } from '@/model/guide'
import { nextTick, ref, watch } from 'vue'
import { useAudioStore } from '@/stores/audio'

// 播放列表
const props = withDefaults(defineProps<{
  list?: AudioItemType[]
}>(), {
  list: () => [],
})

const currentItem = defineModel<AudioItemType>('modelValue', { default: {} as AudioItemType })
const audioStore = useAudioStore()
const value = ref<number>(0)

watch(() => props.list, (newVal: AudioItemType[]) => {
  value.value = newVal[0]?.id || 0
  currentItem.value = newVal[0]
})

function handleConfirm() {
  currentItem.value = props.list.find(item => item.id === value.value) || {} as AudioItemType
  nextTick(() => {
    console.warn('currentItem', currentItem.value)
    if (Object.keys(currentItem.value).length > 0) {
      audioStore.playAudio(currentItem.value)
    }
    else {
      uni.showToast({ title: '该语言暂无音频', icon: 'none' })
    }
  })
}
</script>

<template>
  <wd-select-picker
    v-model="value"
    use-default-slot
    :columns="list"
    title="Switch Lang"
    label-key="language"
    value-key="id"
    type="radio"
    :show-confirm="false"
    :z-index="1009"
    @confirm="handleConfirm"
  >
    <view class="item">
      <view class="item__text">
        {{ currentItem?.language || 'Select' }}
      </view>
      <zui-svg-icon width="36rpx" height="36rpx" color="#000000" icon="arrow-down2-light" />
    </view>
  </wd-select-picker>
</template>

<style scoped lang="scss">
.item{
    display: flex;
    align-items: center;

    &__text{
        font-size: 28rpx;
        font-weight: 500;
        color: #000;
        margin-right: 10rpx;
    }

}
</style>
