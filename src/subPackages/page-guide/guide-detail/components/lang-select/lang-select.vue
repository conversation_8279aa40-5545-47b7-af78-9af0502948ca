<script setup lang="ts">
import type { AudioItemType } from '@/model/guide'
import { computed, ref, watch } from 'vue'
import { useAudioStore } from '@/stores/audio'

// 播放列表
const props = withDefaults(defineProps<{
  list?: AudioItemType[]
}>(), {
  list: () => [],
})
const audioStore = useAudioStore()
const value = ref<number>(0)

watch(() => props.list, (newVal: AudioItemType[]) => {
  value.value = newVal[0]?.id || 0
})

// 当前选中的对象
const currentItem = computed(() => {
  return props.list.find(item => item.id === value.value)
})

function handleConfirm() {

}
</script>

<template>
  <wd-select-picker
    v-model="value"
    use-default-slot
    :columns="list"
    title="Switch Lang"
    label-key="language"
    value-key="id"
    type="radio"
    :z-index="1009"
    @confirm="handleConfirm"
  >
    <view class="item">
      <view class="item__text">
        {{ currentItem?.language || 'Select' }}
      </view>
      <zui-svg-icon width="36rpx" height="36rpx" color="#000000" icon="arrow-down2-light" />
    </view>
  </wd-select-picker>
</template>

<style scoped lang="scss">
.item{
    display: flex;
    align-items: center;

    &__text{
        font-size: 28rpx;
        font-weight: 500;
        color: #000;
        margin-right: 10rpx;
    }

}
</style>
