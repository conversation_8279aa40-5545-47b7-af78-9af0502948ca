<script lang="ts" setup>
import type { AudioItemType } from '@/model/guide'
import { useAudioStore } from '@/stores/audio'

const audioStore = useAudioStore()
const show = defineModel<boolean>('modelValue', { default: false })

function open() {
  show.value = true
}

function close() {
  show.value = false
}

// 播放
function playItem(item: AudioItemType) {
  audioStore.playAudio(item)
  close()
}

// 加载更多音频
function loadMore() {
  audioStore.loadMoreAudioList()
}

defineExpose({
  open,
})
</script>

<template>
  <wd-popup v-model="show" :z-index="1009" position="bottom" :safe-area-inset-bottom="true" custom-style="border-radius: 40rpx 40rpx 0 0;">
    <view class="list" @touchmove.stop.prevent>
      <view class="list__title">
        <text class="list__title__text fs--16--700">
          Switch audio
        </text>
        <view class="list__title__close">
          <zui-svg-icon width="36rpx" height="36rpx" color="#000" icon="cancel-light" @tap="show = false" />
        </view>
      </view>
      <scroll-view scroll-y :show-scrollbar="false" class="list__content" @scrolltolower="loadMore">
        <view
          v-for="(item, index) in audioStore.playlist"
          :key="item.id"
          class="list__content__item"
          :class="{ active: index === audioStore.currentIndex }"
          @tap="playItem(item)"
        >
          <view class="list__content__item__left">
            <zui-svg-icon
              width="32rpx"
              height="32rpx"
              :color="index === audioStore.currentIndex ? '#fff' : '#000'"
              icon="transport-light"
            />
            <text class="list__content__item__left__text fs--14--400">
              {{ item.name }}{{ item.id }}
            </text>
          </view>
          <zui-svg-icon
            v-if="index === audioStore.currentIndex"
            width="32rpx"
            height="32rpx"
            color="#fff"
            icon="check-light"
          />
        </view>
      </scroll-view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@import './audio-play-list.scss'
</style>
