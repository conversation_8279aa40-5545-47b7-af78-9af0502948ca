<script lang="ts" setup>
import type { AudioItemType } from '@/model/guide'
import { useAudioStore } from '@/stores/audio'

const audioStore = useAudioStore()
const show = defineModel<boolean>('modelValue', { default: false })

function open() {
  show.value = true
}

function close() {
  show.value = false
}

// 播放
function playItem(item: AudioItemType) {
  audioStore.playAudio(item)
  close()
}

defineExpose({
  open,
})
</script>

<template>
  <base-popup v-model="show" title="Switch audio" height="1000rpx">
    <view class="list">
      <view
        v-for="(item, index) in audioStore.playlist"
        :key="item.id"
        class="list__item"
        :class="{ active: index === audioStore.currentIndex }"
        @tap="playItem(item)"
      >
        <view class="list__item__left">
          <zui-svg-icon
            width="32rpx"
            height="32rpx"
            :color="index === audioStore.currentIndex ? '#fff' : '#000'"
            icon="transport-light"
          />
          <text class="list__item__left__text fs--14--400">
            {{ item.name }}
          </text>
        </view>
        <zui-svg-icon
          v-if="index === audioStore.currentIndex"
          width="32rpx"
          height="32rpx"
          color="#fff"
          icon="check-light"
        />
      </view>
    </view>
  </base-popup>
</template>

<style lang="scss" scoped>
@import './audio-play-list.scss'
</style>
