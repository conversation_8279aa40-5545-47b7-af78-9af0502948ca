

.list{
	padding: 32rpx;

	&__title {
		height: 80rpx;
		position: relative;
		text-align: center;

		&__text {
			color: #111B19;
			line-height: 80rpx;
		}

		&__close {
			position: absolute;
			top: 0;
			width: 50rpx;
			height: 50rpx;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: row;
		}

	}

	&__content{
		height: 800rpx;
		
		&__item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: row;
			height: 70rpx;
			padding: 20rpx 30rpx;
			border-radius: 120rpx;
			margin-bottom: 24rpx;
			background-color: #F7F7F7;

			&__left {
				display: flex;
				align-items: center;
				flex-direction: row;
				flex: 1;

				&__text {
					color: #161616;
					margin-left: 18rpx;
				}
			}

			&.active {
				background-color: #103A62;

				.list__content__item__left__text{
					color:#fff;
				}
			}
		}
	}
}