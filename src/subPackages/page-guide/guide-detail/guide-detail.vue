<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import audioPlayer from './components/audio-player/audio-player.vue'

const id = ref<number>(0)
const swiperList = ref<BaseSwiperItemType[]>([
  { id: 1, url: '/static/test/guide-detail.png' },
  { id: 2, url: '/static/test/guide-detail.png' },
])

const str = `<PERSON> • <PERSON> • <PERSON> Bloggs • John <PERSON>. <PERSON> • <PERSON>, <PERSON> and <PERSON> • Foo • Bar • Baz •
Qux • J. Random User • Thingamajig • Doohickey • Whatchamacallit • Widget • Gizmo • Gadget •
Thingy • Jiggambob • Whatshisname • Whatshername • So‑and‑so • Main Street • Exampleland •
Examplestan • Podunk • Timbuktu • Hicksville • Blankville • Slobbovian • Loamshire •
Freedonia • XYZ Widget Company • Contoso • Fabrika<PERSON> • Wingtip Toys • Ac<PERSON> • <PERSON> • <PERSON> • <PERSON> • <PERSON>, <PERSON> and <PERSON> • Foo • Bar • Baz •
Qux • J. <PERSON>r • Thingamajig • Doohickey • Whatchamacallit • Widget • Gizmo • Gadget •
Thingy • Jiggambob • Whatshisname • Whatshername • So‑and‑so • Main Street • Exampleland •
Examplestan • Podunk • Timbuktu • Hicksville • Blankville • Slobbovian • Loamshire •
Freedonia • XYZ Widget Company • Contoso • Fabrikam • Wingtip Toys • Acme
John Doe • Jane Doe • Joe Bloggs • John Q. Public • Tom, Dick and Harry • Foo • Bar • Baz •
Qux • J. Random User • Thingamajig • Doohickey • Whatchamacallit • Widget • Gizmo • Gadget •
Thingy • Jiggambob • Whatshisname • Whatshername • So‑and‑so • Main Street • Exampleland •
Examplestan • Podunk • Timbuktu • Hicksville • Blankville • Slobbovian • Loamshire •
Freedonia • XYZ Widget Company • Contoso • Fabrikam • Wingtip Toys • Acme`

onLoad((e: any) => {
  if (e.id) {
    id.value = e.id
  }
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="custom-bar">
        <text class="custom-bar_title fs--16--700">
          OCT
        </text>
        <view class="custom-bar_right">
          <view class="custom-bar_right_icon">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="danger-circle-light" />
          </view>
          <view class="custom-bar_right_icon">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="heart-light" />
          </view>
          <view class="custom-bar_right_icon">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="logout-light" />
          </view>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="detail">
        <!-- 轮播图 -->
        <view class="detail__swiper">
          <base-swiper :list="swiperList" height="350rpx" border-radius="24rpx" />
        </view>

        <view class="detail__main">
          <view class="detail__main__content">
            <view class="detail_title">
              <view class="detail_title_left">
                <text class="fs--14--700">
                  Introduction
                </text>
                <text class="detail_title_left_tag fs--10--400">
                  Explaining
                </text>
              </view>
              <view class="detail_title_right">
                <!-- <switch-lang v-model="lang"></switch-lang> -->
              </view>
            </view>
            <scroll-view class="detail__main__content__scroll" scroll-y>
              <mp-html :content="str" style="width: 100%;float: left;" />
            </scroll-view>
          </view>
        </view>

        <view class="detail__bottom">
          <audio-player />
          <view class="detail__bottom__btn">
            Purchase (42 NZD)
          </view>
        </view>
        <view class="detail__bottom__placeholder" />
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
@import './guide-detail.scss'
</style>
