<script setup lang="ts">
import type { AudioItemType, GuideDetailType } from '@/model/guide'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiGuideCollect, ApiGuideDetail, ApisSettlementDetail } from '@/api/guide'
import priceDetailPopup from '@/pages/page-guide/components/price-detail-popup/price-detail-popup.vue'
import { useAudioStore } from '@/stores/audio'
import { gotoPage } from '@/utils/router'
import audioPlayer from './components/audio-player/audio-player.vue'
import guideBuyerPopup from './components/guide-buyer-popup/guide-buyer-popup.vue'
import langSelect from './components/lang-select/lang-select.vue'

const audioStore = useAudioStore()
const id = ref<number>(0)
const detail = ref<GuideDetailType>({} as GuideDetailType)
const { t } = useI18n()

const price = computed<string>(() => {
  if (detail.value.price?.length) {
    return detail.value.price[0].price
  }
  else {
    return '0'
  }
})

// 收藏
async function collect() {
  await ApiGuideCollect({
    guide_id: id.value,
    type: detail.value.info.is_collect === 0 ? 1 : 0,
  })
  uni.showToast({
    title: t('guide.collect_tips'),
    icon: 'none',
  })
  getDetail()
}

// 获取播放详情和电子导游详情
async function getDetail() {
  const res = await ApiGuideDetail({ guide_id: id.value })
  res.audiolist.forEach((item: AudioItemType) => {
    item.image = res.info.image
    item.is_buy = res.info.is_buy
    item.trial_duration = 1
  })
  detail.value = res
}

// 购买弹窗
const buyerShow = ref<boolean>(false)
const priceShow = ref<boolean>(false)
const prevviewShow = ref<boolean>(false)
// 试听结束
function onTrialEnd() {
  prevviewShow.value = true
}
async function handlePriceDetail(e: any) {
  const res = await ApisSettlementDetail(e)
  console.log('result', res)
  priceShow.value = true
}
function handleClose() {
  if (audioStore.playInfo.progressPercent < audioStore.playInfo?.previewRatio) {
    audioStore.status.isPreview = false
  }
}
function handlePurchase() {
  prevviewShow.value = false
  buyerShow.value = true
}
// 确认购买
function handleBuy() {
  console.warn('确认要购买，提交订单')
}

onLoad(async (e: any) => {
  if (e.id) {
    id.value = e.id
    await getDetail()
  }
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="custom-bar">
        <text class="custom-bar_title fs--16--700">
          {{ detail.info?.name }}
        </text>
        <view class="custom-bar_right">
          <view class="custom-bar_right_icon" @click="gotoPage(`/subPackages/page-mine/feedback/feedback`)">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="danger-circle-light" />
          </view>
          <view class="custom-bar_right_icon" @click="collect">
            <zui-svg-icon
              width="48rpx"
              height="48rpx"
              color="#000"
              :icon="detail?.info?.is_collect === 0 ? 'heart-light' : 'heart' "
            />
          </view>
          <view class="custom-bar_right_icon">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="logout-light" />
          </view>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="detail">
        <view class="detail__swiper">
          <image class="detail__swiper__image" :src="detail.info?.image" mode="aspectFill" />
        </view>

        <view class="detail__main">
          <view class="detail__main__content">
            <view class="detail_title">
              <view class="detail_title_left">
                <view class="fs--14--700">
                  Introduction
                </view>
                <view v-if="audioStore.status.isPlaying" class="detail_title_left_tag fs--10--400">
                  Explaining
                </view>
              </view>
              <!-- 语言切换 -->
              <lang-select :list="detail.audiolist" />
            </view>
            <scroll-view class="detail__main__content__scroll" scroll-y>
              <mp-html :content="audioStore.currentAudio?.duction" style="width: 100%;float: left;" />
            </scroll-view>
          </view>
        </view>

        <view class="detail__bottom">
          <audio-player :list="detail.audiolist" @on-trial-end="onTrialEnd" />
          <view class="detail__bottom__btn" @click="buyerShow = true">
            Purchase ({{ price }} NZD)
          </view>
        </view>
        <view class="detail__bottom__placeholder" />
      </view>
    </view>

    <!-- 试听到期提示 -->
    <confirm-popup
      v-model="prevviewShow"
      title="Audition end notification"
      content="Your trial has ended. Purchase this audio to listen to the full version."
      confirm-text="Purchase"
      cancel-text="Not yet"
      height="350rpx"
      @confirm="handlePurchase"
      @close="handleClose"
    />
    <!-- 购买弹窗 -->
    <guide-buyer-popup
      v-model="buyerShow"
      :detail="detail"
      @detail="handlePriceDetail"
      @submit="handleBuy"
    />
    <!-- 价格详情 -->
    <price-detail-popup v-model="priceShow" />
  </page>
</template>

<style scoped lang="scss">
@import './guide-detail.scss'
</style>
