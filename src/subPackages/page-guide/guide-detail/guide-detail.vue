<script setup lang="ts">
import type { AudioItemType, GuideDetailType, PriceInfoType } from '@/model/guide'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiGuideCollect, ApiGuideDetail, ApisSettlementDetail } from '@/api/guide'
import priceDetailPopup from '@/pages/page-guide/components/price-detail-popup/price-detail-popup.vue'
import { useAudioStore } from '@/stores/audio'
import { goBack, gotoPage } from '@/utils/router'
import audioPlayer from './components/audio-player/audio-player.vue'
import guideBuyerPopup from './components/guide-buyer-popup/guide-buyer-popup.vue'
import langSelect from './components/lang-select/lang-select.vue'

const audioStore = useAudioStore()
const id = ref<number>(0)
const detail = ref<GuideDetailType>({} as GuideDetailType)
const { t } = useI18n()

const price = computed(() => detail.value.price?.[0]?.price ?? '0')

// 收藏
async function collect() {
  const isCollect = detail.value.info.is_collect === 1
  await ApiGuideCollect({ guide_id: id.value, type: isCollect ? 0 : 1 })
  uni.showToast({ title: t('guide.collect_tips'), icon: 'none' })
  await getDetail()
}

// 获取播放详情和电子导游详情
async function getDetail() {
  const res = await ApiGuideDetail({ guide_id: id.value })
  res.audiolist.forEach((item: AudioItemType) => {
    item.image = res.info.image
    item.is_buy = res.info.is_buy
  })
  res.audiolist[1].file_url = 'https://web-ext-storage.dcloud.net.cn/uni-app/ForElise.mp3'
  res.audiolist[2].file_url = 'https://www.cambridgeenglish.org/images/153149-movers-sample-listening-test-vol2.mp3'
  detail.value = res
}

// 当前选中语言
const currentLang = ref<AudioItemType>({} as AudioItemType)

// 弹窗控制
const popup = reactive({
  buyer: false,
  price: false,
  preview: false,
})
// 试听结束
function onTrialEnd() {
  popup.preview = true
}

const priceInfo = ref<PriceInfoType>({} as PriceInfoType)
async function handlePriceDetail(e: any) {
  priceInfo.value = await ApisSettlementDetail({
    data: JSON.stringify(e.data),
    point: e.point,
  })
  popup.price = true
}

function resetPreview() {
  if (audioStore.playInfo.progressPercent < audioStore.playInfo?.previewRatio) {
    audioStore.status.isPreview = false
  }
}
function openBuyerPopup() {
  popup.preview = false
  popup.buyer = true
}

// 是否正在播放 👇
const isExplaining = computed<boolean>(() =>
  audioStore.status.isPlaying && audioStore.currentAudio?.id === currentLang.value.id,
)
// 是否正在播放 👆

onLoad(async (e: any) => {
  if (e.id) {
    id.value = e.id
    await getDetail()
    // 默认每次不挂载
    audioStore.status.isTabbar = false
  }
})

onUnload(() => {
  if (!audioStore.status.isTabbar) {
    audioStore.clearAudio()
  }
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="custom-bar">
        <text class="custom-bar_title fs--16--700">
          {{ detail.info?.name }}
        </text>
        <view class="custom-bar_right">
          <view class="custom-bar_right_icon" @click="gotoPage(`/subPackages/page-mine/feedback/feedback`)">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="danger-circle-light" />
          </view>
          <view class="custom-bar_right_icon" @click="collect">
            <zui-svg-icon
              width="48rpx"
              height="48rpx"
              color="#000"
              :icon="detail?.info?.is_collect === 0 ? 'heart-light' : 'heart' "
            />
          </view>
          <view class="custom-bar_right_icon" @click="goBack">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="logout-light" />
          </view>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="detail">
        <view class="detail__swiper">
          <image class="detail__swiper__image" :src="detail.info?.image" mode="aspectFill" />
        </view>

        <view class="detail__main">
          <view class="detail__main__content">
            <view class="detail_title">
              <view class="detail_title_left">
                <view class="fs--14--700">
                  {{ currentLang.name }}
                </view>
                <view
                  v-if="isExplaining"
                  class="detail_title_left_tag fs--10--400"
                >
                  {{ t('guide.explaining') }}
                </view>
              </view>
              <!-- 语言切换 -->
              <lang-select v-model="currentLang" :list="detail.audiolist" />
            </view>
            <scroll-view class="detail__main__content__scroll" scroll-y>
              <mp-html :content="currentLang?.duction" style="width: 100%;float: left;" />
            </scroll-view>
          </view>
        </view>

        <view class="detail__bottom">
          <!-- 音频操作栏 -->
          <audio-player :list="detail.audiolist" @on-trial-end="onTrialEnd" />
          <view class="detail__bottom__btn" @click="popup.buyer = true">
            {{ t('guide.purchases') }} ({{ price }} {{ t('guide.unit') }})
          </view>
        </view>
        <view class="detail__bottom__placeholder" />
      </view>
    </view>

    <!-- 试听到期提示 -->
    <confirm-popup
      v-model="popup.preview"
      :title="t('guide.trialTitle')"
      :content="t('guide.trialContent')"
      :confirm-text="t('guide.purchases')"
      :cancel-text="t('guide.notYet')"
      height="350rpx"
      @confirm="openBuyerPopup"
      @close="resetPreview"
    />
    <!-- 购买弹窗 -->
    <guide-buyer-popup
      v-model="popup.buyer"
      :detail="detail"
      @detail="handlePriceDetail"
    />
    <!-- 价格详情 -->
    <price-detail-popup v-model="popup.price" :info="priceInfo" />
  </page>
</template>

<style scoped lang="scss">
@import './guide-detail.scss'
</style>
