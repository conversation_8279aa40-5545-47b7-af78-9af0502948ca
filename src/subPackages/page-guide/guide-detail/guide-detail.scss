.custom-bar{
	width: 100%;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	
	&_title{
		line-height: 52rpx;
		color: #111B19;
	}
	
	&_right{
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex-direction: row;
		
		&_icon{
			width: 42rpx;
			height: 42rpx;
			margin-left: 40rpx;
			
			&:first-child{
				margin-left: 0;
			}
		}
	}
}

.detail{
	padding: 0 32rpx;
	
	&__swiper{
		padding-top: 24rpx;
	}
	
	&__main{
		border-radius: 24rpx;
		background-color: #FAFAFA;
		padding: 32rpx 22rpx;
		margin: 32rpx 0;
		
		&__content{
			background-color: #fff;
			border-radius: 24rpx;
			padding: 24rpx 24rpx 0 24rpx;
			
			&__scroll{
				height: 520rpx !important;
				background-color: #fff;
                font-size: 24rpx;
                font-weight: 400;
                font-family: Alimama FangYuanTi VF;
			}
		}
	}
	
	&__audio{
		margin: 32rpx 0;
	}
	
	&_title{
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: row;
		margin-bottom: 24rpx;
		width: 100%;
		
		&_left{
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: row;
		
			
			&_tag{
				margin-left: 18rpx;
				background-color: #D5D7DA;
				padding: 10rpx 20rpx;
				border-radius: 10rpx;
			}

		}
		
		&_right{
			width: 180rpx;
		}
	}

	&__bottom{
		position: fixed;
		left: 0;
		bottom: 0;
		background-color: #fff;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		width: 750rpx;
		padding-left: 32rpx;
		padding-right: 32rpx;
		padding-top: 20rpx;
		z-index: 1000;
		&__btn{
			background-color: #103A62;
			padding: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			border-radius: 24rpx;
			margin-top: 32rpx;
		}

		&__placeholder{
			width: 100%;
			height: 301rpx;
		}
	}
}