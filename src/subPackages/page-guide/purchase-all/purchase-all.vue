<script setup lang="ts">
import { ref } from 'vue'
import guideItem from '@/pages/page-guide/components/guide-item/guide-item.vue'
import priceDetailPopup from '@/pages/page-guide/components/price-detail-popup/price-detail-popup.vue'

const show = ref<boolean>(false)

function openPriceDetail() {
  show.value = true
}
</script>

<template>
  <page>
    <nav-bar-page title="Purchase all" />
    <view class="body">
      <view class="list">
        <guide-item v-for="(item, index) in 10" :key="index" />
      </view>
    </view>

    <!-- 底部栏 -->
    <fixed-bottom-bar>
      <view class="list__bottom">
        <view class="list__bottom__left">
          Total: 168 NZD
        </view>
        <view class="list__bottom__right">
          <view class="list__bottom__right__detail" @tap="openPriceDetail">
            Details
          </view>
          <view class="list__bottom__right__btn">
            Pay (00:58:56)
          </view>
        </view>
      </view>
    </fixed-bottom-bar>

    <price-detail-popup v-model="show" />
  </page>
</template>

<style scoped lang="scss">
.list{
    padding: 0 32rpx 32rpx 32rpx;

    &__bottom{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 90rpx;
        padding: 20rpx 32rpx 0 32rpx;
        &__left{
            color: #103A62;
            font-weight: 600;
            line-height: 40rpx;
            font-size: 32rpx;
        }

        &__right{
            display: flex;
            align-items: center;

            &__detail{
                font-size: 24rpx;
                font-weight: 500;
                line-height: 40rpx;
                color:#717680;
            }

            &__btn{
                margin-left: 20rpx;
                height: 48rpx;
                padding: 0 16rpx;
                color: #fff;
                background-color: #103A62;
                line-height: 48rpx;
                text-align: center;
                border-radius: 10rpx;
                font-size: 24rpx;
                font-weight: 600;
            }
        }
    }
}
</style>
