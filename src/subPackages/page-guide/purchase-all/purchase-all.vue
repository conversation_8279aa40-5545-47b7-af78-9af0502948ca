<script setup lang="ts">
import type { GuideListItemType, PriceInfoType } from '@/model/guide'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiConfigPack, ApiGuideList, ApisSettlementDetail } from '@/api/guide'
import { useList } from '@/hooks/useList'
import guideItem from '@/pages/page-guide/components/guide-item/guide-item.vue'
import priceDetailPopup from '@/pages/page-guide/components/price-detail-popup/price-detail-popup.vue'
import buyerPopup from './components/buyer-popup/buyer-popup.vue'

const { t } = useI18n()

const { list, refresh, loadMore } = useList<GuideListItemType>(ApiGuideList, { page_no: 1, page_size: 10000 })
const popup = reactive({
  showDetail: false,
  buyer: false,
})
const infoList = ref<any[]>([])
async function getConfigPackPrice() {
  const res = await ApiConfigPack()
  infoList.value = [
    { day: 3, price: Number.parseFloat(res.three_days_price) * list.value.length },
    { day: 10, price: Number.parseFloat(res.ten_days_price) * list.value.length },
    { day: 30, price: Number.parseFloat(res.thirty_days_price) * list.value.length },
  ]
}

const active = ref<number>(0)
const point = ref<number | undefined>()
const priceInfo = ref<PriceInfoType>({} as PriceInfoType)
async function handleDetail() {
  const pamams = {
    data: JSON.stringify(list.value.map((item: any) => {
      return {
        id: item.id,
        guide_price_id: 0,
        num: 1,
      }
    })),
    pack_price: infoList.value[active.value].price,
    pack_day: infoList.value[active.value].day,
    points: point.value,
  }
  priceInfo.value = await ApisSettlementDetail(pamams)
  popup.showDetail = true
}

async function handlePay() {
  // const pamams = {
  //   data: JSON.stringify(list.value.map((item: any) => {
  //     return {
  //       id: item.id,
  //       guide_price_id: 0,
  //       num: 1,
  //     }
  //   })),
  //   pack_price: infoList.value[active.value].price,
  //   pack_day: infoList.value[active.value].day,
  //   points: point.value,
  // }
  // const res = await ApiSaveOrder(pamams)
  // console.log(res)
}
onReachBottom(() => {
  loadMore()
})

onLoad(async () => {
  await refresh()
  getConfigPackPrice()
})
</script>

<template>
  <page>
    <nav-bar-page :title="t('guide.purchaseAll')" />
    <view class="body">
      <view class="list">
        <guide-item v-for="(item) in list" :key="item.id" :item="item" />
      </view>
    </view>

    <!-- 底部栏 -->
    <fixed-bottom-bar>
      <view class="list__bottom">
        <view class="list__bottom__left">
          {{ t('guide.total') }}: {{ infoList[active]?.price }} NZD
        </view>
        <view class="list__bottom__right">
          <view class="list__bottom__right__btn" @click="popup.buyer = true">
            {{ t('guide.purchases') }}
          </view>
        </view>
      </view>
    </fixed-bottom-bar>

    <buyer-popup
      v-model="popup.buyer"
      v-model:active="active"
      v-model:point="point"
      :list="infoList"
      @detail="handleDetail"
      @submit="handlePay"
    />

    <price-detail-popup v-model="popup.showDetail" :info="priceInfo" />
  </page>
</template>

<style scoped lang="scss">
.list{
    padding: 0 32rpx 32rpx 32rpx;

    &__bottom{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 90rpx;
        padding: 20rpx 32rpx 0 32rpx;
        &__left{
            color: #103A62;
            font-weight: 600;
            line-height: 40rpx;
            font-size: 32rpx;
        }

        &__right{
            display: flex;
            align-items: center;

            &__btn{
                margin-left: 20rpx;
                height: 52rpx;
                padding:0 32rpx;
                color: #fff;
                background-color: #103A62;
                line-height: 52rpx;
                text-align: center;
                border-radius: 10rpx;
                font-size: 24rpx;
                font-weight: 600;
            }
        }
    }
}
</style>
