<script lang="ts" setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { gotoPage } from '@/utils/router'

// 价格列表
const props = withDefaults(defineProps<{
  list?: any[]
}>(), {
  list: () => [],
})

const emit = defineEmits(['detail', 'submit'])
const { t } = useI18n()
const active = defineModel<number>('active', { default: 0 }) // 所选
const point = defineModel<number | undefined>('point', { default: undefined }) // 积分
const show = defineModel<boolean>('modelValue', { default: false })

const userStore = useUserStore()

const price = computed(() => {
  return props.list[active.value].price
})

function selectModel(i: number) {
  active.value = i
}

async function sendOrder(type: 'prepare' | 'pay') {
  if (!userStore.token) {
    return gotoPage(`/subPackages/page-mine/login/login`)
  }
  // 积分校验
  if (point.value && userStore.userInfo?.points && point.value > userStore.userInfo?.points) {
    return uni.showToast({ title: t('guide.pointTips'), icon: 'none' })
  }
  type === 'prepare' ? emit('detail') : emit('submit')
}
</script>

<template>
  <base-popup v-model="show" is-footer title="Switch audio" height="600rpx">
    <view class="popup__buyer">
      <view class="popup__buyer__top">
        <view
          v-for="(item, index) in list" :key="index" class="popup__buyer__top__item"
          :class="{ active: active === index }" @tap="selectModel(index)"
        >
          <text class="popup__buyer__top__item__title fs--14--400">
            {{ item.day }} {{ t('guide.days') }}
          </text>
          <text class="popup__buyer__top__item__price fs--14--400">
            {{ item.price }} {{ t('guide.unit') }}
          </text>
        </view>
      </view>
      <view class="popup__buyer__bottom">
        <text class="popup__buyer__bottom__text fs--12--400">
          points deduction (current remaining:  {{ userStore.userInfo?.points || 0 }})
        </text>
        <input
          v-model="point" type="number" class="popup__buyer__bottom__input"
          placeholder-class="popup__buyer__bottom__input__text" placeholder="Enter amount (maximum 20 points)"
        >
      </view>
    </view>
    <template #footer>
      <view class="popup__buyer__footer">
        <view class="popup__buyer__footer__btn left" @click.stop="sendOrder('prepare')">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            {{ t('guide.detail') }}
          </text>
        </view>
        <view class="popup__buyer__footer__btn right" @click.stop="sendOrder('pay')">
          <text class="fs--14--400 popup__buyer__footer__btn__text">
            {{ t('guide.purchases') }} ({{ price }}  {{ t('guide.unit') }})
          </text>
        </view>
      </view>
    </template>
  </base-popup>
</template>

<style lang="scss" scoped>
@import './buyer-popup.scss'
</style>
