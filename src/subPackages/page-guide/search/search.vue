<script setup lang="ts">
import type { GuideListItemType } from '@/model/guide'
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiGuideList } from '@/api/guide'
import { useList } from '@/hooks/useList'
import guideItem from '@/pages/page-guide/components/guide-item/guide-item.vue'
import { goBack } from '@/utils/router'
import searchHistory from './components/search-history/search-history.vue'
import searchHotKeyword from './components/search-hot-keywrod/search-hot-keywrod.vue'

const { t } = useI18n()
const name = ref<string>('')
const isSearch = ref<boolean>(false)

const { list, setParams } = useList<GuideListItemType>(ApiGuideList, { name: name.value })

watch(() => name.value, (val: string | undefined) => {
  if (!val || val.trim().length === 0) {
    isSearch.value = false
  }
})

// 搜索
function search() {
  setKeyword(name.value)
}

// 设置搜索历史
const historyRef = ref<any>(null)
async function setKeyword(kw: string) {
  name.value = kw
  historyRef.value?.addHistory(kw)
  await setParams({ name: name.value })
  isSearch.value = true
}

// 清空
function clearKeyword() {
  isSearch.value = false
}
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="nav-search">
        <search-input
          v-model="name"
          :text="t('guide.cancel')"
          text-class="default"
          @action-click="goBack"
          @search="search"
          @clear="clearKeyword"
        />
      </view>
    </nav-bar-page>

    <view class="body">
      <!-- 搜索历史 -->
      <template v-if="!isSearch">
        <search-history ref="historyRef" store-key="guide-search-history" @on-click="setKeyword" />
        <search-hot-keyword @on-click="setKeyword" />
      </template>
      <template v-else>
        <view v-if="list.length > 0" style="padding: 10rpx 32rpx 32rpx 32rpx;">
          <guide-item v-for="(item) in list" :key="item.id" :item="item" />
        </view>
        <empty v-else :description="t('guide.notContent')" />
      </template>
    </view>
  </page>
</template>

<style scoped lang="scss">
.nav-search{
    width: 100%;
}
</style>
