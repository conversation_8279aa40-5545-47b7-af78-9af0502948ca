.history {
		padding: 48rpx 32rpx;

		&__title {
			display: flex;
			align-items: center;
			flex-direction: row;
			justify-content: space-between;

			&__name {
				color: #161616;
				font-size: 30rpx !important;
				font-weight: 700 !important;
			}

			&__icon {
				width: 32rpx;
				height: 32rpx;
			}
		}

		&__list {
			padding-top: 32rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			flex-wrap: wrap;

			&__item {
				padding: 14rpx 32rpx;
				background-color: #F7F7F7;
				border-radius: 20rpx;
				margin-right: 24rpx;
				margin-bottom: 24rpx;

				&__text {
					color: #161616;
					font-size: 26rpx !important;
					font-weight: 400 !important;
				}
			}
		}
	}