<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const props = withDefaults(defineProps<{
  storeKey?: string
}>(), {
  storeKey: '',
})
const emit = defineEmits(['onClick'])
const { t } = useI18n()
const list = ref<string[]>([]) // 列表

// 加载本地缓存
function loadHistory() {
  const local = uni.getStorageSync(props.storeKey)
  // 判断是否为非空字符串再解析
  if (typeof local == 'string' && local.trim().length > 0) {
    try {
      const parsed = JSON.parse(local) as string[]
      if (Array.isArray(parsed)) {
        list.value = parsed
      }
      else {
        list.value = []
      }
    }
    catch (e: any) {
      console.error(e)
      list.value = []
    }
  }
  else {
    list.value = []
  }
}
loadHistory()

function itemClick(item: string) {
  emit('onClick', item)
}

function clearHistory() {
  uni.removeStorageSync(props.storeKey)
  list.value = []
}

// 添加搜索历史项（供外部调用）
function addHistory(keyword: string) {
  if (keyword == null || keyword.trim().length === 0)
    return

  // 移除已有的
  list.value = list.value.filter(item => item !== keyword)

  // 插入到最前
  list.value.unshift(keyword)

  // 最多保留 10 条
  if (list.value.length > 10) {
    list.value = list.value.slice(0, 10)
  }

  // 保存到本地
  uni.setStorageSync(props.storeKey, JSON.stringify(list.value))
}

onMounted(() => {
  loadHistory()
})

defineExpose({
  addHistory,
})
</script>

<template>
  <view class="history">
    <view class="history__title">
      <text class="history__title__name">
        {{ t('guide.searchHistory') }}
      </text>
      <view class="history__title__icon" @tap="clearHistory">
        <zui-svg-icon width="32rpx" height="32rpx" color="#000" icon="delete-light" />
      </view>
    </view>

    <view class="history__list">
      <view v-for="(item, index) in list" :key="index" class="history__list__item" @tap="itemClick(item)">
        <text class="history__list__item__text">
          {{ item }}
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './search-history.scss'
</style>
