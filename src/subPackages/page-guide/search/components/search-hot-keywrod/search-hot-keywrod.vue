<script lang="ts" setup>
import { ref } from 'vue'

const emit = defineEmits(['onClick'])
const list = ref<any[]>([
  { label: 'Travel', value: 'Travel' },
  { label: 'Tourism', value: 'Tourism' },
  { label: 'Accommodation', value: 'Accommodation' },
  { label: 'Transportation', value: 'Transportation' },
  { label: 'Activities', value: 'Activities' },
  { label: 'Events', value: 'Events' },
])

function itemClick(item: any) {
  emit('onClick', item?.value || '')
}
</script>

<template>
  <view class="history">
    <view class="history__title">
      <text class="history__title__name">
        Popular searches
      </text>
    </view>

    <view class="history__list">
      <view v-for="(item, index) in list" :key="index" class="history__list__item" @tap="itemClick(item)">
        <text class="history__list__item__text">
          {{ item.label }}
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../search-history/search-history.scss'
</style>
