<script setup lang="ts">
import { reactive, ref } from 'vue'

const show = defineModel<boolean>('modelValue', { default: false })

const coloumns = ref([
  {
    prop: 'name',
    label: 'Name',
    width: '50%',
    align: 'center',
  },
  {
    prop: 'phone',
    label: 'Phone number',
    width: '50%',
    align: 'center',
    slot: 'phone',
  },
])

const dataList = reactive([
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
  {
    name: 'Name a',
    phone: 'Phone number',
  },
])
</script>

<template>
  <view>
    <base-popup v-model="show" height="1000rpx">
      <view class="wrapper">
        <view class="wrapper__tips">
          Information of passengers who have had their tickets verified
        </view>
        <view class="wrapper__table">
          <base-table style="margin-top: 32rpx;" :data="dataList" :coloumns="coloumns">
            <template #phone="{ row }">
              <view class="site-column">
                <text class="site-column__text">
                  {{ row.phone }}
                </text>
                <image src="/static/driver/calling.png" mode="widthFix" style="width: 36rpx; height: 36rpx;" />
              </view>
            </template>
          </base-table>
        </view>
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
.wrapper{
    padding: 10rpx;

    &__tips{
        color:#91A8BE;
        font-size: 28rpx;
        font-weight: 600;
        line-height: 36rpx;
    }

    .site-column{
        display: flex;
        align-items: center;

        &__text{
            margin-right: 10rpx;
        }
    }
}
</style>
