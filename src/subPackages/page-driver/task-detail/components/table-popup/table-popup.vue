<script setup lang="ts">
import type { PassengersItemType } from '@/model/driver'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const show = defineModel<boolean>('modelValue', { default: false })
const { t } = useI18n()
const coloumns = ref([
  {
    prop: 'name',
    label: 'Name',
    width: '50%',
    align: 'center',
  },
  {
    prop: 'phone',
    label: 'Phone number',
    width: '50%',
    align: 'center',
    slot: 'phone',
  },
])

const dataList = ref<PassengersItemType[]>([])

function open(list: PassengersItemType[]) {
  dataList.value = list.length ? list : []
  show.value = true
}

function callPhone(phone: string) {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

defineExpose({
  open,
})
</script>

<template>
  <view>
    <base-popup v-model="show" height="1000rpx">
      <view class="wrapper">
        <view class="wrapper__tips">
          {{ t('driver.verifiedTips') }}
        </view>
        <view class="wrapper__table">
          <base-table v-if="dataList.length > 0" style="margin-top: 32rpx;" :data="dataList" :coloumns="coloumns">
            <template #phone="{ row }">
              <view class="site-column" @click.stop="callPhone(row.phone)">
                <text class="site-column__text">
                  {{ row.phone }}
                </text>
                <image src="/static/driver/calling.png" mode="widthFix" style="width: 36rpx; height: 36rpx;" />
              </view>
            </template>
          </base-table>
          <empty v-else :description="t('driver.notVerified')" height="500rpx" />
        </view>
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
.wrapper{
    padding: 10rpx;

    &__tips{
        color:#91A8BE;
        font-size: 28rpx;
        font-weight: 600;
        line-height: 36rpx;
    }

    .site-column{
        display: flex;
        align-items: center;

        &__text{
            margin-right: 10rpx;
        }
    }
}
</style>
