<script setup lang="ts">
import { ref } from 'vue'

const current = ref<number>(-1)
</script>

<template>
  <view class="time-table-container">
    <view class="time-table-container__header">
      <text class="time-table-container__header__title">
        Timetable
      </text>
    </view>

    <view class="step-list">
      <wd-steps
        v-model="current"
        dot
        vertical
      >
        <wd-step v-for="(item, idx) in 10" :key="idx">
          <template #description>
            <view class="step-list__item">
              <view class="step-list__item__title">
                <view class="step-list__item__title__index">
                  {{ idx + 1 }}
                </view>
                <view class="step-list__item__title__text">
                  Keynesian
                </view>
                <view class="step-list__item__title__tag normal">
                  Normal
                </view>
                <!-- <view class="step-list__item__title__tag delay">
                  Delay
                </view> -->
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  On time
                </view>
                <view class="step-list__item__flex__time">
                  8:20
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  Open it up
                </view>
                <view class="step-list__item__flex__time">
                  8:20
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  Actual arrival time
                </view>
                <view class="step-list__item__flex__time">
                  8:20
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  Actual departure time
                </view>
                <view class="step-list__item__flex__time">
                  8:20
                </view>
              </view>
            </view>
          </template>
        </wd-step>
      </wd-steps>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './time-table.scss'
</style>
