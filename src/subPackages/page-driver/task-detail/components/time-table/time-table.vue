<script setup lang="ts">
import type { TaskDetailType } from '@/model/driver'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiCheckInArrival, ApiCheckInDeparture, ApiCoachLocation } from '@/api/driver'
import { useLocationPolling } from '@/hooks/useLocationPolling'

const props = withDefaults(defineProps<{
  detail?: TaskDetailType
}>(), {
  detail: () => {
    return {} as TaskDetailType
  },
})

const emits = defineEmits(['onRefresh'])

const { t } = useI18n()

const current = ref<number>(-1)

const { startLocationPolling, stopLocationPolling } = useLocationPolling()

async function handleSetoff() {
  uni.showModal({
    title: t('driver.kindReminder'),
    content: t('driver.setOffTips'),
    confirmText: t('driver.confirm'),
    cancelText: t('driver.confirm'),
    success: async (res: any) => {
      if (res.confirm) {
        await ApiCheckInDeparture({ id: props.detail.id })
        uni.showToast({
          title: 'Operation successful',
          icon: 'none',
        })
        startLocationPolling(10000, async (lat: number, lng: number) => {
          await ApiCoachLocation({
            longitude: lng,
            latitude: lat,
            coach_id: props.detail.id,
          })
        })
        emits('onRefresh')
      }
    },
  })
}

async function handleArrive() {
  uni.showModal({
    title: t('driver.kindReminder'),
    content: t('driver.arrivalTips'),
    confirmText: t('driver.confirm'),
    cancelText: t('driver.confirm'),
    success: async (res: any) => {
      if (res.confirm) {
        await ApiCheckInArrival({ id: props.detail.id })
        uni.showToast({
          title: 'Operation successful',
          icon: 'none',
        })
        stopLocationPolling()
        emits('onRefresh')
      }
    },
  })
}
</script>

<template>
  <view class="time-table-container">
    <view class="time-table-container__header">
      <text class="time-table-container__header__title">
        {{ t('driver.timeTableTitle') }}
      </text>
    </view>

    <view class="step-list">
      <wd-steps
        v-model="current"
        dot
        vertical
      >
        <wd-step v-for="(item, idx) in detail.timetable" :key="idx">
          <template #description>
            <view class="step-list__item">
              <view class="step-list__item__title">
                <view class="step-list__item__title__index">
                  {{ idx + 1 }}
                </view>
                <view class="step-list__item__title__text">
                  {{ item.name }}
                </view>
                <view v-if="item.status === 1" class="step-list__item__title__tag normal">
                  {{ t('driver.Normal') }}
                </view>
                <view v-if="item.status === 2" class="step-list__item__title__tag delay">
                  {{ t('driver.Delay') }}
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  {{ t('driver.onTime') }}
                </view>
                <view class="step-list__item__flex__time">
                  {{ item.arrival_time }}
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  {{ t('driver.openItUp') }}
                </view>
                <view class="step-list__item__flex__time">
                  {{ item.departure_time }}
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  {{ t('driver.actualArrivalTime') }}
                </view>
                <view v-if="item.is_arrival_btn" class="step-list__item__flex__btn blue" @click.stop="handleArrive()">
                  {{ t('driver.Arrive') }}
                </view>
                <view v-else class="step-list__item__flex__time">
                  {{ item.true_arrival_time }}
                </view>
              </view>
              <view class="step-list__item__flex">
                <view class="step-list__item__flex__name">
                  {{ t('driver.actualDepartureTime') }}
                </view>
                <view v-if="item.is_departure_btn" class="step-list__item__flex__btn green" @click.stop="handleSetoff()">
                  {{ t('driver.setOff') }}
                </view>
                <view v-else class="step-list__item__flex__time">
                  {{ item.true_departure_time }}
                </view>
              </view>
            </view>
          </template>
        </wd-step>
      </wd-steps>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './time-table.scss'
</style>
