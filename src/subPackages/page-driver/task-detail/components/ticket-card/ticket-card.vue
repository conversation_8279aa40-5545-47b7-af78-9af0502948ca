<script lang="ts" setup>
const emit = defineEmits<{
  (e: 'preview'): void
  (e: 'scanCode'): void
}>()

function handleScanCode() {
  emit('scanCode')
}

function handlePreviewPassenger() {
  emit('preview')
}
</script>

<template>
  <view class="ticket-card-container">
    <ticket-style-card>
      <template #top>
        <view class="ticket-card-container__line">
          <view class="ticket-card-container__line__stop">
            <view class="ticket-card-container__line__stop__name">
              Shenzhen N.
            </view>
            <view class="ticket-card-container__line__stop__date">
              10:00 AM Set off
            </view>
          </view>
          <view class="ticket-card-container__line__center">
            <view class="ticket-card-container__line__center__num fs--14--400">
              D3534
            </view>
            <image class="ticket-card-container__line__center__img" src="/static/mine/location_right.png" />
            <view class="ticket-card-container__line__center__km fs--12--400">
              1h45m
            </view>
          </view>
          <view class="ticket-card-container__line__stop right">
            <view class="ticket-card-container__line__stop__name">
              Cairns
            </view>
            <view class="ticket-card-container__line__stop__date text-right">
              Expected arrival 11:45 AM
            </view>
          </view>
        </view>
      </template>
      <template #bottom>
        <view class="ticket-card-container__desc">
          <view class="ticket-card-container__desc__ticket-check" @click="handleScanCode">
            <text class="ticket-card-container__desc__ticket-check__label">
              Scan the code for ticket verification：
            </text>
            <text class="ticket-card-container__desc__ticket-check__val">
              0/15
            </text>
          </view>
          <view class="ticket-card-container__desc__preview" @click.stop="handlePreviewPassenger">
            <text class="ticket-card-container__desc__preview__text">
              View passengers
            </text>
            <image class="ticket-card-container__desc__preview__icon" src="/static/driver/preview.png" mode="aspectFill" />
          </view>
        </view>
      </template>
    </ticket-style-card>
  </view>
</template>

<style lang="scss" scoped>
@import './ticket-card.scss'
</style>
