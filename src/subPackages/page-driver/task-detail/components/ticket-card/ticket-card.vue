<script lang="ts" setup>
import type { TaskDetailType } from '@/model/driver'
import { useI18n } from 'vue-i18n'

withDefaults(defineProps<{
  detail?: TaskDetailType
}>(), {
  detail: () => {
    return {} as TaskDetailType
  },
})

const emit = defineEmits<{
  (e: 'preview'): void
  (e: 'scanCode'): void
}>()

const { t } = useI18n()

function handleScanCode() {
  emit('scanCode')
}

function handlePreviewPassenger() {
  emit('preview')
}
</script>

<template>
  <view class="ticket-card-container">
    <ticket-style-card>
      <template #top>
        <view class="ticket-card-container__line">
          <view class="ticket-card-container__line__stop">
            <view class="ticket-card-container__line__stop__name">
              {{ detail.ticket?.from_station_name }}
            </view>
            <view class="ticket-card-container__line__stop__date">
              {{ detail.ticket?.departure_time }}
            </view>
          </view>
          <view class="ticket-card-container__line__center">
            <view class="ticket-card-container__line__center__num fs--14--400">
              {{ detail.ticket?.station_distance }}
            </view>
            <image class="ticket-card-container__line__center__img" src="/static/mine/location_right.png" />
            <view class="ticket-card-container__line__center__km fs--12--400">
              {{ detail.ticket?.travel_time }}
            </view>
          </view>
          <view class="ticket-card-container__line__stop right">
            <view class="ticket-card-container__line__stop__name">
              {{ detail.ticket?.to_station_name }}
            </view>
            <view class="ticket-card-container__line__stop__date text-right">
              {{ detail.ticket?.arrival_time }}
            </view>
          </view>
        </view>
      </template>
      <template #bottom>
        <view class="ticket-card-container__desc">
          <view class="ticket-card-container__desc__ticket-check" @click="handleScanCode">
            <text class="ticket-card-container__desc__ticket-check__label">
              {{ t('driver.scanTips') }}
            </text>
            <text class="ticket-card-container__desc__ticket-check__val">
              {{ detail.ticket?.verify_passenger_count }} / {{ detail.ticket?.passenger_count }}
            </text>
          </view>
          <view class="ticket-card-container__desc__preview" @click.stop="handlePreviewPassenger">
            <text class="ticket-card-container__desc__preview__text">
              {{ t('driver.viewPassengers') }}
            </text>
            <image class="ticket-card-container__desc__preview__icon" src="/static/driver/preview.png" mode="aspectFill" />
          </view>
        </view>
      </template>
    </ticket-style-card>
  </view>
</template>

<style lang="scss" scoped>
@import './ticket-card.scss'
</style>
