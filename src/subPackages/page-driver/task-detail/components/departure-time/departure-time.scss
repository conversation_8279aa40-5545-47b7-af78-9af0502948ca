%base {
    font-family: <PERSON><PERSON><PERSON>i VF;
    font-weight: 400;
    font-size: 32rpx;
    margin-bottom: 32rpx;
}
.departure-time-container{
    padding: 32rpx;
    border-radius: 32rpx;
    border: 2rpx solid #cfd6dd;
    &__time{
        @extend %base;
        color: #101010;
        line-height: 48rpx;
    }
    &__desc{
        font-size: 24rpx;
        font-weight: 500;
        line-height: 32rpx;
        color: #252B37;
    }
    &__line{
        border-bottom: 2rpx dashed #d6d6d6;
        margin: 32rpx 0;
        // border: 1px solid red;
    }
    &__btn{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56rpx;
        background: #103A62;
        border-radius: 100rpx;
        padding: 8rpx 24rpx;
        &__text{
            font-family: Alimama FangYuanTi VF;
            font-weight: 700;
            font-size: 28rpx;
            color: #fff;
        }
    }
}