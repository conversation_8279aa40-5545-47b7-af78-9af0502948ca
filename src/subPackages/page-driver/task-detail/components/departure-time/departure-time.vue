<script lang="ts" setup>
function handleArrive() {

}
</script>

<template>
  <view class="departure-time-container">
    <view class="departure-time-container__time">
      The vehicle is expected to depart in 12 minutes
    </view>
    <view class="departure-time-container__desc">
      The exact departure time is subject to the driver clicking the departure button
    </view>
    <view class="departure-time-container__line" />
    <view class="departure-time-container__btn" @click="handleArrive">
      <text class="departure-time-container__btn__text">
        I've got on the bus.
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './departure-time.scss'
</style>
