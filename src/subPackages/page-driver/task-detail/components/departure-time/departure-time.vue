<script lang="ts" setup>
import type { TaskDetailType } from '@/model/driver'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { dayjs } from 'wot-design-uni'
import { ApiCheckInBoarding } from '@/api/driver'

const props = withDefaults(defineProps<{
  detail?: TaskDetailType
}>(), {
  detail: () => {
    return {} as TaskDetailType
  },
})
const emits = defineEmits(['onRefresh'])
const { t } = useI18n()
// 计算显示时间
const showText = computed<string>(() => {
  if (props.detail.is_boarding) {
    return t('driver.alreadyOnBoard')
  }
  const departureTime = props.detail.reserve_departure_time * 1000
  const now = dayjs()
  // 已经过了出发时间
  if (now.isAfter(departureTime)) {
    return t('driver.boardingTimeout')
  }
  const depDate = dayjs(departureTime)
  // 是否为今天
  if (depDate.isSame(now, 'day')) {
    const diffMinutes = depDate.diff(now, 'minute')
    return `${t('driver.tips')} ${diffMinutes} ${t('driver.minutes')}`
  }
  // 其他情况，返回完整时间
  return depDate.format('YYYY/MM/DD HH:mm')
})

async function handleArrive() {
  if (props.detail.is_boarding)
    return
  uni.showModal({
    title: t('driver.kindReminder'),
    content: t('driver.arriveTips'),
    confirmText: t('driver.confirm'),
    cancelText: t('driver.cancel'),
    success: async (res: any) => {
      if (res.confirm) {
        await ApiCheckInBoarding({ id: props.detail.id })
        uni.showToast({
          title: t('driver.successTips'),
          icon: 'none',
        })
        emits('onRefresh')
      }
    },
  })
}
</script>

<template>
  <view class="departure-time-container">
    <view class="departure-time-container__time">
      {{ showText }}
    </view>
    <view class="departure-time-container__desc">
      {{ t('driver.timeTips') }}
    </view>
    <view class="departure-time-container__line" />
    <view class="departure-time-container__btn" @click="handleArrive">
      <text class="departure-time-container__btn__text">
        {{ detail.is_boarding ? `Boarding time: ${detail.boarding_time}` : `I've got on the bus.` }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './departure-time.scss'
</style>
