<script setup lang="ts">
import { ref } from 'vue'
import { goBack, gotoPage } from '@/utils/router'
import departureTime from './components/departure-time/departure-time.vue'
import tablePopup from './components/table-popup/table-popup.vue'
import ticketCard from './components/ticket-card/ticket-card.vue'
import timeTable from './components/time-table/time-table.vue'

const show = ref<boolean>(false)

function handleScanCode() {
  show.value = true
}

function handlePreview() {
  gotoPage(`/subPackages/page-driver/scan-code/scan-code`)
}
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="task-detail-container__nav-bar">
        <view class="task-detail-container__nav-bar">
          <view class="task-detail-container__nav-bar__icon">
            <zui-svg-icon icon="arrow-left2-light" width="48rpx" height="48rpx" @click="goBack" />
          </view>
          <text class="task-detail-container__nav-bar__from">
            Shenzhen N.
          </text>
          <text class="task-detail-container__nav-bar__middle">
            -
          </text>
          <text class="task-detail-container__nav-bar__to">
            Bao'an Center
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="task-detail-container__main">
        <view class="gap">
          <departure-time />
        </view>
        <view class="gap">
          <ticket-card @scan-code="handleScanCode" @preview="handlePreview" />
        </view>
        <view class="gap">
          <time-table />
        </view>
      </view>
    </view>
    <table-popup v-model="show" />
  </page>
</template>

<style scoped lang="scss">
@import './task-detail.scss'
</style>
