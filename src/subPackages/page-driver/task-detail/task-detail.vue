<script setup lang="ts">
import type { TaskDetailType } from '@/model/driver'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiTaskDetail } from '@/api/driver'
import { goBack, gotoPage } from '@/utils/router'
import departureTime from './components/departure-time/departure-time.vue'
import tablePopup from './components/table-popup/table-popup.vue'
import ticketCard from './components/ticket-card/ticket-card.vue'
import timeTable from './components/time-table/time-table.vue'

const show = ref<boolean>(false)

const id = ref<number>(0)

const detail = ref<TaskDetailType>({} as TaskDetailType)

async function getDetail() {
  const res = await ApiTaskDetail({ id: id.value })
  detail.value = res
}

const tablePopupRef = ref<any>(null)

function handleScanCode() {
  show.value = true
  tablePopupRef.value?.open(detail.value.ticket.passengers)
}

function handlePreview() {
  gotoPage(`/subPackages/page-driver/scan-code/scan-code?id=${detail.value.id}`)
}
onLoad(async (e: any) => {
  if (e.id) {
    id.value = Number.parseInt(e.id)
    await getDetail()
  }
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="task-detail-container__nav-bar">
        <view class="task-detail-container__nav-bar">
          <view class="task-detail-container__nav-bar__icon">
            <zui-svg-icon icon="arrow-left2-light" width="48rpx" height="48rpx" @click="goBack" />
          </view>
          <text class="task-detail-container__nav-bar__from">
            {{ detail?.ticket?.from_station_name }}
          </text>
          <text class="task-detail-container__nav-bar__middle">
            -
          </text>
          <text class="task-detail-container__nav-bar__to">
            {{ detail?.ticket?.to_station_name }}
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="task-detail-container__main">
        <view class="gap">
          <departure-time :detail="detail" @on-refresh="getDetail" />
        </view>
        <view class="gap">
          <ticket-card :detail="detail" @scan-code="handleScanCode" @preview="handlePreview" />
        </view>
        <view class="gap">
          <time-table :detail="detail" @on-refresh="getDetail" />
        </view>
      </view>
    </view>
    <table-popup ref="tablePopupRef" v-model="show" />
  </page>
</template>

<style scoped lang="scss">
@import './task-detail.scss'
</style>
