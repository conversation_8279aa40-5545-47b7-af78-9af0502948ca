<script setup lang="ts">
import type { TaskDetailType } from '@/model/driver'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiTaskDetail, ApiVerify } from '@/api/driver'
import { goBack } from '@/utils/router'
import currentSite from './current-site/current-site.vue'

const id = ref<number>(0)
const { t } = useI18n()
const detail = ref<TaskDetailType>({} as TaskDetailType)

const code = ref('')

async function getDetail() {
  const res = await ApiTaskDetail({ id: id.value })
  detail.value = res
}

function handleScanCode() {
  uni.scanCode({
    success(e: any) {
      code.value = e.result
    },
    fail() {
      uni.showToast({
        title: 'Scan failed',
        icon: 'none',
      })
    },
  })
}

async function submit() {
  if (!code.value || !code.value.trim()) {
    return uni.showToast({ title: 'Please enter the ticket code', icon: 'none' })
  }
  await ApiVerify({ ticktet_code: code.value })
  uni.showToast({
    title: t('driver.successTips'),
    icon: 'none',
  })
  getDetail()
}

onLoad((e: any) => {
  if (e.id) {
    id.value = Number.parseInt(e.id)
    getDetail()
  }
})
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="scan-code-container__nav-bar">
        <view class="scan-code-container__nav-bar">
          <view class="scan-code-container__nav-bar__icon" @click="goBack">
            <zui-svg-icon icon="arrow-left2-light" width="48rpx" height="48rpx" />
          </view>
          <text class="scan-code-container__nav-bar__from">
            {{ detail?.ticket?.from_station_name }}
          </text>
          <text class="scan-code-container__nav-bar__middle">
            -
          </text>
          <text class="scan-code-container__nav-bar__to">
            {{ detail?.ticket?.to_station_name }}
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="scan-code-container__main">
        <view class="scan-code-container__main__scan" @click="handleScanCode">
          <image class="scan-code-container__main__scan__img" src="/static/driver/scan.png" mode="aspectFill" />
        </view>
        <view class="scan-code-container__main__ticket">
          <text class="scan-code-container__main__ticket__label">
            {{ t('driver.ticketId') }}
          </text>
          <input class="scan-code-container__main__ticket__val" type="text" placeholder="Please Input" :value="code">
        </view>
        <view style="margin-bottom: 20rpx;">
          <current-site :detail="detail" />
        </view>
      </view>
    </view>
    <fixed-bottom-bar>
      <view class="bottom_bar" @click.stop="submit">
        <view class="bottom_bar__btn">
          {{ t('driver.confirmWriteOff') }}
        </view>
      </view>
    </fixed-bottom-bar>
  </page>
</template>

<style scoped lang="scss">
@import './scan-code.scss'
</style>
