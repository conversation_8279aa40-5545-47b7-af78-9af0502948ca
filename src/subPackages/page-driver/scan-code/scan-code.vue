<script setup lang="ts">
import { ref } from 'vue'
import { goBack } from '@/utils/router'
import currentSite from './current-site/current-site.vue'

const ticketId = ref('')

function handleScanCode() {
  uni.scanCode({
    success() {
      console.warn('success')
    },
    fail() {
      console.warn('fail')
    },
    complete() {
      console.warn('complete')
    },
  })
}
</script>

<template>
  <page>
    <nav-bar-page is-custom>
      <view class="scan-code-container__nav-bar">
        <view class="scan-code-container__nav-bar">
          <view class="scan-code-container__nav-bar__icon" @click="goBack">
            <zui-svg-icon icon="arrow-left2-light" width="48rpx" height="48rpx" />
          </view>
          <text class="scan-code-container__nav-bar__from">
            Shenzhen N.
          </text>
          <text class="scan-code-container__nav-bar__middle">
            -
          </text>
          <text class="scan-code-container__nav-bar__to">
            Bao'an Center
          </text>
        </view>
      </view>
    </nav-bar-page>
    <view class="body">
      <view class="scan-code-container__main">
        <view class="scan-code-container__main__scan" @click="handleScanCode">
          <image class="scan-code-container__main__scan__img" src="/static/driver/scan.png" mode="aspectFill" />
        </view>
        <view class="scan-code-container__main__ticket">
          <text class="scan-code-container__main__ticket__label">
            Enter the ticket ID
          </text>
          <input class="scan-code-container__main__ticket__val" type="text" placeholder="Please Input" :value="ticketId">
        </view>
        <view style="margin-bottom: 20rpx;">
          <current-site />
        </view>
      </view>
    </view>
    <fixed-bottom-bar>
      <view class="bottom_bar">
        <view class="bottom_bar__btn">
          Confirm write-off
        </view>
      </view>
    </fixed-bottom-bar>
  </page>
</template>

<style scoped lang="scss">
@import './scan-code.scss'
</style>
