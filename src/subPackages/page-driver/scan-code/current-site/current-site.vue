<script lang="ts" setup>
import type { TaskDetailType } from '@/model/driver'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = withDefaults(defineProps<{
  detail?: TaskDetailType
}>(), {
  detail: () => {
    return {} as TaskDetailType
  },
})

const { t } = useI18n()

const notCount = computed<number>(() => {
  return props.detail.ticket?.passenger_count - props.detail.ticket?.verify_passenger_count
})
</script>

<template>
  <view class="scan-code-container">
    <view class="scan-code-container__header">
      <text class="scan-code-container__header__title">
        {{ t('driver.currentSite') }}
      </text>
      <text class="scan-code-container__header__site">
        {{ detail.ticket?.from_station_name }}
      </text>
    </view>
    <view class="scan-code-container__body">
      <view class="scan-code-container__body__item">
        <text class="scan-code-container__body__item__people">
          {{ detail.ticket?.passenger_count }} {{ t('driver.people') }}
        </text>
        <text class="scan-code-container__body__item__desc">
          {{ t('driver.dueToarrive') }}
        </text>
      </view>
      <view class="scan-code-container__body__item">
        <text class="scan-code-container__body__item__people">
          {{ detail.ticket?.verify_passenger_count }}  {{ t('driver.people') }}
        </text>
        <text class="scan-code-container__body__item__desc">
          {{ t('driver.actualNumber') }}
        </text>
      </view>
      <view class="scan-code-container__body__item border-none">
        <text class="scan-code-container__body__item__people">
          {{ notCount }} {{ t('driver.people') }}
        </text>
        <text class="scan-code-container__body__item__desc">
          {{ t('driver.includedNumber') }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './current-site.scss'
</style>
