<script lang="ts" setup></script>

<template>
  <view class="scan-code-container">
    <view class="scan-code-container__header">
      <text class="scan-code-container__header__title">
        Current site
      </text>
      <text class="scan-code-container__header__site">
        Shenzhen N.
      </text>
    </view>
    <view class="scan-code-container__body">
      <view class="scan-code-container__body__item">
        <text class="scan-code-container__body__item__people">
          5 people
        </text>
        <text class="scan-code-container__body__item__desc">
          The number of people to be present
        </text>
      </view>
      <view class="scan-code-container__body__item">
        <text class="scan-code-container__body__item__people">
          4 people
        </text>
        <text class="scan-code-container__body__item__desc">
          Actual number of people
        </text>
      </view>
      <view class="scan-code-container__body__item border-none">
        <text class="scan-code-container__body__item__people">
          1 people
        </text>
        <text class="scan-code-container__body__item__desc">
          The number of people not included
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './current-site.scss'
</style>
