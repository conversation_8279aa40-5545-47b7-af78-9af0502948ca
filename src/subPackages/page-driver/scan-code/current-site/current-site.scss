.scan-code-container{
    padding: 24rpx;
    border-radius: 24rpx;
    background: #FAFAFA;
    &__header{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 32rpx;
        &__title{
            font-family: <PERSON><PERSON><PERSON> FangYuanTi VF;
            font-weight: 700;
            font-size: 32rpx;
            color: #000;
        }
        &__site{
            font-family: Alimama FangYuanTi VF;
            font-weight: 400;
            font-size: 24rpx;
            color: #103A62;
        }
    }
    &__body{
        &__item{
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            height: 120rpx;
            padding: 16rpx 12rpx;
            border-bottom: 2rpx solid #F5F6F6;
            &__people{
                font-family: Alimama FangYuanTi VF;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 48rpx;
            }
            &__desc{
                font-family: Alimama FangYuanTi VF;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 20px;
                color: #1f1f1f;
            }
        }
    }
}
.border-none{
    border: none;
}