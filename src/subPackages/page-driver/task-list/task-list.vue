<script setup lang="ts">
import type { TabsType } from '@/model/driver'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { dayjs } from 'wot-design-uni'
import { ApiTaskDates, ApiTaskList } from '@/api/driver'
import { useList } from '@/hooks/useList'
import busLine from './components/bus-line/bus-line.vue'
import timeSwiper from './components/time-swiper/time-swiper.vue'

const dates = ref<TabsType[]>([])
const activeDate = ref<number>(0)
const { t } = useI18n()

const { list, loadMore, setParams, loading } = useList(ApiTaskList, { date: activeDate.value })

async function getDates() {
  try {
    const res = await ApiTaskDates({})
    dates.value = res.map((item: number) => {
      return {
        label: dayjs(item * 1000).format('YYYY/MM/DD'),
        value: item,
      }
    })
    activeDate.value = dates.value[0]?.value || 0
    if (activeDate.value) {
      setParams({ date: activeDate.value })
    }
  }
  catch (error) {
    dates.value = []
    console.error(error)
  }
}

function handleChange() {
  nextTick(() => {
    setParams({ date: activeDate.value })
  })
}

onLoad(async () => {
  await getDates()
})

onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <page>
    <nav-bar-page :title="t('driver.taskTitle')" />
    <view class="body">
      <view class="task-list-container__main">
        <view style="margin-bottom: 32rpx;">
          <time-swiper v-model="activeDate" :list="dates" @change="handleChange" />
        </view>

        <bus-line :list="list" :loading="loading" />
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
.task-list-container__main{
    padding: 24rpx 32rpx;
}
</style>
