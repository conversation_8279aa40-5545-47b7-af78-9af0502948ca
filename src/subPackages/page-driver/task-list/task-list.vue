<script setup lang="ts">
import busLine from './components/bus-line/bus-line.vue'
import timeSwiper from './components/time-swiper/time-swiper.vue'
</script>

<template>
  <page>
    <nav-bar-page title="Task list" />
    <view class="body">
      <view class="task-list-container__main">
        <view style="margin-bottom: 32rpx;">
          <time-swiper />
        </view>

        <bus-line />
      </view>
    </view>
  </page>
</template>

<style scoped lang="scss">
.task-list-container__main{
    padding: 24rpx 32rpx;
}
</style>
