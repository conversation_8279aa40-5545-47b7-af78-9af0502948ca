<script setup lang="ts">
import type { TabsType } from '@/model/driver'
import { ref } from 'vue'

withDefaults(defineProps<{
  list?: TabsType[]
}>(), {
  list: () => [],
})

const emits = defineEmits(['change'])

const activeValue = defineModel<number>('modelValue', { default: 0 })
const activeItemId = ref('item0')

function handleSelectTime(idx: number, value: number) {
  if (activeValue.value === value)
    return
  activeItemId.value = `item${idx}`
  activeValue.value = value
  emits('change')
}
</script>

<template>
  <scroll-view
    class="time-swiper-container"
    scroll-x
    :scroll-with-animation="true"
    :show-scrollbar="false"
    :scroll-into-view="activeItemId"
  >
    <view
      v-for="(item, idx) in list"
      :id="`item${idx}`"
      :key="idx"
      class="time-swiper-container__item"
      :class="{ 'time-swiper-container__item--active': activeItemId === `item${idx}` }"
      @click.stop="handleSelectTime(idx, item.value)"
    >
      <text class="time-swiper-container__item__text">
        {{ item.label }}
      </text>
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
@import './time-swiper.scss'
</style>
