<script setup lang="ts">
import { ref } from 'vue'

const activeItemId = ref('item0')

function handleSelectTime(idx: number) {
  activeItemId.value = `item${idx}`
}
</script>

<template>
  <scroll-view
    class="time-swiper-container"
    direction="horizontal"
    :scroll-with-animation="true"
    :show-scrollbar="false"
    :scroll-into-view="activeItemId"
  >
    <view
      v-for="(item, idx) in 6"
      :id="`item${idx}`"
      :key="idx"
      class="time-swiper-container__item"
      :class="{ 'time-swiper-container__item--active': activeItemId === `item${idx}` }"
      @click.stop="handleSelectTime(idx)"
    >
      <text class="time-swiper-container__item__text">
        2025/07/02
      </text>
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
@import './time-swiper.scss'
</style>
