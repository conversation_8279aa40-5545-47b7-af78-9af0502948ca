<script lang="ts" setup>
import type { TaskListItemType } from '@/model/driver'
import { useI18n } from 'vue-i18n'
import busLineCard from '../bus-line-card/bus-line-card.vue'

withDefaults(defineProps<{
  list?: TaskListItemType[]
}>(), {
  list: () => [],
})

const { t } = useI18n()
</script>

<template>
  <view class="bus-line-container">
    <template v-if="list.length > 0">
      <bus-line-card v-for="(item) in list" :key="item.id" :item="item" style="margin-bottom: 48rpx;" />
    </template>
    <empty v-else :description="t('driver.notContent')" />
  </view>
</template>

<style lang="scss" scoped>
</style>
