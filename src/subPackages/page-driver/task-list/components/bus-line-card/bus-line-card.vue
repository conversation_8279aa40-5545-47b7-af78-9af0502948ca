<script lang="ts" setup>
import type { TaskListItemType } from '@/model/driver'
import { gotoPage } from '@/utils/router'

withDefaults(defineProps<{
  item?: TaskListItemType
}>(), {
  item: () => {
    return {} as TaskListItemType
  },
})

function handleGoTaskDetail() {
  gotoPage(`/subPackages/page-driver/task-detail/task-detail?id=6`)
}
</script>

<template>
  <view class="bus-line-card-container">
    <view class="bus-line-card-container__header">
      <text class="bus-line-card-container__header__time">
        {{ item.departure_time_txt }}
      </text>
    </view>
    <view class="bus-line-card-container__body" @click.stop="handleGoTaskDetail">
      <image class="bus-line-card-container__body__icon" src="/static/home/<USER>" mode="aspectFill" />
      <view class="bus-line-card-container__body__text">
        {{ item.route_name }}
        {{ item.from_station_name }}
        -
        {{ item.to_station_name }}
      </view>
      <image class="bus-line-card-container__body__preview-icon" src="/static/driver/preview.png" mode="aspectFill" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './bus-line-card.scss'
</style>
