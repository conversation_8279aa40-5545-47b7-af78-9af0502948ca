<script lang="ts" setup>
import { gotoPage } from '@/utils/router'

function handleGoTaskDetail() {
  gotoPage(`/subPackages/page-driver/task-detail/task-detail`)
}
</script>

<template>
  <view class="bus-line-card-container">
    <view class="bus-line-card-container__header">
      <text class="bus-line-card-container__header__time">
        08:00
      </text>
    </view>
    <view class="bus-line-card-container__body" @click.stop="handleGoTaskDetail">
      <view class="bus-line-card-container__body__left">
        <image class="bus-line-card-container__body__left__icon" src="/static/home/<USER>" mode="aspectFill" />
        <text class="bus-line-card-container__body__left__text">
          East line ZQN-DUN-CHC
        </text>
      </view>
      <image class="bus-line-card-container__body__preview-icon" src="/static/driver/preview.png" mode="aspectFill" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './bus-line-card.scss'
</style>
