<script setup lang="ts">
import { onLaunch, onUnload } from '@dcloudio/uni-app'
import { useAudioStore } from './stores/audio'
import { useConfigStore } from './stores/config'
import { useUserStore } from './stores/user'

onLaunch(() => {
  uni.hideTabBar()
  useConfigStore().initConfig()
  useConfigStore().initSystemInfo()
  useUserStore().getUserInfo()
  // #ifdef APP-PLUS
  useAudioStore().initAudioManager()
  useAudioStore().getAudioList()
  // #endif
})

onUnload(() => {

})
</script>

<style lang="scss">
@import './style/app.scss';
@import './style/color.scss';
@import './style/font.scss';
</style>
