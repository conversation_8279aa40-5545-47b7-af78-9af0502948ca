export enum AddressEnum {
  DEPART_ADDRESS = 'depart',
  ARRIVE_ADDRESS = 'arrive',
}

export enum AddressInfoEnum {
  ADDRESS_INFO = 'addressInfo',
}

export enum SequenceEnum {
  First,
  Second,
  Third,
  Fourth,
  Fifth,
  Sixth,
}

export enum PageInfoEnum {
  PAGE_NO = 'page_no', // 页码
  PAGE_SIZE = 'page_size', // 每页数量
}

export enum TicketTypeEnum {
  NORMAL_TICKET, // 普通票
  PACKAGE_TICKET, // 套票
  DAY_TOURS_TICKET, // 一日游
}
