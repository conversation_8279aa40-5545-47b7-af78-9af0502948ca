export default {
  driver: {
    taskTitle: 'Task list',
    notContent: 'No tasks available at the momentNo tasks available at the moment',
    alreadyOnBoard: 'Already on board',
    boardingTimeout: 'Boarding timeout',
    tips: 'The vehicle is expected to depart in',
    minutes: 'minutes',
    kindReminder: 'Kind Reminder',
    setOffTips: 'May I confirm the departure?',
    arriveTips: 'May I ask if you have confirmed boarding the vehicle?',
    arrivalTips: 'May I confirm arrival?',
    confirm: 'Confirm',
    cancel: 'Cancel',
    successTips: 'Operation successful',
    timeTips: 'The exact departure time is subject to the driver clicking the departure button',
    scanTips: ' Scan the code for ticket verification：',
    viewPassengers: ' View passengers',
    timeTableTitle: ' Timetable',
    Normal: 'Normal',
    Delay: 'Delay',
    onTime: 'On time',
    openItUp: 'Open it up',
    actualArrivalTime: ' Actual arrival time',
    Arrive: 'Arrive',
    actualDepartureTime: 'Actual departure time',
    setOff: 'Set off',
    verifiedTips: ' Information of passengers who have had their tickets verified',
    notVerified: 'No passengers have boarded the vehicle',
    ticketId: 'Enter the ticket ID',
    confirmWriteOff: 'Confirm write-off',
    currentSite: 'Current site',
    people: 'people',
    dueToarrive: 'The number of people to be present',
    actualNumber: 'Actual number of people',
    includedNumber: 'The number of people not included',
  },
}
