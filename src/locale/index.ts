import { createI18n } from 'vue-i18n'

const langModule = import.meta.glob('./modules/**/index.ts', { eager: true, import: 'default' })
const regex = /\.\/modules\/(.*?)\/index\.ts/

/**
 * 定义语言映射类型
 */
export interface LangMappingType {
  [key: string]: string
}

/**
 * 语言映射
 */
export const langMapping: LangMappingType = {
  'en': 'en-us',
  'zh-Hans': 'zh-cn',
  'zh-Hant': 'zh-tw',
}

const messages = Object.keys(langModule).reduce((language: any, path: string) => {
  language[path.match(regex)![1]] = langModule[path]
  return language
}, {})

const currentLocale = uni.getStorageSync('lang') || 'en'
uni.setLocale(currentLocale)
uni.setStorageSync('lang', currentLocale)

const i18n = createI18n({
  locale: currentLocale,
  messages,
})

export default i18n
