import * as <PERSON><PERSON> from 'pinia'
import { createSSRApp } from 'vue'
import App from './App.vue'
import i18n from './locale'
import 'core-js/actual/array/iterator'
import 'core-js/actual/promise'
import 'core-js/actual/object/assign'
import 'core-js/actual/promise/finally'

export function createApp() {
  const app = createSSRApp(App)
  app.use(i18n)
  app.use(Pinia.createPinia())
  return {
    app,
    Pinia,
  }
}
