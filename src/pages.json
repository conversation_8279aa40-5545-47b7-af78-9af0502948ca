{
  "pages": [
    {
      "path": "pages/splash/splash",
      "style": {
        "navigationBarTitleText": "splash",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/page-home/index",
      "style": {
        "navigationBarTitleText": "uni-app x",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/page-guide/index",
      "style": {
        "navigationBarTitleText": "guide",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/page-view/index",
      "style": {
        "navigationBarTitleText": "view",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/page-mine/index",
      "style": {
        "navigationBarTitleText": "mine",
        "navigationStyle": "custom"
      }
    }
  ],
  "globalStyle": {
    "backgroundColor": "#ffffff",
    "backgroundColorBottom": "#ffffff",
    "backgroundColorTop": "#ffffff",
    "backgroundTextStyle": "@bgTxtStyle",
    "navigationBarBackgroundColor": "#000000",
    "navigationBarTextStyle": "@navTxtStyle",
    "navigationBarTitleText": "Uni Creator",
    "navigationStyle": "custom",
    "app-plus": {
      "bounce": "none"
    }
  },
  "tabBar": {
    "color": "#7E8492",
    "selectedColor": "#CF436D",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "white",
    "list": [{
      "pagePath": "pages/page-home/index",
      "text": "",
      "iconPath": "static/tab-icon/home-light1.png",
      "selectedIconPath": "static/tab-icon/home1.png"
    }, {
      "pagePath": "pages/page-guide/index",
      "text": "",
      "iconPath": "static/tab-icon/discovery-light1.png",
      "selectedIconPath": "static/tab-icon/discovery1.png"
    }, {
      "pagePath": "pages/page-view/index",
      "text": "",
      "iconPath": "static/tab-icon/location-light1.png",
      "selectedIconPath": "static/tab-icon/location1.png"
    }, {
      "pagePath": "pages/page-mine/index",
      "text": "",
      "iconPath": "static/tab-icon/profile-light1.png",
      "selectedIconPath": "static/tab-icon/profile1.png"
    }]
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue"
    }
  },
  "subPackages": [
    {
      "root": "subPackages/page-home",
      "pages": [
        {
          "path": "message-list/index",
          "style": {
            "navigationBarTitleText": "message list",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "select-address/index",
          "style": {
            "navigationBarTitleText": "select address list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search-address/index",
          "style": {
            "navigationBarTitleText": "search search list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search-ticket/index",
          "style": {
            "navigationBarTitleText": "search ticket",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "buy-ticket/index",
          "style": {
            "navigationBarTitleText": "buy ticket list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "map/index",
          "style": {
            "navigationBarTitleText": "map list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search-round-trip-ticket/index",
          "style": {
            "navigationBarTitleText": "buy round trip ticket list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "buy-round-trip-ticket/index",
          "style": {
            "navigationBarTitleText": "buy round trip ticket list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "notice/index",
          "style": {
            "navigationBarTitleText": "notice list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search-muti-city-ticket/index",
          "style": {
            "navigationBarTitleText": "buy round trip ticket list",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "buy-muti-city-ticket/index",
          "style": {
            "navigationBarTitleText": "buy muti city address",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "package/index",
          "style": {
            "navigationBarTitleText": "package",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "order-fill-out/index",
          "style": {
            "navigationBarTitleText": "order fill out",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "day-tours/index",
          "style": {
            "navigationBarTitleText": "day tours",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "day-tours/detail",
          "style": {
            "navigationBarTitleText": "day tours detail",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        },
        {
          "path": "transfer/index",
          "style": {
            "navigationBarTitleText": "transfer",
            // "enablePullDownRefresh": true
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "subPackages/page-guide",
      "pages": [
        {
          "path": "purchase-all/purchase-all",
          "style": {
            "navigationBarTitleText": "Purchanse all",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search/search",
          "style": {
            "navigationBarTitleText": "search",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "guide-detail/guide-detail",
          "style": {
            "navigationBarTitleText": "guide-detail",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "subPackages/page-mine",
      "pages": [
        {
          "path": "my-guide/my-guide",
          "style": {
            "navigationBarTitleText": "E-Guide",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-itinerary/my-itinerary",
          "style": {
            "navigationBarTitleText": "My itinerary",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-points/my-points",
          "style": {
            "navigationBarTitleText": "My points",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "redeem-coupons/redeem-coupons",
          "style": {
            "navigationBarTitleText": "Redeem coupons",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "real-information/real-information",
          "style": {
            "navigationBarTitleText": "Real information",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-collection/my-collection",
          "style": {
            "navigationBarTitleText": "My collection",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-tickets/my-tickets",
          "style": {
            "navigationBarTitleText": "My tickets",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "invite-friends/invite-friends",
          "style": {
            "navigationBarTitleText": "Invite friends",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "coupon-package/coupon-package",
          "style": {
            "navigationBarTitleText": "Coupon Package",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earn-points/earn-points",
          "style": {
            "navigationBarTitleText": "Earn points",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "points-detail/points-detail",
          "style": {
            "navigationBarTitleText": "Points details",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "setting/setting",
          "style": {
            "navigationBarTitleText": "Setting",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "account-security/account-security",
          "style": {
            "navigationBarTitleText": "Account security",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-inviter/my-inviter",
          "style": {
            "navigationBarTitleText": "My inviter",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "feedback/feedback",
          "style": {
            "navigationBarTitleText": "Feedback",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "cancel-account/cancel-account",
          "style": {
            "navigationBarTitleText": "Cancel account",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "change-info/change-info",
          "style": {
            "navigationBarTitleText": "Chang Info",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my-order/my-order",
          "style": {
            "navigationBarTitleText": "My order",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "order-detail/order-detail",
          "style": {
            "navigationBarTitleText": "Order Detail",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "edit-info/edit-info",
          "style": {
            "navigationBarTitleText": "Edit materials",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "login/login",
          "style": {
            "navigationBarTitleText": "Edit materials",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "subPackages/page-driver",
      "pages": [
        {
          "path": "task-list/task-list",
          "style": {
            "navigationBarTitleText": "Task list",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "task-detail/task-detail",
          "style": {
            "navigationBarTitleText": "Task detail",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "scan-code/scan-code",
          "style": {
            "navigationBarTitleText": "Scan code",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ]
}
