<script setup lang="ts">
const show = defineModel<boolean>('modelValue', { default: false })
</script>

<template>
  <base-popup v-model="show" title="Price Detail" height="460rpx">
    <view class="wrapper">
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          E-guide price:
        </view>
        <view class="wrapper__item__value">
          20 NZD
        </view>
      </view>
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          Point deduction:
        </view>
        <view class="wrapper__item__value">
          -1 NZD
        </view>
      </view>
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          Total:
        </view>
        <view class="wrapper__item__value red">
          20 NZD
        </view>
      </view>
    </view>
  </base-popup>
</template>

<style scoped lang="scss">
.wrapper{
  padding-top: 80rpx;

  &__item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 48rpx;

    &__label{
      width: 350rpx;
      color: #161616;
      font-size: 28rpx;
      font-weight: 500;
    }

    &__value{
      flex: 1;
      text-align: left;
      color: #717680;
      font-size: 28rpx;
      font-weight: 400;

      &.red{
        color: #912018;
      }
    }
  }
}
</style>
