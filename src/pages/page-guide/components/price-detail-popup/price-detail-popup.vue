<script setup lang="ts">
import type { PriceInfoType } from '@/model/guide'
import { useI18n } from 'vue-i18n'

withDefaults(defineProps<{
  info?: PriceInfoType
}>(), {
  info: () => {
    return {} as PriceInfoType
  },
})

const { t } = useI18n()

const show = defineModel<boolean>('modelValue', { default: false })
</script>

<template>
  <base-popup v-model="show" title="Price Detail" height="460rpx">
    <view class="wrapper">
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          {{ t('guide.guidePrice') }}:
        </view>
        <view class="wrapper__item__value">
          {{ info.money }} {{ t('guide.unit') }}
        </view>
      </view>
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          {{ t('guide.pointDeduction') }}:
        </view>
        <view class="wrapper__item__value">
          -0 {{ t('guide.unit') }}
        </view>
      </view>
      <view class="wrapper__item">
        <view class="wrapper__item__label">
          {{ t('guide.total') }}:
        </view>
        <view class="wrapper__item__value red">
          {{ info.order_money }} {{ t('guide.unit') }}
        </view>
      </view>
    </view>
  </base-popup>
</template>

<style scoped lang="scss">
.wrapper{
  padding-top: 80rpx;

  &__item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 48rpx;

    &__label{
      width: 350rpx;
      color: #161616;
      font-size: 28rpx;
      font-weight: 500;
    }

    &__value{
      flex: 1;
      text-align: left;
      color: #717680;
      font-size: 28rpx;
      font-weight: 400;

      &.red{
        color: #912018;
      }
    }
  }
}
</style>
