<script setup lang="ts">
import type { GuideListItemType } from '@/model/guide'
import { onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiDictList } from '@/api/common'
import { ApiGuideList } from '@/api/guide'
import { useList } from '@/hooks/useList'
import guideItem from '../guide-item/guide-item.vue'
import selectPopup from '../select-popup/select-popup.vue'

const { t } = useI18n()
const query = ref({
  is_recommend: 0,
  guide_area_id: '',
  attractions_type: '',
  price_sorting: 'desc',
  purchase_frequency: 'desc',
})
const attrList = ref<any[]>([])
const areaList = ref<any[]>([])

const { list, refresh, loadMore, setParams } = useList<GuideListItemType>(ApiGuideList, query.value)

watch(() => query.value, (newVal: any) => {
  setParams(newVal)
}, {
  deep: true,
})

function setOrder(type: string) {
  if (type === 'price_sorting') {
    query.value.price_sorting = query.value.price_sorting === 'desc' ? 'asc' : 'desc'
  }
  else {
    query.value.purchase_frequency = query.value.purchase_frequency === 'desc' ? 'asc' : 'desc'
  }
}

async function getDictList(type: string) {
  if (type === 'guide_area') {
    const res = await ApiDictList({ type: 'guide_area' })
    areaList.value = res?.guide_area || []
  }
  else {
    const res = await ApiDictList({ type: 'attractions_type' })
    attrList.value = res?.attractions_type || []
  }
}

onMounted(async () => {
  getDictList('guide_area')
  getDictList('attractions_type')
  await refresh()
})

defineExpose({
  loadMore,
})
</script>

<template>
  <view class="list">
    <view class="list__title">
      {{ t('guide.most_popular') }}
    </view>
    <view class="list__filter">
      <select-popup v-model="query.guide_area_id" :list="areaList" :title="t('guide.route')" />
      <select-popup v-model="query.attractions_type" :list="attrList" :title="t('guide.category')" />
      <view class="list__filter__items" @click="setOrder('price_sorting')">
        <view class="fs--14--400">
          {{ t('guide.price') }}
        </view>
        <zui-svg-icon
          width="36rpx"
          height="36rpx"
          color="#000000"
          :icon="query.price_sorting === 'desc' ? 'arrow-down2-light' : 'arrow-up2-light'"
        />
      </view>
      <view class="list__filter__items" @click="setOrder('purchase_frequency')">
        <view class="fs--14--400">
          {{ t('guide.purchases') }}
        </view>
        <zui-svg-icon
          width="36rpx"
          height="36rpx"
          color="#000000"
          :icon="query.purchase_frequency === 'desc' ? 'arrow-down2-light' : 'arrow-up2-light'"
        />
      </view>
    </view>
    <view>
      <guide-item v-for="(item) in list" :key="item.id" :item="item" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.list{
    padding-bottom: 32rpx;

    &__filter{
      padding: 24rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &__items{
        display: flex;
        align-items: center;
      }
    }

    &__title{
      color: #000;
      font-size: 30rpx;
      font-weight: 600;
    }

    .custom-popup-class{
      width: 100rpx !important;
    }
}
</style>
