<script lang="ts" setup>
import type { GuideListItemType } from '@/model/guide'
import { formatAudioTime } from '@/utils/audio'
import { gotoPage } from '@/utils/router'

const props = withDefaults(defineProps<{
  item?: GuideListItemType
}>(), {
  item: () => {
    return {} as GuideListItemType
  },
})
function toDetail() {
  gotoPage(`/subPackages/page-guide/guide-detail/guide-detail?id=${props.item.id}`)
}
</script>

<template>
  <view class="guide_item" @tap="toDetail()">
    <image class="guide_item_img" :src="item.image" />
    <view class="guide_item_content">
      <view class="guide_item_content_left">
        <view class="guide_item_content_left_title">
          <text class="fs--14--700 guide_item_content_left_title_text">
            {{ item.name }}
          </text>
          <text v-if="item.is_buy === 2" class="guide_item_content_left_title_tag fs--10--400">
            {{ $t('guide.expired') }}
          </text>
          <text v-if="item.is_buy === 1" class="guide_item_content_left_title_tag blue fs--10--400">
            {{ $t('guide.gift') }}
          </text>
          <!-- <text v-if="item.is_buy === 2" class="guide_item_content_left_title_tag red fs--10--400">Expires in 10d 5h 23m</text> -->
        </view>
        <view class="guide_item_content_left_info">
          <view class="guide_item_content_left_info_item">
            <zui-svg-icon width="30rpx" height="30rpx" color="#717680" icon="time-circle-light" />
            <view class="guide_item_content_left_info_item_text fs--12--400">
              {{ item.audiolist.length > 0 ? formatAudioTime(parseInt(item.audiolist[0].duration)) : '00:00' }}
            </view>
          </view>
          <view class="guide_item_content_left_info_item" style="margin-left: 20rpx;">
            <zui-svg-icon width="30rpx" height="30rpx" color="#717680" icon="bag-light" />
            <view class="guide_item_content_left_info_item_text fs--12--400">
              {{ item.sales }} {{ $t('guide.times') }}
            </view>
          </view>
        </view>
      </view>

      <view class="guide_item_content_right">
        <text class="guide_item_content_right_price">
          {{ item.way === 0 ? $t('guide.free') : `${item.price}${$t('guide.unit')}` }}
        </text>
        <view class="guide_item_content_right_icon">
          <zui-svg-icon width="42rpx" height="42rpx" color="#103A62" icon="play-light" />
        </view>
      </view>
    </view>

    <base-discount />
  </view>
</template>

<style lang="scss" scoped>
@import './guide-item.scss'
</style>
