<script setup lang="ts">
import type { GuideListItemType } from '@/model/guide'
import { onMounted, ref } from 'vue'
import { ApiGuideList } from '@/api/guide'
import guideItem from '../guide-item/guide-item.vue'

const list = ref<GuideListItemType[]>([])

async function getList() {
  const res = await ApiGuideList({ is_recommend: 1, page_no: 1, page_size: 20 })
  list.value = res.lists
}

onMounted(() => {
  getList()
})
</script>

<template>
  <view class="list">
    <view class="list__title">
      {{ $t('guide.recommended') }}
    </view>
    <view>
      <guide-item v-for="(item, index) in list" :key="index" :item="item" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.list{
  margin-bottom: 44rpx;
  &__title{
    color: #000;
    font-size: 30rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
  }
}
</style>
