<script setup lang="ts">
import type { DistItemType } from '@/model/common'
import { ref } from 'vue'

withDefaults(defineProps<{
  title?: string
  list?: DistItemType[]
}>(), {
  title: '',
  list: () => [],
})

const result = defineModel<string>('modelValue', { default: '' })
const temp = ref<string>('')

const show = ref<boolean>(false)

function open() {
  show.value = true
}

function selectItem(value: string) {
  if (temp.value && temp.value === value) {
    temp.value = ''
  }
  else {
    temp.value = value
  }
}

function submit() {
  result.value = temp.value
  show.value = false
}
</script>

<template>
  <view class="items" @click="open">
    <view class="items__title fs--14--400">
      {{ title }}
    </view>
    <zui-svg-icon width="36rpx" height="36rpx" color="#000000" icon="arrow-down2-light" />
  </view>
  <wd-popup v-model="show" :z-index="1009" position="bottom" :safe-area-inset-bottom="true" custom-style="border-radius: 40rpx 40rpx 0 0;">
    <view class="select-popup" @touchmove.stop.prevent>
      <view class="select-popup__title">
        <text v-if="title" class="select-popup__title__text fs--16--700">
          {{ title }}
        </text>
        <view class="select-popup__title__close">
          <zui-svg-icon width="36rpx" height="36rpx" color="#000" icon="cancel-light" @tap="show = false" />
        </view>
      </view>
      <scroll-view scroll-y :show-scrollbar="false" class="select-popup__content">
        <view
          v-for="(item) in list"
          :key="item.id"
          class="select-popup__content__item"
          :class="{ active: temp === item.value }"
          @click="selectItem(item.value)"
        >
          {{ item.name }}
        </view>
      </scroll-view>
      <view class="select-popup__footer">
        <view class="select-popup__footer__btn" @tap.stop="submit">
          Confirm
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
@import './select-popup.scss'
</style>
