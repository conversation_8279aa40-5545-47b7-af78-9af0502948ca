.select-popup{
  padding: 32rpx;

  &__title {
      height: 60rpx;
      position: relative;
      text-align: center;

      &__text {
          color: #111B19;
          line-height: 60rpx;
      }

      &__close {
          position: absolute;
          top: 0;
          width: 50rpx;
          height: 50rpx;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: row;
      }

  }

  &__content{
    height: 600rpx;
    &__item{
      font-size: 28rpx;
      font-weight: 500;
      color: rgba(22, 22, 22, 0.30);
      text-align: center;
      padding: 32rpx 0;

      &.active{
        color: #161616;
      }
    }
  }

  &__footer{
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 100%;
    height: 100%;

    &__btn{
      height: 70rpx;
      border-radius: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      color: #fff;
      text-align: center;
      background-color: #103A62;
      width: 100%;
    }
  }
}
.items{
    display: flex;
    align-items: center;
    &__title{
        margin-right: 16rpx;
    }
}