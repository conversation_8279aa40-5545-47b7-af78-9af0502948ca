<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiBannerList } from '@/api/common'
import { useAudioStore } from '@/stores/audio'
import { gotoPage } from '@/utils/router'
import guideList from './components/guide-list/guide-list.vue'
import recommendedList from './components/recommended-list/recommended-list.vue'

const { t } = useI18n()
const kw = ref<string>('')

const swiperList = ref<BaseSwiperItemType[]>([])

async function getSwiperList() {
  const res = await ApiBannerList({ show_type: 2 })
  swiperList.value = res.lists
}

const guideListRef = ref<any>(null)

onReachBottom(() => {
  guideListRef.value?.loadMore()
})

onLoad(() => {
  getSwiperList()
})
</script>

<template>
  <page>
    <nav-bar :num="10" />
    <view class="body">
      <view class="wrapper">
        <!-- 搜索框 -->
        <view @tap="gotoPage('/subPackages/page-guide/search/search')">
          <search-input
            v-model="kw"
            disabled
            :placeholder="t('guide.search')"
            :text="t('guide.purchaseAll')"
            text-class="blue"
            @action-click="gotoPage('/subPackages/page-guide/purchase-all/purchase-all')"
          />
        </view>
        <!-- 轮播图 -->
        <view class="wrapper__swiper">
          <base-swiper :list="swiperList" />
        </view>
        <!-- 推荐列表 -->
        <recommended-list />
        <!-- 电子导游列表 -->
        <guide-list ref="guideListRef" />
      </view>
    </view>
    <!-- 播放器  -->
    <audio-bar v-if="useAudioStore().status.isTabbar && useAudioStore().currentAudio" />
    <tab-bar :current="1" />
  </page>
</template>

<style lang="scss" scoped>
.wrapper{
  padding: 0 32rpx;

  &__swiper{
    margin: 32rpx 0 44rpx 0;
  }
}
</style>
