<script lang="ts" setup>
const emits = defineEmits(['close'])

const show = defineModel<boolean>('modelValue', { default: false })

function close() {
  show.value = false
  emits('close')
}
</script>

<template>
  <wd-popup v-model="show" custom-style="border-radius:44rpx;" @close="close">
    <view class="tips">
      <view class="tips__close">
        <zui-svg-icon width="28rpx" height="28rpx" color="#161616" icon="cancel-light" @tap="close" />
      </view>
      <view class="tips__header">
        Notification
      </view>
      <view class="tips__content">
        Dear user, the time zone displayed on the current App is New Zealand time, please pay attention when booking tickets.
      </view>
      <view class="tips__btn" @click="close">
        Got it
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.tips{
    width: 600rpx;
    background-color: #fff;
    padding: 60rpx 32rpx;
    position: relative;

    &__close{
        position: absolute;
        top: 24rpx;
        right: 32rpx;
    }

    &__header{
        text-align: center;
        color: #1D2129;
        font-size: 34rpx;
        font-weight: 500;
        line-height: 48rpx;
    }

    &__content{
        margin: 32rpx 0;
        color: #4E5969;
        font-size: 24rpx;
        font-weight: 400;
        line-height: 40rpx;
    }

    &__btn{
        background-color: #103A62;
        padding: 20rpx;
        border-radius: 16rpx;
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
        text-align: center;
    }
}
</style>
