<script setup lang="ts">
import type { StartPageType } from '@/model/splash'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { ApiStartPage } from '@/api/splash/index'
import { useNavBar } from '@/hooks/useNavBar'
import tipsPopup from './components/tips-popup.vue'

const current = ref<number>(0)
const list = ref<StartPageType[]>([])

const { statusBarHeight } = useNavBar()

const show = ref<boolean>(true)
const autoplay = ref<boolean>(false)
const interval = ref<number>(2000) // 切换间隔
const timmer = ref<any>(null)

async function getList() {
  const res = await ApiStartPage({
    show_type: 2,
  })
  list.value = res.lists || []
}

function handleChange(e: any) {
  current.value = e.detail.current
}

function toHome() {
  if (timmer.value) {
    clearTimeout(timmer.value)
  }
  uni.switchTab({
    url: '/pages/page-home/index',
  })
}

function startPlay() {
  autoplay.value = true
  timmer.value = setTimeout(() => {
    toHome()
  }, interval.value * list.value.length)
}

onLoad(() => {
  getList()
})
</script>

<template>
  <view class="wrapper">
    <swiper
      class="swiper__container"
      :current="current"
      :interval="interval"
      :autoplay="autoplay"
      @change="handleChange"
    >
      <swiper-item v-for="(item, index) in list" :key="index" class="swiper__container__item">
        <image :src="item.image" mode="aspectFill" class="swiper__container__item__image" />
      </swiper-item>
    </swiper>
    <view class="wrapper__top">
      <view :style="{ height: `${statusBarHeight}px` }" />
      <view class="wrapper__top__list" :style="{ gridTemplateColumns: `repeat(${list.length}, 1fr)` }">
        <view
          v-for="(item, index) in list"
          :key="index"
          class="wrapper__top__list__item"
          :class="{ active: current === index }"
        />
      </view>
    </view>
    <view class="wrapper__bottom">
      <view class="wrapper__bottom__title">
        Start Now! Attract Goals, Achieve Success!
      </view>
      <view class="wrapper__bottom__desc">
        Learn how to align your thoughts and actions with your goals for a life of fulfillment and achievement.
      </view>
      <view class="wrapper__bottom__btn" @click="toHome">
        Get Started
      </view>
    </view>

    <tips-popup v-model="show" @close="startPlay" />
  </view>
</template>

<style scoped lang="scss">
@import './splash.scss'
</style>
