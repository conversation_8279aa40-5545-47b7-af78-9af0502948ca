page{
    height: 100vh;
}
.wrapper{
    width: 750rpx;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);  /* 兼容旧 iOS */
    padding-bottom: env(safe-area-inset-bottom);       /* iOS 11+ */
    // height: calc(100vh + env(safe-area-inset-bottom)); /* 把被减掉的安全区补回来 */
    // height: calc(100vh + constant(safe-area-inset-bottom)); /* 兼容旧版 iOS */
    // background-color: red;

    .swiper__container{
        width: 100%;
        height: calc(100vh + env(safe-area-inset-bottom));
        height: calc(100vh + constant(safe-area-inset-bottom));
        position: fixed;
        left: 0;
        right: 0;
        &__item{
            &__image{
                width: 100%;
                height: 100%;
            }
        }

    }

    &__top{
        width: 660rpx;
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);

        &__list{
            display: grid;
            gap: 10rpx;
            padding-top: 100rpx;

            &__item{
                background-color: #103A62;
                height: 8rpx;
                border-radius: 100rpx;

                &.active{
                     background-color: #EDF1F6;
                }
            }
        }

    }

    &__bottom{
        width: 660rpx;
        padding-bottom: 160rpx;
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;

        &__title{
            font-size: 60rpx;
            line-height: 72rpx;
            font-weight: 500;
            color: #fff;
            text-align: center;
        }

        &__desc{
            color: #fff;
            text-align: center;
            font-size: 32rpx;
            font-weight: 400;
            line-height: 48rpx;
            letter-spacing: 0.15px;
        }

        &__btn{
            background-color: #103A62;
            color: #fff;
            font-size: 28rpx;
            font-weight: 500;
            line-height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 24rpx 24rpx;
            border-radius: 24rpx;
            margin-top: 32rpx;
        }
    }
}