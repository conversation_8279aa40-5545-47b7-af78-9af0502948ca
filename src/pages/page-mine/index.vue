<script setup lang="ts">
import type { Component } from 'vue'
import { ref, shallowRef } from 'vue'
import { useUserStore } from '@/stores/user'
import clientSide from './components/client-side/client-side.vue'
import driverApp from './components/driver-app/driver-app.vue'
import notLogin from './components/not-login/not-login.vue'
import userInfo from './components/user-info/user-info.vue'

const userStore = useUserStore()
const current = ref('clientSide')

const pageMap = shallowRef<Record<string, Component>>({
  clientSide,
  driverApp,
})
function handleSwitchRoles(role: string) {
  current.value = role
}
</script>

<template>
  <page>
    <nav-bar :num="10" />
    <view class="body">
      <user-info v-if="userStore.token" />
      <not-login v-else />
      <view class="line" />
      <component :is="pageMap[current]" :key="current" @switch-roles="handleSwitchRoles" />
    </view>
    <tab-bar :current="3" />
  </page>
</template>

<style lang="scss" scoped>
.line {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx auto;
  background-color: #F5F6F6;
}
</style>
