.info {
	width: 686rpx;
	height: 128rpx;
	margin: 24rpx 32rpx 0 32rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;

	&__left {
		height: 128rpx;
		display: flex;
		flex-direction: row;
		align-items: center;

		&__image {
			width: 128rpx;
			height: 128rpx;
			border-radius: 64rpx;
			overflow: hidden;

			&__avatar {
				width: 128rpx;
				height: 128rpx;
			}
		}

		&__name {
			padding-bottom: 20rpx;
			height: 128rpx;
			margin-left: 30rpx;

			&__username {
				height: 60rpx;
				display: flex;
				flex-direction: row;
				align-items: center;

				&__text {
					color: #4B5353;
				}
			}

			&__label {
				width: 206rpx;
				height: 44rpx;
				border-radius: 100rpx;
				padding: 8rpx 24rpx;
				background-color: #103A62;
				display: flex;
				align-items: center;
				justify-content: center;

				&__text {
					color: #FFFFFF;
				}
			}
		}
	}

	&__right {
		width: 158rpx;
		height: 80rpx;
		border-radius: 24rpx;
		border: 3rpx solid #F5F6F6;
		padding: 16rpx 24rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;

		&__text {
			color: #4B5353;
		}
	}
}