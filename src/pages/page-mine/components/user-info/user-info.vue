<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { gotoPage } from '@/utils/router'

const userStore = useUserStore()
</script>

<template>
  <view class="info">
    <view class="info__left">
      <view class="info__left__image">
        <image class="info__left__image__avatar" :src="userStore.userInfo?.avatar" mode="aspectFit" />
      </view>
      <view class="info__left__name" @click="gotoPage('/subPackages/page-mine/edit-info/edit-info')">
        <view class="info__left__name__username">
          <text class="info__left__name__username__text fs--18--400">
            {{ userStore.userInfo?.nickname }}
          </text>
        </view>
        <view class="info__left__name__label">
          <text class="info__left__name__label__text fs--12--400">
            {{ userStore.userInfo?.account_type }}
          </text>
        </view>
      </view>
    </view>
    <view class="info__right" @click.stop="gotoPage('/subPackages/page-mine/edit-info/edit-info')">
      <zui-svg-icon width="40rpx" height="40rpx" icon="edit-light" />
      <text class="info__righ__text fs--14--400">
        {{ $t('mine.edit') }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "./user-info.scss"
</style>
