<script lang="ts" setup>
import type { ListItemType } from '@/model/driver'

withDefaults(defineProps<{
  item?: ListItemType
}>(), {
  item: () => {
    return {} as ListItemType
  },
})
</script>

<template>
  <view class="list-item-container">
    <view class="list-item-container__left">
      <!-- <image class="list-item-container__left__icon" src="/static/driver/category.png" mode="aspectFill"></image> -->
      <image class="list-item-container__left__icon" :src="item.icon" mode="aspectFill" />
      <text class="list-item-container__left__label">
        {{ item.label }}
      </text>
    </view>
    <image class="list-item-container__right-arrow" src="/static/driver/right-arrow.png" mode="aspectFill" />
  </view>
</template>

<style lang="scss" scoped>
@import './driver-item.scss'
</style>
