<script lang="ts" setup>
import type { ListItemType } from '@/model/driver'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import driverItem from '../driver-item/driver-item.vue'
import logout from '../logout/logout.vue'

const emit = defineEmits<{
  (e: 'switchRoles', role: string): void
}>()
const { t } = useI18n()
const list = ref<ListItemType[]>([
  {
    icon: '/static/driver/category.png',
    label: t('mine.taskList'),
  },
  {
    icon: '/static/driver/swap.png',
    label: t('mine.switchRoles'),
  },
])

const showLogout = ref(false)

function handleClick(idx: number) {
  if (idx === 1) {
    emit('switchRoles', 'clientSide')
    return
  }
  uni.navigateTo({
    url: '/subPackages/page-driver/task-list/task-list',
  })
}

function handleLogout() {
  showLogout.value = true
}
</script>

<template>
  <view class="driver-list-container">
    <driver-item
      v-for="(item, idx) in list"
      :key="item.label"
      :item="item"
      @click="handleClick(idx)"
    />
    <view class="driver-list-container__logout" @click.stop="handleLogout">
      <view class="driver-list-container__logout__left">
        <image class="driver-list-container__logout__left__icon" src="/static/driver/logout.png" mode="aspectFill" />
        <text class="driver-list-container__logout__left__label">
          {{ $t('mine.logout') }}
        </text>
      </view>
      <image class="driver-list-container__logout__right-arrow" src="/static/driver/right-arrow.png" mode="aspectFill" />
    </view>
    <view class="driver-list-container__slogon">
      <text class="driver-list-container__slogon__text">
        -- {{ $t('mine.footer') }} --
      </text>
    </view>
    <logout v-model:visible="showLogout" />
  </view>
</template>

<style lang="scss" scoped>
@import './driver-app.scss'
</style>
