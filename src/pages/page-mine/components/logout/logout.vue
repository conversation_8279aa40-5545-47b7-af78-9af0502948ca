<script lang="ts" setup>
import { useUserStore } from '@/stores/user'

const visible = defineModel('visible', {
  default: false,
})

function handleClose() {
  visible.value = false
}

function handleLogout() {
  useUserStore().logout()
}
</script>

<template>
  <wd-popup v-model="visible" position="bottom" :z-index="9999" :safe-area-inset-bottom="true" custom-style="border-radius: 40rpx 40rpx 0 0;">
    <view class="popup-detail-container__content">
      <view class="popup-detail-container__content__header">
        <text class="popup-detail-container__content__header__title">
          {{ $t('mine.logout') }}
        </text>
      </view>
      <view class="popup-detail-container__content__body">
        <view class="popup-detail-container__content__body__content">
          <text class="popup-detail-container__content__body__content__text">
            {{ $t('mine.logoutTips') }}
          </text>
        </view>
      </view>
      <view class="popup-detail-container__content__footer">
        <view class="popup-detail-container__content__footer__logout" @click.stop="handleLogout">
          <text class="popup-detail-container__content__footer__logout__text">
            {{ $t('mine.logout') }}
          </text>
        </view>
        <view class="popup-detail-container__content__footer__cancel" @click.stop="handleClose">
          <text class="popup-detail-container__content__footer__cancel__text">
            {{ $t('mine.cancel') }}
          </text>
        </view>
      </view>
      <view class="popup-detail-container__content__close" @click.stop="handleClose">
        <zui-svg-icon width="40rpx" height="40rpx" icon="cancel-light" />
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@import './logout.scss'
</style>
