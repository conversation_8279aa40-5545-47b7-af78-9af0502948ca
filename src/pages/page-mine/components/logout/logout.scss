.popup-detail-container__content{
    position: relative;
    padding: 40rpx 24rpx  56rpx;
    &__header{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 52rpx;
        margin-bottom: 32rpx;
        &__title{
            font-family: "Alimama FangYuanTi VF";
            font-weight: 700;
            font-size: 32rpx;
            color: #111b19;
        }
    }
    &__body{
        width: 702rpx;
        padding: 32rpx 32rpx 0;
        &__content{
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 32rpx;
            &__text{
                font-family: Alimama FangYuanTi VF;
                font-weight: 700;
                font-size: 32rpx;
                line-height: 18px;
                color: #91A8BE;
            }
        }
    }
    &__footer{
        display:flex;
        flex-direction: row;
        justify-content: space-between;
        &__logout,&__cancel{
            display:flex;
            justify-content: center;
            align-items: center;
            width: 332rpx;
            height: 56rpx;
            border: 1px solid #103A62;
            border-radius: 24rpx;
        }
        &__logout{
            background: #EDF1F6;
            &__text{
                color: #103A62;
            }
        }
        &__cancel{
            background: #103A62;
            &__text{
                color:  #fff;
            }
        }
    }
    &__close{
        position: absolute;
        top: 46rpx;
        right: 46rpx;
    }
}
.total {
    font-weight: 700;
    color: #912018;
}