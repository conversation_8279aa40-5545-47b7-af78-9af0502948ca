<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiUserCenter } from '@/api/user'
import { useTabbar } from '@/hooks/useTabbar'
import { useConfigStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'
import { gotoPage } from '@/utils/router'
import concactService from '../concact-service/concact-service.vue'
import logout from '../logout/logout.vue'
import orderList from '../order-list/order-list.vue'

const emit = defineEmits<{
  (e: 'switchRoles', role: string): void
}>()
const { t } = useI18n()
const userStore = useUserStore()
const showLogout = ref<boolean>(false)
const showService = ref<boolean>(false)

const { terminal } = useConfigStore()

const orderItem = ref<any[]>([
  {
    image: 'category-light',
    value: 0,
    label: t('mine.order'),
    url: '/subPackages/page-mine/my-order/my-order',
  },
  {
    image: 'discovery-light',
    value: 0,
    label: t('mine.eGuide'),
    url: '/subPackages/page-mine/my-guide/my-guide',
  },
  {
    image: 'activity-light',
    value: 0,
    label: t('mine.message'),
    url: '/subPackages/page-home/message-list/index',
  },
])

const { placeholderHeight } = useTabbar(120)

// 跳转数据
const linkItem = computed(() => {
  const baseList = [
    {
      image: 'work-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.myItinerary'),
      path: '/subPackages/page-mine/my-itinerary/my-itinerary',
    },
    {
      image: 'discount-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.myPoints'),
      path: '/subPackages/page-mine/my-points/my-points',
    },
    {
      image: 'heart-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.myCollection'),
      path: '/subPackages/page-mine/my-collection/my-collection',
    },
    {
      image: 'ticket-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.myTickets'),
      path: '/subPackages/page-mine/my-tickets/my-tickets',
    },
    {
      image: 'send-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.myShare'),
      path: '/subPackages/page-mine/invite-friends/invite-friends',
    },
    {
      image: 'more-circle-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.contact'),
      path: 'concact-service',
    },
    {
      image: 'setting-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.setting'),
      path: '/subPackages/page-mine/setting/setting',
    },
    {
      image: 'swap-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.switchRoles'),
      path: 'switchRoles',
    },
    {
      image: 'logout-light',
      image_arrow: 'arrow-right2-light',
      title: t('mine.logout'),
      path: 'logout',
    },
  ]
  return baseList
  // const shouldHideSwitch
  //   = terminal === 3 || userStore.userInfo?.is_driver !== 1

  // return shouldHideSwitch
  //   ? baseList.filter(item => item.path !== 'switchRoles')
  //   : baseList
})
// 跳转我的订单页
function clickUrl(val: string | null) {
  if (!userStore.token) {
    gotoPage('/subPackages/page-mine/login/login')
    return
  }
  if (val != null) {
    gotoPage(val)
  }
}

// 跳转详情
function toDetail(path: any | null) {
  if (path === 'switchRoles') {
    emit('switchRoles', 'driverApp')
    return
  }
  if (path === 'logout') {
    showLogout.value = true
  }
  if (path === 'concact-service') {
    showService.value = true
  }
  if (path !== null && typeof path === 'string') {
    uni.navigateTo({
      url: path as string,
    })
  }
}

async function getUserCenter() {
  if (!userStore.token)
    return
  try {
    const res = await ApiUserCenter()
    orderItem.value[0].value = res.user_order_count || 0
    orderItem.value[1].value = res.guide_order_count || 0
    orderItem.value[2].value = res.noticer_ecord_count || 0
  }
  catch (error) {
    console.error(error)
  }
}
getUserCenter()
</script>

<template>
  <view>
    <order-list :order-item="orderItem" @click-url="clickUrl" />
    <view class="line" />
    <view v-if="userStore.token" class="link">
      <view
        v-for="(item, index) in linkItem" :key="index" class="link__item"
        :style="{ marginBottom: index === 3 || index === 5 || index === 7 ? '32rpx' : '0rpx' }" @tap="toDetail(item.path)"
      >
        <view class="link__item__left">
          <zui-svg-icon width="40rpx" height="40rpx" :icon="item.image" />
          <text class="link__item__left__text fs--12--400">
            {{ item.title }}
          </text>
        </view>
        <view class="link__item__right">
          <zui-svg-icon width="38rpx" height="38rpx" :icon="item.image_arrow" />
        </view>
      </view>
    </view>
    <view class="footer">
      <text class="footer__text fs--12--400">
        —— {{ $t('mine.footer') }} ——
      </text>
    </view>
    <view v-if="!userStore.token" class="login__wrapper" :style="{ bottom: `${placeholderHeight}px` }">
      <view class="login__wrapper__btn" @click="gotoPage('/subPackages/page-mine/login/login')">
        <text>Login</text>
      </view>
    </view>
    <!-- 退出登录 -->
    <logout v-model:visible="showLogout" />
    <!-- 联系客服 -->
    <concact-service v-model="showService" />
  </view>
</template>

<style lang="scss" scoped>
@import "./client-side.scss"
</style>
