	.order {
		width: 686rpx;
		height: 120rpx;
		margin: 0 auto;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		margin-bottom: 30rpx;

		&__item {
			display: flex;
			align-items: center;
			justify-content: center;
			&__image {
				width: 64rpx;
				height: 64rpx;
				border-radius: 32rpx;
				border: 2rpx solid #F5F6F6;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			&__view {
				margin-left: 16rpx;

				&__value {
					color: #4B5353;
				}

				&__label {
					color: #1F1F1F;
				}
			}
		}
	}