<script lang="ts" setup>
import { useUserStore } from '@/stores/user'

defineProps<{
  orderItem?: any[]
}>()
const emit = defineEmits(['clickUrl'])
const userStore = useUserStore()
function onClick(url: string | null) {
  emit('clickUrl', url)
}
</script>

<template>
  <view class="order">
    <view
      v-for="(item, index) in orderItem" :key="index" class="order__item"
      :style="{ 'border-right': index === 2 ? 'none' : '2rpx solid #F5F6F6' }"
      @tap="onClick(item.url as string | null)"
    >
      <view class="order__item__image">
        <zui-svg-icon width="40rpx" height="40rpx" :icon="item.image" />
      </view>
      <view class="order__item__view">
        <view v-if="userStore.token" class="order__item__view__value fs--16--700">
          {{ item.value }}
        </view>
        <view v-else class="order__item__view__value fs--12--700" style="line-height: 48rpx;">
          —
        </view>
        <view class="order__item__view__label fs--12--400">
          {{ item.label }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './order-list.scss'
</style>
