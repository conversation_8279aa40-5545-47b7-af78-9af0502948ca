.service{
    padding: 32rpx 0;

    &__item{
        display: flex;
        margin-bottom: 32rpx;

        &__title{
            margin-left: 32rpx;
            flex: 1;

            &__text{
                color: #103A62;
                font-size: 24rpx;
                font-weight: 600;
                line-height: 40rpx;
            }

            &__desc{
                color: #A4A7AE;
                font-size: 24rpx;
                font-weight: 600;
                line-height: 40rpx;
            }
        }
    }


    &__footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;

        &__btn {
            height: 60rpx;
            width: 48%;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &.left {
                border: 1px solid #103A62;
                .service__footer__btn__text {
                    color: #103A62;
                }
            }

            &.right {
                border: 1px solid transparent;

                .service__footer__btn__text {
                    color: #fff;
                }

            }

            &.blue {
                background-color: #103A62;
            }
        }

    }
}