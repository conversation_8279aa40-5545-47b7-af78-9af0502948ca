<script setup lang="ts">
const show = defineModel<boolean>('modelValue', { default: false })
</script>

<template>
  <view>
    <base-popup v-model="show" is-footer title="Contact customer service" height="550rpx">
      <view class="service">
        <view class="service__item">
          <zui-svg-icon icon="call-light" width="30rpx" height="30rpx" color="#103A62" />
          <view class="service__item__title">
            <view class="service__item__title__text">
              Telephone customer service (010-00000)
            </view>
            <view class="service__item__title__desc">
              Working hours :08:00-18:00
            </view>
          </view>
        </view>
        <view class="service__item">
          <zui-svg-icon icon="user3-light" width="30rpx" height="30rpx" color="#103A62" />
          <view class="service__item__title">
            <view class="service__item__title__text">
              Online customer service
            </view>
          </view>
        </view>
        <view class="service__item">
          <zui-svg-icon icon="message-light" width="30rpx" height="30rpx" color="#103A62" />
          <view class="service__item__title">
            <view class="service__item__title__text">
              Official email
            </view>
            <view class="service__item__title__desc">
              <text><EMAIL></text>
            </view>
          </view>
        </view>
      </view>
      <template #footer>
        <view class="service__footer">
          <view class="service__footer__btn left" @tap.stop="show = false">
            <text class="fs--14--400 service__footer__btn__text">
              Return
            </text>
          </view>
          <view class="service__footer__btn right blue">
            <text class="fs--14--400 service__footer__btn__text">
              Coupon package
            </text>
          </view>
        </view>
      </template>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './concact-service.scss'
</style>
