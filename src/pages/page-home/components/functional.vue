<script lang="ts" setup>
import type { FunctionalCardData } from './functionalCard.vue'

withDefaults(defineProps<{
  data?: FunctionalCardData
}>(), {
  data: () => ({}),
})

const emit = defineEmits<{
  (e: 'change', data: FunctionalCardData): void
}>()
</script>

<template>
  <view class="functional-container" @click="emit('change', data)">
    <zui-svg-icon style="margin-right: 24rpx;" width="64rpx" height="64rpx" :icon="data.icon" />
    <view class="functional-container__right">
      <text class="functional-container__right__title">
        {{ data.title }}
      </text>
      <text class="functional-container__right__content">
        {{ data.text }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.functional-container {
    width: 100%;
    height: 120rpx;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 16rpx 12rpx;
    border-bottom: 1px solid #f5f6f6;
    &__right {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &__title {
            font-family: "Alimama FangYuanTi VF";
            font-size: 28rpx;
            line-height: 48rpx;
            color: #4b5353;
            font-weight: 700;
        }
        &__content {
            font-family: "Alimama FangYuanTi VF";
            font-weight: 400;
            font-size: 20rpx;
            color: #1F1F1F;
            line-height: 40rpx;
        }
    }
}
</style>
