<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import swiperPng from '@/static/test/guide-swiper.png'

withDefaults(defineProps<{ list?: Partial<BaseSwiperItemType>[] }>(), {
  list: () => [
    {
      id: 1,
      image: swiperPng,
    },
    {
      id: 2,
      image: swiperPng,
    },
    {
      id: 3,
      image: swiperPng,
    },
  ],
})
</script>

<template>
  <base-swiper height="376rpx" :list="list" />
</template>

<style lang="scss" scoped>

</style>
