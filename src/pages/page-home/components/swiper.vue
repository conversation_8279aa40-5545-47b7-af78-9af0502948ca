<script setup lang="ts">
import type { BaseSwiperItemType } from '@/model/common'
import swiperPng from '@/static/test/guide-swiper.png'

withDefaults(defineProps<{ list?: BaseSwiperItemType[] }>(), {
  list: () => [
    {
      id: 1,
      url: swiperPng,
    },
    {
      id: 2,
      url: swiperPng,
    },
    {
      id: 3,
      url: swiperPng,
    },
  ],
})
</script>

<template>
  <base-swiper height="376rpx" :list="list" />
</template>

<style lang="scss" scoped>

</style>
