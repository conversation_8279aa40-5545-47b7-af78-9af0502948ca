<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'
import { AddressEnum, AddressInfoEnum } from '@/enum/home'
import { getStore, setStore } from '@/utils/store'
import Calendar from './calendar.vue'

withDefaults(defineProps<{
  title?: string
  text?: string
}>(), {
  title: 'Package',
  text: 'Save more when you book more！',
})

const addressInfo = ref({
  depart: '',
  arrive: '',
  departId: '',
  arriveId: '',
  date: null,
})

const isSwitch = ref(false)

function handleSwitch() {
  const tempDePart = addressInfo.value.depart
  const tempDepartId = addressInfo.value.departId
  addressInfo.value.departId = addressInfo.value.arriveId
  addressInfo.value.arriveId = tempDepartId
  addressInfo.value.depart = addressInfo.value.arrive
  addressInfo.value.arrive = tempDePart
  const { date, ...parms } = addressInfo.value
  setStore(AddressInfoEnum.ADDRESS_INFO, {
    way: 'oneWay',
    ...parms,
  })
}

function handleSelectAddress(address: string) {
  uni.navigateTo({
    url: `/subPackages/page-home/select-address/index?way=oneWay&address=${address}`,
  })
}

function handleSearch() {
  // console.log(addressInfo.value, 'addressInfo-oneWay')
  if (!addressInfo.value.arrive || !addressInfo.value.depart) {
    return
  }
  uni.navigateTo({
    url: '/subPackages/page-home/search-ticket/index',
  })
}

onShow(() => {
  const { ...data } = getStore(AddressInfoEnum.ADDRESS_INFO)
  addressInfo.value = {
    date: addressInfo.value.date,
    ...data,
  }
})

onMounted(() => {

})
</script>

<template>
  <view class="one-way-container">
    <view class="one-way-container__top">
      <view class="one-way-container__top__way">
        <view
          class="one-way-container__top__way__from"
          :class="{ 'one-way-container__top__way__from--move': isSwitch }"
          @click.stop="handleSelectAddress(AddressEnum.DEPART_ADDRESS)"
        >
          <view class="one-way-container__top__way__from--inactive">
            <zui-svg-icon class="one-way-container__top__way__to--inactive__icon" width="40rpx" height="40rpx" icon="home-location-from" />
            <text class="one-way-container__top__way__from--inactive__text">
              {{ addressInfo.depart ? addressInfo.depart : 'From' }}
            </text>
          </view>
        </view>
        <view
          class="one-way-container__top__way__to"
          :class="{ 'one-way-container__top__way__to--move': isSwitch }"
          @click.stop="handleSelectAddress(AddressEnum.ARRIVE_ADDRESS)"
        >
          <view class="one-way-container__top__way__to--inactive">
            <zui-svg-icon class="one-way-container__top__way__to--inactive__icon" width="40rpx" height="40rpx" icon="home-location-to" />
            <text class="one-way-container__top__way__to--inactive__text">
              {{ addressInfo.arrive ? addressInfo.arrive : 'To' }}
            </text>
          </view>
        </view>
      </view>
      <zui-svg-icon width="80rpx" height="80rpx" icon="home-switch" @click.stop="handleSwitch" />
    </view>
    <Calendar v-model="addressInfo.date" style="width: 100%;z-index:9999;" />
    <view class="one-way-container__search" @click.stop="handleSearch">
      <text class="one-way-container__search__text">
        {{ $t('home.searchText') }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.one-way-container {
  width: 686rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__top{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    &__way{
      display: flex;
       flex-direction: column;
      &__from, &__to {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 588rpx;
        height: 88rpx;
        border-radius: 24rpx;
        padding: 24rpx;
        background-color: #fafafa;
        &--inactive{
          display: flex;
          flex-direction: row;
          align-items: center;
          &__icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 24rpx;
          }
          &__text {
            font-family: "Alimama FangYuanTi VF";
            font-weight: 400;
            color: #717680;
            font-size: 24rpx;
          }
        }
        &--active {
          font-size: 24rpx;
          color: black;
        }
      }

      &__from {
        margin-bottom: 16rpx;
        transition:all .3s;
        &--move{
          transform: translateY(104rpx);
        }
      }

      &__to {
        transition:all .3s;
        &--move {
          transform: translateY(-104rpx);
        }
      }
    }

  }

  &__search {
    display: flex;
     flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 84rpx;
    background-color: #103a62;
    margin-top: 32rpx;
    border-radius: 32rpx;
    &__text {
      font-family: "Alimama FangYuanTi VF";
      font-size: 28rpx;
      color: #fff;
      font-weight: 400;
    }
  }

}
</style>
