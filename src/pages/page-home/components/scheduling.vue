<script lang="ts" setup>
import type { Component } from 'vue'
import { ref, shallowRef } from 'vue'
import { useI18n } from 'vue-i18n'
import { AddressEnum, AddressInfoEnum } from '@/enum/home'
import { removeStore } from '@/utils/store'
import mutiCity from './mutiCity.vue'
import oneWay from './oneWay.vue'
import roundTrip from './roundTrip.vue'

const { t } = useI18n()

interface LineTabItem {
  value: string
  label: string
}

const page = shallowRef<Record<string, Component>>({
  oneWay,
  roundTrip,
  mutiCity,
})

const tabList: LineTabItem[] = [
  {
    value: 'oneWay',
    label: t('home.oneWay'),
  },
  {
    value: 'roundTrip',
    label: t('home.roundTrip'),
  },
  {
    value: 'mutiCity',
    label: t('home.mutiCity'),
  },
]
const active = ref('oneWay')

function handleClickTab(tab: LineTabItem): void {
  // removeStore(AddressEnum.DEPART_ADDRESS)
  // removeStore(AddressEnum.ARRIVE_ADDRESS)
  removeStore(AddressInfoEnum.ADDRESS_INFO)
  active.value = tab.value
}
</script>

<template>
  <view class="scheduling-container">
    <view class="scheduling-container__tab">
      <view
        v-for="(item) in tabList"
        :key="item.value"
        class="scheduling-container__tab__tab-item"
        :class="{ 'scheduling-container__tab__tab-item--active': item.value === active }"
        @click="handleClickTab(item)"
      >
        <text class="scheduling-container__tab__tab-item--active_text">
          {{ item.label }}
        </text>
      </view>
    </view>
    <component :is="page[active]" />
  </view>
</template>

<style lang="scss" scoped>
.scheduling-container {
    // border: 1px solid red;
    &__tab {
        display: flex;
        flex-direction: row;
        margin-bottom: 32rpx;
        &__tab-item{
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 12rpx;
            height: 80rpx;
            border-radius: 20rpx;
            background-color: #acbecf;

            &::first-child, &::last-child {
                margin: 0;
            }

            &--active {
                background-color: #103a62;
                &_text {
                    font-family: "Alimama FangYuanTi VF";
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #fff;
                }
            }

        }
    }
}

:deep(.l-tabbar-item__content) {
    background-color: #acbecf;
}
</style>
