<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { AddressEnum, AddressInfoEnum } from '@/enum/home'
import { getStore, setStore } from '@/utils/store'
import Calendar from './calendar.vue'

withDefaults(defineProps<{
  title?: string
  text?: string
}>(), {
  title: 'Package',
  text: 'Save more when you book more！',
})

const canlendarZIndex = ref(9999)
const addressInfo = ref({
  depart: '',
  arrive: '',
  date: null,
  returnDate: null,
})

function handleSearch() {
  // setStore(AddressInfoEnum.ADDRESS_INFO, {
  //   depart: addressInfo.value?.depart,
  //   arrive: addressInfo.value?.arrive,
  // })
  if (!addressInfo.value.arrive || !addressInfo.value.depart) {
    return
  }
  uni.navigateTo({
    url: '/subPackages/page-home/search-round-trip-ticket/index',
  })
}

function handleSelectAddress(address: string) {
  uni.navigateTo({
    url: `/subPackages/page-home/select-address/index?way=roundTrip&address=${address}`,
  })
}

function handleVisibleCalendar(isOver: boolean) {
  canlendarZIndex.value = isOver ? 999 : 9999
}

onShow(() => {
  const { depart, arrive } = getStore(AddressInfoEnum.ADDRESS_INFO)
  addressInfo.value.depart = depart ?? ''
  addressInfo.value.arrive = arrive ?? ''
})
</script>

<template>
  <view class="round-trip-container">
    <view class="round-trip-container__top">
      <view class="round-trip-container__top__way">
        <view class="round-trip-container__top__way__from" @click.stop="handleSelectAddress(AddressEnum.DEPART_ADDRESS)">
          <view class="round-trip-container__top__way__from--inactive">
            <zui-svg-icon class="round-trip-container__top__way__to--inactive__icon" width="40rpx" height="40rpx" icon="home-location-from" />
            <text class="round-trip-container__top__way__from--inactive__text">
              {{ addressInfo.depart ? addressInfo.depart : 'From' }}
            </text>
          </view>
        </view>
        <view class="round-trip-container__top__way__to" @click.stop="handleSelectAddress(AddressEnum.ARRIVE_ADDRESS)">
          <view class="round-trip-container__top__way__to--inactive">
            <zui-svg-icon class="round-trip-container__top__way__to--inactive__icon" width="40rpx" height="40rpx" icon="home-location-to" />
            <text class="round-trip-container__top__way__to--inactive__text">
              {{ addressInfo.arrive ? addressInfo.arrive : 'To' }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <Calendar v-model="addressInfo.date" style="width: 100%;z-index:9999;margin-bottom: 16rpx" @show="handleVisibleCalendar(true)" />
    <Calendar v-model="addressInfo.returnDate" placeholder="Return Time" :style="{ 'width': '100%', 'z-index': canlendarZIndex }" @show="handleVisibleCalendar(false)" />
    <view class="round-trip-container__search" @click="handleSearch">
      <text class="round-trip-container__search__text">
        {{ $t('home.searchText') }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.round-trip-container {
    width: 686rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    &__top{
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        &__way{
            flex: 1;
            display: flex;
            flex-direction: column;
            &__from, &__to {
                display: flex;
                flex-direction: row;
                align-items: center;
                width: 100%;
                height: 88rpx;
                border-radius: 24rpx;
                padding: 24rpx;
                background-color: #fafafa;
                &--inactive{
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    &__icon {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 24rpx;
                    }
                    &__text {
                        font-family: "Alimama FangYuanTi VF";
                        font-weight: 400;
                        color: #717680;
                        font-size: 24rpx;
                    }
                }
                &--active {
                    font-size: 24rpx;
                    color: black;
                }
            }

            &__from {
                margin-bottom: 16rpx;
            }

        }

        &__switch {
            width: 80rpx;
            height: 80rpx;
        }
    }

    &__search {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 84rpx;
        background-color: #103a62;
        margin-top: 32rpx;
        border-radius: 32rpx;
        &__text {
            font-family: "Alimama FangYuanTi VF";
            font-size: 28rpx;
            color: #fff;
            font-weight: 400;
        }
    }

}
</style>
