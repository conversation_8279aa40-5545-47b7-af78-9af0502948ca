<script lang="ts" setup>
import type { collapseItemProps } from 'wot-design-uni/components/wd-collapse-item/types"'
import { ref } from 'vue'
import QACollapseItem from './QACollapseItem.vue'

export type QAItem = {
  id: number
  title: string
  abstract: string
} & Partial<collapseItemProps>

withDefaults(defineProps<{ qaList?: QAItem[] }>(), {
  qaList: () => [
    {
      title: '1. Is ticket change and refund supported?',
      abstract: '自定义内容1',
      id: 1,
    },
    {
      title: '2. Which payment methods are supported?',
      abstract: '自定义内容2',
      id: 2,
    },
    {
      title: '3. Is there a tour guide service?',
      abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.Aeneaneuismod bibendum laoreet, Proin gravida dolor sit ametlacus accumsan et viverra justo commodo.',
      id: 3,
    },
  ],
})

const value = ref('1')
</script>

<template>
  <view class="qa-container">
    <text class="qa-container__title">
      Q&A
    </text>
    <wd-collapse v-model="value" custom-class="qa-custom-collapse" accordion>
      <QACollapseItem v-for="(item, idx) in qaList" :key="idx" :data="item" />
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
.qa-container {

    &__title {
        font-family: "Alimama FangYuanTi VF";
        font-weight: 400;
        color: #1D232E;
        font-size: 32rpx;
        letter-spacing: 1px;
    }

}

.qa-container{
     :deep(.wd-collapse-item.is-border::after){
       display: none;
    }
    :deep(.wd-collapse-item__header.is-border::after){
        display: none;
    }
     :deep(.wd-collapse-item__header.is-expanded::after){
        display: none;
    }
    :deep(.wd-collapse-item__title){
        font-family: Alimama FangYuanTi VF;
        font-weight: 500;
        font-size: 24rpx;
        color: #1D232E;
    }

    :deep(.wd-collapse-item__header){
        height: 84rpx;
        border-radius: 24rpx;
        border-bottom: 1rpx solid #EAECF0;
        background: #FFF;
        box-shadow: 0 4rpx 20rpx 0 rgba(0, 0, 0, 0.04);
    }
    :deep(.wd-collapse-item__body){
       border-radius: 24rpx;
        border-bottom: 0.5px solid #EAECF0;
        background: #FAFAFA;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
    }
}

.qa-custom-collapse{
    margin-top: 32rpx;
}
</style>
