<script lang="ts" setup>
import type { QAItem } from '.QACollapse.vue'

withDefaults(defineProps<{
  data?: QAItem
}>(), {
  data: () => ({}),
})
</script>

<template>
  <wd-collapse-item :title="data?.title" :name="`${data.id}`">
    <template #title="{ expanded }">
      <view class="qa-custom-collapse-item-header">
        <text class="qa-custom-collapse-item-header__title">
          {{ data?.title }}
        </text>
        <zui-svg-icon style="margin-right: 24rpx;" width="38rpx" height="36rpx" :icon="expanded ? 'arrow-down2-light' : 'arrow-right2-light'" />
      </view>
    </template>
    <text class="collapse-item-text">
      {{ data?.abstract }}
    </text>
  </wd-collapse-item>
</template>

<style lang="scss" scoped>
.collapse-item-text{
    font-family: "Alimama FangYuanTi VF";
    font-size: 12px;
    color: #1D232E;
    font-weight: 400;
}

.qa-custom-collapse-item-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  &__title{
     font-family: Alimama FangYuanTi VF;
      font-weight: 500;
      font-size: 24rpx;
      color: #1D232E;
  }
}

.qa-collapse-item{
  border: none;
}
</style>
