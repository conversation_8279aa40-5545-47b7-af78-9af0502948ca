<script lang="uts" setup>
import type { Ref } from 'vue'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { AddressInfoEnum } from '@/enum/home'
import { removeStore } from '@/utils/store'
import Functional from './functional.vue'

const { t } = useI18n()

export interface FunctionalCardData {
  title: string
  text: string
  icon: string
  url: string
}

const domainList: Ref<FunctionalCardData[]> = ref([
  {
    title: t('home.package'),
    text: t('home.packageText'),
    icon: 'package',
    url: '/subPackages/page-home/package/index',
  },
  {
    title: t('home.dayTours'),
    text: t('home.dayToursText'),
    icon: 'dayTours',
    url: '/subPackages/page-home/day-tours/index',
  },
  {
    title: t('home.transfer'),
    text: t('home.transferText'),
    icon: 'transfer',
    url: '/subPackages/page-home/transfer/index',
  },
])

function handleCheckDetail(item: FunctionalCardData) {
  removeStore(AddressInfoEnum.ADDRESS_INFO)
  uni.navigateTo({
    url: item.url,
  })
}
</script>

<template>
  <view class="functional-card-container">
    <Functional
      v-for="(item, index) in domainList"
      :key="index"
      :data="item"
      @change="handleCheckDetail"
    />
  </view>
</template>

<style lang="scss" scoped>
.functional-card-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
</style>
