<script lang="ts" setup>
import type { AddressInfo } from '@/model/home/<USER>'
import { onShow } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { AddressEnum, AddressInfoEnum, SequenceEnum } from '@/enum/home'
import { getStore, setStore } from '@/utils/store'
import Calendar from './calendar.vue'

export interface TripDataItem {
  startTime: null | number
  depart: string
  arrive: string
}

withDefaults(defineProps<{
  title?: string
  text?: string
}>(), {
  title: 'Package',
  text: 'Save more when you book more！',
})

const ZIndex = ref(0)

const tripData = reactive<TripDataItem[]>([
  {
    startTime: null,
    depart: '',
    arrive: '',
  },
])

const addressList = ref([])

onShow(() => {
  addressList.value = getStore(AddressInfoEnum.ADDRESS_INFO) || []
  if (!addressList.value.length) {
    return
  }
  addressList.value.forEach((item: AddressInfo, idx: number) => {
    const { depart, arrive } = item
    tripData[idx] = { ...item, depart, arrive }
  })
})

function handleClose(index: number) {
  addressList.value.splice(index, 1)
  tripData.splice(index, 1)
  setStore(AddressInfoEnum.ADDRESS_INFO, addressList.value)
}

function handleCancel() {
  setTimeout(() => {
    ZIndex.value = 0
  }, 500)
}

function handleAddTrip() {
  ZIndex.value = 0
  if (tripData.length >= 5 || !tripData[tripData.length - 1].depart || !tripData[tripData.length - 1].arrive || !tripData[tripData.length - 1].startTime) {
    return
  }
  tripData.push({
    startTime: null,
    depart: '',
    arrive: '',
  })
}

function handleSearch() {
  if (tripData.length < 1 || !tripData[tripData.length - 1].depart || !tripData[tripData.length - 1].arrive || !tripData[tripData.length - 1].startTime) {
    return
  }
  uni.navigateTo({
    url: '/subPackages/page-home/search-muti-city-ticket/index',
  })
}

function handleSelectAddress(address: string, idx: number) {
  uni.navigateTo({
    url: `/subPackages/page-home/select-address/index?way=mutiCity&idx=${idx}&address=${address}`,
  })
}

function handleSelectTime(index: number, time: number | null) {
  ZIndex.value = 0
  const data = getStore(AddressInfoEnum.ADDRESS_INFO)
  if (!data) {
    setStore(AddressInfoEnum.ADDRESS_INFO, [
      {
        startTime: time,
      },
    ])
    return
  }

  data[index] = {
    ...data[index],
    startTime: time,
  }

  setStore(AddressInfoEnum.ADDRESS_INFO, data)
}

function handleShowCalendar() {
  ZIndex.value = 9999
}
</script>

<template>
  <view class="muti-city-container" :style="{ 'z-index': ZIndex }">
    <view v-for="(item, idx) in tripData" :key="idx" class="muti-city-container__top">
      <Calendar
        v-model="item.startTime"
        :show-close="idx > 0"
        :placeholder="`${SequenceEnum[idx]} trip time`"
        style="width: 100%;z-index:999; margin-bottom: 16rpx;"
        @close="handleClose(idx)"
        @cancel="handleCancel"
        @show="handleShowCalendar"
        @confirm="handleSelectTime(idx, $event)"
      />
      <view class="muti-city-container__top__way">
        <view class="muti-city-container__top__way__from" @click.stop="handleSelectAddress(AddressEnum.DEPART_ADDRESS, idx)">
          <view class="muti-city-container__top__way__from--inactive">
            <zui-svg-icon class="muti-city-container__top__way__from--inactive__icon" icon="home-location-from" />
            <text class="muti-city-container__top__way__from--inactive__text">
              {{ item.depart ? item.depart : 'From' }}
            </text>
          </view>
        </view>
        <view class="muti-city-container__top__way__to" @click.stop="handleSelectAddress(AddressEnum.ARRIVE_ADDRESS, idx)">
          <view class="muti-city-container__top__way__to--inactive">
            <zui-svg-icon class="muti-city-container__top__way__to--inactive__icon" icon="home-location-to" />
            <text class="muti-city-container__top__way__to--inactive__text">
              {{ item.arrive ? item.arrive : 'To' }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="muti-city-container__add" @click="handleAddTrip">
      <text class="muti-city-container__add__icon">
        +
      </text>
      <text class="muti-city-container__add__text">
        {{ $t('home.addTrip') }}
      </text>
    </view>
    <view class="muti-city-container__search" @click="handleSearch">
      <text class="muti-city-container__search__text">
        {{ $t('home.searchText') }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.muti-city-container {
    position: relative;
    z-index: 9999;
    width: 686rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    &__top{
        width: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        &__way{
            width: 100%;
            display: flex;
            flex-direction: column;
            &__from, &__to {
                display: flex;
                flex-direction: row;
                align-items: center;
                width: 100%;
                height: 88rpx;
                border-radius: 24rpx;
                padding: 24rpx;
                background-color: #fafafa;
                &--inactive{
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    &__icon {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 24rpx;
                    }
                    &__text {
                        font-family: "Alimama FangYuanTi VF";
                        font-weight: 400;
                        color: #717680;
                        font-size: 24rpx;
                    }
                }
                &--active {
                    font-size: 24rpx;
                    color: black;
                }
            }

            &__from {
                margin-bottom: 16rpx;
            }

        }

        &__switch {
            width: 80rpx;
            height: 80rpx;
        }
    }

    &__add {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 76rpx;
        border-radius: 32rpx;
        background-color: #fafafa;
        &__icon{
            margin-right: 12rpx;
        }

        &__icon, &__text {
            font-family: "Alimama FangYuanTi VF";
            font-weight: 400;
            color: #103a62;
            font-size: 24rpx;
        }
    }

    &__search {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 84rpx;
        background-color: #103a62;
        margin-top: 32rpx;
        border-radius: 32rpx;
        &__text {
            font-family: "Alimama FangYuanTi VF";
            font-size: 28rpx;
            color: #fff;
            font-weight: 400;
        }
    }

}
</style>
