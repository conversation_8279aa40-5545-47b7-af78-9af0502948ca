<script lang="ts" setup>
import type { CalendarInstance } from 'wot-design-uni/components/wd-calendar/types'
import { computed, onMounted, ref } from 'vue'
import { dayjs } from 'wot-design-uni'

const props = withDefaults(defineProps<{ modelValue: number | null, placeholder?: string, showClose?: boolean }>(), {
  placeholder: 'Time',
  showClose: false,
})
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'update:modelValue', value: number | null): void
  (e: 'show'): void
  (e: 'confirm', data: number | null): void
}>()
const TIME_TYPE = 1
const calendarRef = ref<CalendarInstance | null>(null)
const minDate = ref(0)
const date = computed({
  get() {
    return props.modelValue! * TIME_TYPE
  },
  set(val: number | null) {
    const value = val! / TIME_TYPE
    emit('update:modelValue', value)
  },
})
const dateText = computed(() => {
  return date.value ? dayjs(date.value).format('YYYY/MM/DD') : ''
})

onMounted(() => {
  minDate.value = Date.now()
})

function handleVisibleCalendar() {
  emit('show')
  calendarRef.value?.open()
}

function handleClose() {
  emit('close')
}

function handleConfirm(dateValue: { value: number | null, type: string }) {
  date.value = dateValue.value
  emit('confirm', dateValue.value)
}
</script>

<template>
  <view class="calendar-container">
    <view class="calendar-container__placeholder" @click.stop="handleVisibleCalendar">
      <slot>
        <view v-show="!date" class="calendar-container__placeholder__container">
          <view class="calendar-container__placeholder__container__content">
            <zui-svg-icon class="mr" width="40rpx" height="40rpx" icon="calendar-light" />
            <text class="calendar-container__placeholder__container__content__text">
              {{ placeholder }}
            </text>
          </view>
          <image
            v-if="showClose"
            class="calendar-container__placeholder__container__close-icon"
            src="/static/home/<USER>"
            mode="aspectFill"
            @click.stop="handleClose"
          />
        </view>
        <view v-show="!!date" class="calendar-container__date">
          <view class="calendar-container__date__value">
            <zui-svg-icon class="mr" width="40rpx" height="40rpx" icon="calendar-light" />
            <text>{{ dateText }}</text>
          </view>
          <image
            v-if="showClose"
            class="calendar-container__date__close-icon"
            src="/static/home/<USER>"
            mode="aspectFill"
            @click.stop="handleClose"
          />
        </view>
      </slot>
    </view>
    <wd-calendar ref="calendarRef" v-model="date" :min-date="minDate" :with-cell="false" @confirm="handleConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.calendar-container {
    height: 88rpx;
    border-radius: 24rpx;
    background-color: #fafafa;
    &__placeholder {
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 24rpx;
        &__container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;

            &__content {
                display: flex;
                flex-direction: row;
                align-items: center;
                &__text {
                    font-family: "Alimama FangYuanTi VF";
                    font-weight: 400;
                    color: #717680;
                    font-size: 24rpx;
                }
            }

            &__close-icon {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
    &__date{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        &__value {
          display: flex;
          align-items: center;
        }
        &__close-icon {
            width: 40rpx;
            height: 40rpx;
        }
    }
}

.mr {
    margin-right: 24rpx;
}
</style>
