<script lang="ts" setup>
import type { HomePage } from '@/model/home/<USER>'
import { ref } from 'vue'
import { ApiGetHomeData } from '@/api/home/<USER>'
import FunctionalCard from './components/functionalCard.vue'
import QACollapse from './components/QACollapse.vue'
import Scheduling from './components/scheduling.vue'
import Swiper from './components/swiper.vue'

const homePageData = ref<HomePage>({
  article: [],
  banner: [],
  notice: [],
})
const current = ref<number>(0)
function onChange(e: any) {
  current.value = e.detail.current
}

async function homepageList() {
  const data = await ApiGetHomeData()
  homePageData.value = data
  console.log(data, 'data')
}

homepageList()

const flag = ref(uni.getStorageSync('lang') === 'zh-Hans')

function siwtchLang() {
  if (flag.value) {
    uni.setLocale('zh-Hans')
    uni.setStorageSync('lang', 'zh-<PERSON>')
  }
  else {
    uni.setLocale('en')
    uni.setStorageSync('lang', 'en')
  }
  flag.value = !flag.value
}
</script>

<template>
  <page>
    <view class="page">
      <nav-bar />
      <view
        class="home-container"
      >
        <view class="home-container__swiper mb" style="position: relative;">
          <Swiper :list="homePageData.banner" />
        </view>
        <tips
          class="mb"
          :text="$t('home.tipView')"
          :list="['View', 'Share', 'More']"
        />
        <Scheduling class="mb" />
        <FunctionalCard />
        <view class="home-container__line mb" />
        <QACollapse :qa-list="homePageData.article" />
      </view>
    </view>
    <tab-bar :current="0" />
  </page>
</template>

<style lang="scss" scoped>
.home-container {
padding: 32rpx;
background-color: var(--color-white);

&__line {
  width: 100%;
  height: 2rpx;
  background-color: #f5f6f6;
  margin-top: 24rpx;
}
}

.slide {
width: 646rpx;
height: 376rpx;
background-color: skyblue;

&__img {
  width: 100%;
  height: 100%;
}
}

.mb {
margin-bottom: 32rpx;
}
</style>
