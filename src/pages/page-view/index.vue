<script setup lang="ts">
import { ref } from 'vue'
import listViewWrapper from './conponents/list-view-wrapper/list-view-wrapper.vue'

const mode = ref<'map' | 'list'>('list')

function switchMode() {
  mode.value = mode.value === 'map' ? 'list' : 'map'
}
</script>

<template>
  <page>
    <nav-bar :num="10">
      <view class="view_switch" @tap="switchMode">
        <zui-svg-icon width="32rpx" height="32rpx" color="#103A62" icon="transport-light" />
        <text class="fs--12--400 view_switch__text">
          {{ mode === 'map' ? 'View image list' : 'Map view' }}
        </text>
      </view>
    </nav-bar>
    <view class="body">
      <list-view-wrapper v-if="mode === 'list'" />
    </view>
    <tab-bar :current="2" />
  </page>
</template>

<style scoped lang="scss">
.view_switch{
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-left: 30rpx;

  &__text{
    margin-left: 15rpx;
    color: #103A62;
  }
}
</style>
