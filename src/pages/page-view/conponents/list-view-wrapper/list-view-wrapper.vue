<script setup lang="ts">
import { ref } from 'vue'
import viewItem from '../view-item/view-item.vue'
import viewRecommended from '../view-recommended/view-recommended.vue'

const filterList = ref<any[]>([
  { label: 'route' },
  { label: 'category' },
  { label: 'price' },
  { label: 'purchases' },
])
</script>

<template>
  <view class="list__view">
    <view-recommended />
    <view>
      <search-input disabled text="" />
    </view>
    <view class="list__filter">
      <view
        v-for="(item, index) in filterList" :key="index" class="list__filter__items"
      >
        <text class="fs--14--400">
          {{ item.label }}
        </text>
        <view class="list__filter_items__icon">
          <zui-svg-icon width="32rpx" height="32rpx" color="#000000" icon="arrow-down2-light" />
        </view>
      </view>
    </view>

    <view class="list__container">
      <view-item v-for="(item, index) in 10" :key="index" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.list__view {
    padding: 0 32rpx;
}
.list__filter{
    padding: 24rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &__items{
        display: flex;
        flex-direction: row;
        align-items: center;

        &__icon{
          margin-left: 10rpx;
        }
      }
}

.list__container{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    column-gap: 48rpx;
    row-gap: 24rpx;
    padding-bottom: 32rpx;
}
</style>
