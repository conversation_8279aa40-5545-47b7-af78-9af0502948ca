<script setup lang="ts">
import viewItem from '../view-item/view-item.vue'
</script>

<template>
  <view class="list">
    <view class="list__title">
      Popular attraction
    </view>
    <view class="list__container">
      <view-item v-for="(item, index) in 2" :key="index" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.list{
    padding-top: 30rpx;
    margin-bottom: 16rpx;

    &__title{
        color:#161616;
        margin-bottom: 32rpx;
        font-size: 30rpx;
        font-weight: 600;
    }

    &__container{
        display: grid;
        grid-template-columns: repeat(2,1fr);
        column-gap: 48rpx;
        row-gap: 24rpx;
    }

}
</style>
