<script lang="ts" setup>
withDefaults(defineProps<{
  customClass?: string
}>(), {
  customClass: '',
})

function toDetail() {
  uni.navigateTo({
    url: '/subPackages/page-view/view-detail/view-detail',
  })
}
</script>

<template>
  <view class="item" :class="customClass" @tap="toDetail">
    <image class="item__img" src="/static/test/guide-swiper.png" mode="aspectFill" />
    <text class="item__title fs--12--700">
      oct
    </text>
    <text class="item__subtitle fs--10--400">
      emple | Nanshan District
    </text>
    <view class="item__tags">
      <view class="item__tags__tag">
        E-guide
      </view>
      <view class="item__tags__tag">
        E-guide
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './view-item.scss'
</style>
