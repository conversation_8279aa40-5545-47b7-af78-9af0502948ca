// 赚积分列表
export interface EarnPointsItemTyp {
  title: string
  point: number
  is_btn: boolean
  id: number
}

// 个人中心
export interface UserCenterType {
  avatar: string
  integrate: number
  nickname: string
  account_type: string
  user_order_count: number
  guide_order_count: number
  noticer_ecord_count: number
}

// 积分中心
export interface PointCenterType {
  integrate: number
  integrate_rate: number
  integral_rule_article: {
    title: string
    content: string
  }
  coupons_use_article: {
    title: string
    content: string
  }
}

// 区号列表
export interface AreaListType {
  id: number
  country_name: string
  encountry_name: string
  localplace_name: string
  areacode: string
  sign: string
  created_at: string
}

/** 门店列表 */
export interface StoreListType {
  /** 门店id */
  id: number
  /** 门店名称 */
  name: string
  /** 门店图片 */
  image: string
  /** 营业时间开始   */
  business_time_start: string
  /** 营业时间结束 */
  business_time_end: string
  /** 门店类型 */
  type_text: string
}

/** 优惠券列表 */
export interface ICouponListType {
  id: number
  name: string
  integrate: number
  type_text: string
  type: number
  status_text: string
  status: number
  use_time_end_text: string
  end_time: string
}

/** 积分明细 */
export interface PointDetailType {
  id: number
  type_remark: string
  title: string
  change_amount: number
  order_no: string
  create_date: string
  create_time: string
  /** 1: 增加; 2: 减少 */
  action: 1 | 2
}
