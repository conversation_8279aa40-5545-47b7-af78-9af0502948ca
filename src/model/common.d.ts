// Tabber
export interface TabbarItemType {
  activeIcon: string
  defaultIcon: string
  label: string
  path: string
}

// baseSwiper类型
export interface BaseSwiperItemType {
  id: number
  name: string
  image: string
  show_type?: number
  inner_link?: string
  link?: string
  show?: number
  sort?: number
  create_time?: string
  update_time?: string
}

// 节点信息
export interface NodeRectInfo {
  id?: string
  left: number
  right: number
  top: number
  bottom: number
  width: number
  height: number
}

// 图片裁剪模式
export type ImageMode = 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'

// tabs
export interface TabsItem {
  label: string
  value: string | number
}

// 公告信息名称
export type NoticeNameType = 'privacy_policy' | 'user_agreement' | 'ticket_instructions' | 'package_ticket' | 'pick_service' | 'one_daytour' | 'invite_friends' | 'electronic_tourguide' | 'points_rules' | 'points_usage' | 'franchise_cooperation' | 'logout_agreement'

// 公告信息类型
export type NoticeType = 'generalprotocol' | 'aboutus'

// 公告信息
export interface NoticeInfo {
  content: string
  name: string
  logo?: string
  create_time?: string
}

export interface DistItemType {
  create_time: string
  delete_time: string
  id: string
  name: string
  remark: string
  sort: number
  status: number
  type_id: number
  type_value: string
  update_time: string
  value: string
}

export type DistType = 'tickets' | 'guide' | 'attraction' | 'pickup_service'

// 获取支付类型参数
export interface PayTypeParams {
  from: DistType
  order_id: number
}

// 获取支付类型
export interface PayTypeInfo {
  pay_way?: number
  pay_way_desc?: string
  order_amount?: string
}

// 支付发起参数
export interface SendPayParamsType {
  from: 'tickets' | 'guide' | 'attraction' | 'pickup_service'
  pay_way: number
  order_id: number
}

export interface PayResultConfig {
  client_secret: string
  order_id: number
  currency: string
  money: number
  public_key: string
  url: string
  ephemeralKey?: string
  customer?: string
}

// 获取发起支付配置
export interface PayResult {
  config?: PayResultConfig
  pay_way?: number
}

export interface PayStatusResult {
  pay_status?: number
  pay_way?: number
}
