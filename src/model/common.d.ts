// Tabber
export interface TabbarItemType {
  activeIcon: string
  defaultIcon: string
  label: string
  path: string
}

// baseSwiper类型
export interface BaseSwiperItemType {
  id: number
  name: string
  image: string
  show_type: number
  inner_link: string
  link: string
  show: number
  sort: number
  create_time: string
  update_time: string
}

// 节点信息
export interface NodeRectInfo {
  id?: string
  left: number
  right: number
  top: number
  bottom: number
  width: number
  height: number
}

// 图片裁剪模式
export type ImageMode = 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'

// tabs
export interface TabsItem {
  label: string
  value: string | number
}

export type NoticeType = 'privacy_policy' | 'user_agreement' | 'ticket_instructions' | 'package_ticket' | 'pick_service' | 'one_daytour' | 'invite_friends' | 'electronic_tourguide' | 'points_rules' | 'points_usage' | 'franchise_cooperation' | 'logout_agreement'

export interface NoticeInfo {
  name: string
  content: string
  type: NoticeType
}

export interface DistItemType {
  create_time: string
  delete_time: string
  id: string
  name: string
  remark: string
  sort: number
  status: number
  type_id: number
  type_value: string
  update_time: string
  value: string
}
