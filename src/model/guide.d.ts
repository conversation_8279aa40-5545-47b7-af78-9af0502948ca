// 电子导游列表
export interface GuideListItemType {
  id: number
  collect: number
  create_time: string
  delete_time: string
  expire_time: string
  update_time: string
  expire_time_day: number
  image: string
  is_buy: number
  is_recommend: number
  latitude: number
  longitude: number
  name: string
  region_id: number
  sales: number
  show: number
  type_id: number
  virtual: number
  way: number
  audiolist: AudioItemType[]
  price_list: PriceItemType[]
  price: string
}

// 电子导游播放详情
export interface GuidePlayDetailType {
  audioinfo: AudioItemType
  price: PriceItemType[]
}

// 电子导游详情
export interface GuideDetailType {
  info: {
    id: number
    name: string
    region_id: number
    type_id: number
    image: string
    way: number
    sales: number
    virtual: number
    collect: number
    is_recommend: number
    show: number
    create_time: string
    update_time: string
    delete_time: string
    is_buy: number
    expire_time: string
    expire_time_day: string
    is_collect: number

  }
  audiolist: AudioItemType[]
  price: PriceItemType[]
}

// 播放项
export interface AudioItemType {
  create_time: string
  delete_time: string
  duction: string
  duration: string
  file_url: string
  guide_id: number
  id: number
  language: string
  name: string
  play_count: number
  trial_duration: number | null
  update_time: string
  image: string
  is_buy: number
}

// 价格
export interface PriceItemType {
  id: number
  guide_id: number
  day: number
  price: string
  create_time: string
  update_time: string
  delete_time: strng
}

// 价格信息
export interface PriceInfoType {
  goodsAll: GoodsItemType[]
  money: number
  order_money: number
}

// 单项商品
export interface GoodsItemType {
  id: number
  image: string
  image_url: string
  name: string
  day: number
  delete_time: number
  goods_money: string
  num: number
  price: number
  show: number
  way: number
}

// 下单返回参数
export interface SaveOrderResultType {
  from: string
  order_amount: string
  order_id: number
}

// 打包购买价格
export interface ConfigPackType {
  three_days_price: string
  ten_days_price: string
  thirty_days_price: string
  create_time: string
}

// 电子导游订单
export interface GuideOrderItemType {
  id: number
  image: string
  order_id: number
  user_id: number
  goods_id: number
  price: string
  name: string
  day: number
  expire_time: string
  order_source: number
  create_time: string
  delete_time: string | null
  update_time: string
  guide_id: number
  audio_name: string
  file_url: string
  duction: string
  duration: string
  trial_duration: string
  play_count: number
  is_buy: number
  expire_time_day: string
}
