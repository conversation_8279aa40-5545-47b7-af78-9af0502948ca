// 购买电子导游套餐列表
export interface GuideBuyerItemType {
  title: string
  price: number
  unit: string
  id: number
}

// 电子导游列表
export interface GuideListItemType {
  id: number
  collect: number
  create_time: string
  delete_time: string
  expire_time: string
  update_time: string
  expire_time_day: number
  image: string
  is_buy: number
  is_recommend: number
  latitude: number
  longitude: number
  name: string
  region_id: number
  sales: number
  show: number
  type_id: number
  virtual: number
  way: number
  audiolist: AudioItemType[]
  price_list: PriceItemType[]
  price: string
}

// 播放项
export interface AudioItemType {
  create_time: string
  delete_time: string
  duction: string
  duration: string
  file_url: string
  guide_id: number
  id: number
  language: string
  name: string
  play_count: number
  trial_duration: number | null
  update_time: string
}

// 价格
export interface PriceItemType {
  id: number
  guide_id: number
  day: number
  price: string
  create_time: string
  update_time: string
  delete_time: strng
}
