import type { TerminalType } from '@/model/splash'

// 手机号/邮箱登陆参数
export interface LoginParamsType {
  scene: number
  account: string
  terminal: TerminalType | null
  code: string
}

// 苹果登录参数
export interface AppleLoginType {
  terminal: TerminalType | null
  identity_token: string
}

// 谷歌登录参数
export interface GoogleLoginType {
  terminal: TerminalType | null
  nickname: string
  openid: string
}

// 用户信息
export interface UserInfoType {
  id: number
  sn: string
  sex: number
  account: string
  nickname: string
  nickname: string
  avatar: string
  mobile: string
  create_time: string
  user_money: string
  birthday: number | null
  age: number | string
  is_driver: number
  country: string | null
  is_tourist_agency: number
  has_password: boolean
  has_auth: boolean
  version: string
  account_type: string
  points: number
}

// 登陆后返回信息
export interface LoginResType {
  token: string
  avatar: string
  email: string
  is_sign_apple: boolean
  is_sign_google: boolean
  mobile: string
  nickname: string
  sn: number
}
