export interface IOrderListType {
  id: number
  name: string
  integrate: number
  type_text: string
  type: number
  status_text: string
  status: number
  use_time_end_text: string
  end_time: string
}

// 订单列表请求参数
export interface OrderListParams {
  page_no?: string | number // 页码
  page_size?: string | number // 每页数量
  type?: 'ticket' | 'pickup_service' | 'day_tour' | 'attraction' // 订单类型
  status?: string // 订单状态
}
