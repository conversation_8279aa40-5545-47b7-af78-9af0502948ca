export interface IOrderListType {
  id: number
  name: string
  integrate: number
  type_text: string
  type: 'ticket' | 'day_tour' | 'pickup_service' | 'attraction'
  status_text: string
  /** 1-pending 3-used 2-cancelled 0-unpaid */
  status: number
  use_time_end_text: string
  from: string
  to: string
  arrive_time: string
  create_time: string
  travel_time: string
  order_amount: string
  activity_type: string
  car_info: {
    car_number: string
  }
  distance: string
  car_type_text: string
  cancel_desc: string
  process_desc: string
  passenger_type_text: string
  departure_time: string
  departure_time_text: string
  arrive_time_text: string
  coach_sn: string
  price: string
  travel_time_text: string
  travel_time: number
  arriver_desc: string
  close_time: number
  boarding_points: {
    name: string
    latitude: number
    longitude: number
    address: string
    image_lists: string[]
  }[]
}

// 订单列表请求参数
export interface OrderListParams {
  page_no?: string | number // 页码
  page_size?: string | number // 每页数量
  type?: 'ticket' | 'pickup_service' | 'day_tour' | 'attraction' // 订单类型
  status?: string // 订单状态
}

// 订单详情
export interface IOrderDetail {
  ticket: {
    status: 1 | 3 | 2 | 0
    status_text: string
    order_amount: string
    wait_time: string
  }
  amount: {
    order_sn: string
    create_time: string
    pay_way: string
    pay_status: number
    pay_status_text: string
    order_amount: string
    integrate: number
    assign_time: string
  }
  coachdriver: {
    car_type: string
    car_color: string
    car_image: string
    car_number: string
    driver_name: string
    driver_phone: string
    max_passengers: string
  }
  points: {
    from_id: number
    to_id: number
    from_name: string
    form_is_meal: number
    to_name: string
    to_is_meal: number
    travel_time: number
    distance: string
    traveler_count: number
  }
}
