export type SelectWay = 'oneWay' | 'roundTrip' | 'mutiCity' | 'transfer'
export interface ParamsInfo {
  address: string
  way: SelectWay
  idx?: number
}
export interface AddressInfo {
  way: SelectWay
  depart: string
  arrive: string
  departId: number
  arriveId: number
  startTime: number
  endTime?: number
  date?: string
}

export interface StationInfo {
  name: string
  id: number
  is_meal?: boolean
}

export interface LineItem {
  id: number
  name: string
  stations: StationInfo[]
}

export interface HotInfo {
  id: number
  name: string
}
export interface RouteInfo {
  data?: LineItem[]
  hot?: HotInfo[]
}

export interface BusInfo {
  id: number
  color: string
  image_lists: string[]
  max_passengers: number
  sn: string
  type: string
}

export interface DetailRoutteInfo {
  id: number
  name: string
  start_station: string
  end_station: string
  is_change: number
  departure_time: string
  route_detail: string
}

export interface DetailStationInfo {
  id: number
  name: string
  image: string
  type: string
  route_type: string
  is_meal?: boolean
  steer_time?: string
  distance?: string
}

export interface TicketRouteDetail {
  coachs?: BusInfo[]
  route?: DetailRoutteInfo
  stations?: DetailStationInfo[]
}
