import type { BaseSwiperItemType } from '@/model/common'

export interface LuggageInfo {
  large?: number
  small?: number
  medium?: number
}

export interface Transfer {
  form_id: number
  to_id: number
  car_type: number
  depart?: string
  arrive?: string
  travel_time?: null | number
  traveler_count?: number
  phone?: string
  email?: string
  luggage_info: LuggageInfo
  type: number
  departId: number
  arriveId: number
}

export interface TransferBananer {
  article: any
  banner: Partial<BaseSwiperItemType>[]
}
