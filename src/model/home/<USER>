export type SelectWay = 'oneWay' | 'roundTrip' | 'mutiCity' | 'transfer'
// 选择地址url参数
export interface ParamsInfo {
  address: string
  way: SelectWay
  idx?: number
}

// 本地存储的地址信息
export interface AddressInfo {
  way: SelectWay
  depart: string
  arrive: string
  departId: number
  arriveId: number
  startTime: number
  endTime?: number
  date?: string
}

// 站点信息
export interface StationInfo {
  name: string
  id: number
  is_meal?: boolean
}

// 路线信息
export interface LineItem {
  id: number
  name: string
  stations: StationInfo[]
}

// 热门线路信息
export interface HotInfo {
  id: number
  name: string
}
export interface RouteInfo {
  data?: LineItem[]
  hot?: HotInfo[]
}

// 车次信息
export interface BusInfo {
  id: number
  color: string
  image_lists: string[]
  max_passengers: number
  sn: string
  type: string
}

// 详情页路线信息
export interface DetailRoutteInfo {
  id: number
  name: string
  start_station: string
  end_station: string
  is_change: number
  departure_time: string
  route_detail: string
}

// 详情页站点信息
export interface DetailStationInfo {
  id: number
  name: string
  image: string
  type: string
  route_type: string
  is_meal?: boolean
  steer_time?: string
  distance?: string
}

// 车票详情信息
export interface TicketRouteDetail {
  coachs?: BusInfo[]
  route?: DetailRoutteInfo
  stations?: DetailStationInfo[]
}

export interface TrasnferStationInfo {
  address: string
  id: number
  image: string
  is_meal: number
  latitude: number
  longitude: number
  name: string
  route_id: number
}

export interface TransferAddress {
  id: number
  name: string
  shared_price: string
  single_price: string
  stations: TrasnferStationInfo[]
}
