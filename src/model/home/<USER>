export interface TicketInfo {
  adult?: number
  adult_count?: number // 成人票数量
  children?: number
  child_count?: number // 儿童票数量
  baby?: number
  infant_count?: number // 婴儿票数量
  phoneNumber?: string
  phone?: string // 手机号
  email?: string
  rebookChecked?: boolean
  is_use_integrate?: boolean // 是否使用积分
  deductionChecked?: boolean
  is_change?: boolean // 是否改签
}

export interface BusInfo {
  id: number
  route_id?: number
  coach_id?: number
  schedule_id?: number
  station_count?: number
  travel_time: string
  coach_sn: string
  departure_time: string
  is_fest_discount: number // 是否有节假日折扣 1:有 0:无
  fest_discount_rate: number // 节假日折扣率
  adult_changeable_price?: string // 成人票可改价格
  adult_non_changeable_price: string // 成人票不可改价格
  child_changeable_price?: string // 儿童票可改价格
  child_non_changeable_price: string // 儿童票不可改价格
}

// 搜索车票结果
export interface TicketSearchResultItem {
  date: string // 出发日期
  from_id: string // 出发站id
  to_id: string // 到达站id
  route: { id: number, name: string }[]
  tickets: BusInfo[] // 车次信息
}

export interface TicketPriceInfo {
  activity_discount: number
  integrate: number
  integrate_discount: number
  order_amount: number
  reservations: number
  total_amount: number
  total_discount: number
}

// 车票订单用户信息
export interface TicketUerInfo {
  id: number
  email: string
  integrate: number // 积分
  phone: string // 手机号
  is_tourist_agency: number
  tourist_agency_level_id: number | null
}

export interface TicketTimetableItem {
  arrive_time?: string
  departure_time?: string
  is_meal?: number
  name?: string
  stay_time?: string
}

export interface TripItem extends BusInfo {
  from_name?: string
  to_name?: string
  steer_time?: string
  timetable?: TicketTimetableItem[]
}

// 车票预下单
export interface TicketPreOrderResult {
  activity?: any[]
  adult_count?: number // 成人票数量
  child_count?: number // 儿童票数量
  infant_count?: number // 婴儿票数量
  integrate_rate?: number // 积分抵扣率
  is_use_integrate?: boolean // 是否使用积分
  flexi_price?: string // 是否改签价格
  max_use_integrate?: number
  max_use_integrate_rate?: number
  price_info?: TicketPriceInfo
  trips?: TripItem[] // 车次信息
  user_info?: TicketUerInfo
}
