export interface ListItemType {
  icon: string
  label: string
}

export interface TabsType {
  label: string
  value: number
}

// 任务列表
export interface TaskListItemType {
  id: number
  coach_id: number
  departure_time: number
  from_station_id: number
  to_station_id: number
  route_id: number
  departure_date: string
  departure_time_txt: string
  from_station_name: string
  to_station_name: string
  coach_sn: string
  route_name: string
}

// 任务详情
export interface TaskDetailType {
  id: number
  reserve_departure_time: number
  is_boarding: number
  boarding_time: number
  ticket: {
    from_station_id: number
    to_station_id: number
    from_station_name: string
    to_station_name: string
    arrival_time: string
    station_distance: string
    travel_time: string
    departure_time: string
    passenger_count: number
    verify_passenger_count: number
    passengers: PassengersItemType[]
  }
  timetable: {
    is_departure_btn: number
    is_arrival_btn: number
    schedule_id: number
    id: number
    name: string
    arrival_time: string
    arrival_time: string
    true_arrival_time: string
    true_departure_time: string
    departure_time: string
    status: number
  }[]
}

// 乘客
export interface PassengersItemType {
  name: string
  phone: string
}
