import type { LoginParamsType, UserInfoType } from '@/model/login'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ApiEmailLogon, ApiPhoneLogin, ApiUserInfo } from '@/api/user'
import { getToken, removeToken, setToken } from '@/utils/token'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '')
  const userInfo = ref<UserInfoType | null>(null)

  // 存储token
  const setNewToken = (e: string) => {
    token.value = e
    setToken(token.value)
  }

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value)
      return
    const res = await ApiUserInfo()
    setToken(token.value)
    userInfo.value = res
  }

  // 手机号登陆
  const phoneLogin = async (info: LoginParamsType) => {
    const res = await ApiPhoneLogin(info)
    setNewToken(res.token)
    await getUserInfo()
  }

  // 邮箱登陆
  const emailLogin = async (info: LoginParamsType) => {
    const res = await ApiEmailLogon(info)
    setNewToken(res.token)
    await getUserInfo()
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = null
    removeToken()
    uni.reLaunch({
      url: '/subPackages/page-mine/login/login',
    })
  }

  return {
    token,
    userInfo,
    phoneLogin,
    emailLogin,
    getUserInfo,
    logout,
  }
})
