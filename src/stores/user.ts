import type { AppleLoginType, LoginParamsType, UserInfoType } from '@/model/login'
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { ApiAppleLogin, ApiEmailLogon, ApiGoogleLogin, ApiPhoneLogin, ApiUserInfo } from '@/api/user'
import { LoginUtils, LoginUtilsProvider } from '@/utils/login'
import { getToken, removeToken, setToken } from '@/utils/token'
import { useConfigStore } from './config'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '')
  const userInfo = ref<UserInfoType | null>(null)
  const { terminal } = useConfigStore()

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value)
      return
    const res = await ApiUserInfo()
    setToken(token.value)
    userInfo.value = res
  }

  watch(() => token.value, async (newVal: string) => {
    if (newVal) {
      setToken(token.value)
      await getUserInfo()
    }
    else {
      removeToken()
    }
  })

  // 手机号登陆
  const phoneLogin = async (info: LoginParamsType) => {
    const res = await ApiPhoneLogin(info)
    token.value = res.token ? res.token : ''
  }

  // 邮箱登陆
  const emailLogin = async (info: LoginParamsType) => {
    const res = await ApiEmailLogon(info)
    token.value = res.token ? res.token : ''
  }

  // 苹果登录
  const appleLogin = async (params: AppleLoginType) => {
    const res = await ApiAppleLogin(params)
    token.value = res.token ? res.token : ''
  }

  // 三方登录
  const appLogin = async (provider: LoginUtilsProvider) => {
    uni.showLoading({
      mask: true,
      title: 'Logging in',
    })
    if (provider === LoginUtilsProvider.Apple) {
      const appleLoginInfo = await LoginUtils.getAppleLoginInfo()
      console.warn('appleLoginInfo', appleLoginInfo)
      const res = await await ApiAppleLogin({
        terminal: terminal as number,
        identity_token: appleLoginInfo.identityToken,
      })
      console.warn('res', res)
      token.value = res.token ? res.token : ''
    }

    if (provider === LoginUtilsProvider.Google) {
      const googleLoginInfo = await LoginUtils.getGoogleLoginInfo()
      console.warn('googleLoginInfo', googleLoginInfo)
      const res = await await ApiGoogleLogin({
        terminal: terminal as number,
        nickname: googleLoginInfo.userInfo.nickName,
        openid: googleLoginInfo.authResult.openid,
      })
      console.warn('res', res)
      token.value = res.token ? res.token : ''
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = null
    removeToken()
    uni.reLaunch({
      url: '/subPackages/page-mine/login/login',
    })
  }

  return {
    token,
    userInfo,
    phoneLogin,
    emailLogin,
    getUserInfo,
    logout,
    appleLogin,
    appLogin,
  }
})
