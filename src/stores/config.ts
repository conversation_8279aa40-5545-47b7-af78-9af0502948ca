import type { AppConfigType, TerminalType } from '@/model/splash'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ApiConfig } from '@/api/splash/index'

export const useConfigStore = defineStore('config', () => {
  const config = ref<AppConfigType | null>(null) // app设置
  const systemInfo = ref<UniApp.GetSystemInfoResult | null>(null) // 系统信息
  const terminal = ref<TerminalType | null>(null) // 终端代码

  //  初始化设置信息
  const initConfig = async () => {
    const res = await ApiConfig({})
    config.value = res
  }

  //  初始化设备信息
  const initSystemInfo = async () => {
    systemInfo.value = uni.getSystemInfoSync()
    // #ifdef APP-PLUS
    terminal.value = systemInfo.value.platform === 'ios' ? 5 : 6
    // #endif
    // #ifdef H5
    terminal.value = 3
    // #endif
  }

  return {
    config,
    terminal,
    initConfig,
    initSystemInfo,
  }
})
