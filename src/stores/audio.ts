import type { AudioItemType } from '@/model/guide'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { formatAudioTime } from '@/utils/audio'

// 各种状态
export interface Status {
  isPlaying: boolean // 是否正在播放
  isSeeking: boolean // 是否正在拖动进度条
  isLoading: boolean // 是否加载中
  isPreview: boolean // 是否试听到期
  isRefresh: boolean // 是否刷新
}

// 播放信息
export interface PlayInfo {
  currentTime: number // 当前播放进度（秒）
  duration: number // 当前音频总时长（秒
  formatCurrentTime: string // 格式化后的当前时间
  formatDuration: string // 格式化后的总时长
  progressPercent: number // 进度百分比 0~1
  oldProgressPercent: number // 上次开始拖动的位置
  previewRatio: number // 试听时长总总时长的比例
}

export const useAudioStore = defineStore('audioStore', () => {
  const playlist = ref<AudioItemType[]>([]) // 播放列表
  const currentIndex = ref<number>(0) // 当前播放索引
  const currentAudio = ref<AudioItemType | null>(null) // 当前播放的音频项
  const audioManager = ref<UniApp.BackgroundAudioManager | null>(null) // 背景音频管理器
  const status = ref<Status>({
    isPlaying: false, // 是否正在播放jin
    isSeeking: false, // 是否正在拖动进度条
    isLoading: true, // 是否加载中
    isPreview: false, // 是否试听到期
    isRefresh: true, // 是否刷新初始化信息
  })
  //   播放信息
  const playInfo = ref<PlayInfo>({
    currentTime: 0,
    duration: 0,
    formatCurrentTime: '00:00',
    formatDuration: '00:00',
    progressPercent: 0,
    oldProgressPercent: 0,
    previewRatio: 0,
  })

  //   播放指定音频
  const playAudio = (param: AudioItemType | number) => {
    if (typeof param === 'number') {
      const index = param
      if (index < 0 || index >= playlist.value.length) {
        console.error(`索引 ${index} 超出播放列表范围`)
        return
      }
      currentIndex.value = index
      currentAudio.value = playlist.value[index]
    }
    else {
      const index = playlist.value.findIndex((item: AudioItemType) => item.id === param.id)
      if (index === -1) {
        console.error('音频不存在')
        return
      }
      currentIndex.value = index
      currentAudio.value = playlist.value[index]
    }
    if (audioManager.value) {
      audioManager.value.title = currentAudio.value?.name
      audioManager.value.src = currentAudio.value?.file_url
      audioManager.value.coverImgUrl = currentAudio.value?.image
      audioManager.value.play()
      playInfo.value.progressPercent = 0
      playInfo.value.oldProgressPercent = 0
    }
  }

  // 播放
  const resume = () => {
    audioManager.value?.play()
    status.value.isPlaying = true
  }

  // 暂停
  const pause = () => {
    audioManager.value?.pause()
    status.value.isPlaying = false
  }

  //   下一首
  const next = () => {
    if (playlist.value.length === 0)
      return
    status.value.isLoading = true
    status.value.isPreview = false
    currentIndex.value = (currentIndex.value + 1) % playlist.value.length
    const nextAduio = playlist.value[currentIndex.value]
    console.warn('[next] 调用', Date.now(), currentIndex.value)
    playAudio(nextAduio)
  }

  //   上一首
  const prev = () => {
    if (playlist.value.length === 0)
      return
    status.value.isLoading = true
    status.value.isPreview = false
    currentIndex.value = (currentIndex.value - 1 + playlist.value.length) % playlist.value.length
    const prevAudio = playlist.value[currentIndex.value]
    console.warn('[prev] 调用', Date.now(), currentIndex.value)
    playAudio(prevAudio)
  }

  // 获取播放列表
  const getAudioList = () => {
    playlist.value = [
      { id: 28, guide_id: 1, language: 'zh-cn', name: '中文', file_url: 'https://web-ext-storage.dcloud.net.cn/uni-app/ForElise.mp3', duction: '简介', duration: '10', trial_duration: 20, play_count: 0, create_time: '2025-09-03 10:23:18', update_time: '2025-09-03 10:23:18', delete_time: '', is_buy: 0, image: '' },
      { id: 29, guide_id: 1, language: 'zh-cn', name: '中文', file_url: 'https://web-ext-storage.dcloud.net.cn/uni-app/ForElise.mp3', duction: '简介', duration: '10', trial_duration: 20, play_count: 0, create_time: '2025-09-03 10:23:18', update_time: '2025-09-03 10:23:18', image: '', is_buy: 0, delete_time: '' },
      { id: 30, guide_id: 1, language: 'zh-cn', name: '中文', file_url: 'https://www.cambridgeenglish.org/images/153149-movers-sample-listening-test-vol2.mp3', duction: '简介', duration: '10', trial_duration: 15, play_count: 0, create_time: '2025-09-03 10:23:18', update_time: '2025-09-03 10:23:18', image: '', is_buy: 0, delete_time: '' },
    ]
  }

  //   进度拖动开始
  const onDragStart = () => {
    status.value.isSeeking = true
    playInfo.value.oldProgressPercent = playInfo.value.progressPercent
  }

  //   进度拖动结束
  const onDragEnd = (percent: number) => {
    // 拖动超过时间的话回到原位置并弹窗
    if (percent > playInfo.value.previewRatio) {
      playInfo.value.progressPercent = playInfo.value.oldProgressPercent
      pause()
      status.value.isPreview = true
    }
    else {
      const targetTime = playInfo.value.duration * (percent / 100)
      console.warn('targetTime', targetTime)
      audioManager.value?.seek(targetTime)
      status.value.isPreview = false
    }
    setTimeout(() => {
      status.value.isSeeking = false
      resume()
    }, 50)
  }

  // 初始化播放信息
  const initPlayInfo = () => {
    if (!status.value.isRefresh)
      return
    // 获取时长
    playInfo.value.duration = audioManager.value?.duration || 0
    playInfo.value.formatDuration = formatAudioTime(playInfo.value.duration)
    // 计算试听比例
    if (currentAudio.value?.is_buy === 1) {
      playInfo.value.previewRatio = 100
    }
    else {
      if (currentAudio.value?.trial_duration) {
        playInfo.value.previewRatio = (currentAudio.value.trial_duration / playInfo.value.duration) * 100
      }
      else {
        playInfo.value.previewRatio = 100
      }
    }
    status.value.isRefresh = true
  }

  //   初始化音频管理器
  const initAudioManager = () => {
    if (audioManager.value !== null)
      return
    audioManager.value = uni.getBackgroundAudioManager()
    console.warn('音频管理器初始化成功', audioManager.value)

    // 监听可播放事件
    audioManager.value.onCanplay(() => {
      // 关闭loading
      status.value.isLoading = false
      // 关闭试听到期
      status.value.isPreview = false
      // 确认要刷新播放数据
      status.value.isRefresh = true
    })
    // 监听播放进度
    audioManager.value.onTimeUpdate(() => {
      // 初始化音频信息
      initPlayInfo()
      // 更新时间
      playInfo.value.formatCurrentTime = formatAudioTime(audioManager.value?.currentTime || 0)
      playInfo.value.currentTime = audioManager.value?.currentTime || 0
      //   更新进度条
      if (!status.value.isSeeking && playInfo.value.duration > 0) {
        playInfo.value.progressPercent = ((audioManager.value?.currentTime || 0) / playInfo.value.duration) * 100
      }
      // 仅在播放状态下判断试听比例
      if (status.value.isPlaying && playInfo.value.progressPercent > playInfo.value.previewRatio) {
        pause()
        status.value.isPreview = true
      }
    })
    // 监听播放事件
    audioManager.value.onPlay(() => {
      console.warn('开始播放')
      status.value.isPlaying = true
      status.value.isLoading = false
    })
    // 监听暂停事件
    audioManager.value.onPause(() => {
      status.value.isPlaying = false
      console.warn('暂停')
    })
    // 监听播放结束事件
    audioManager.value.onEnded(() => {
      console.warn('播放结束了---------')
      next()
      // console.warn('播放结束了')
    })

    // 系统点击下一首
    audioManager.value.onNext(() => {
      console.warn('监听下一首')
      next()
    })

    // 系统点击上一首
    audioManager.value.onPrev(() => {
      console.warn('监听上一首')
      prev()
    })

    // 加载中
    audioManager.value.onWaiting(() => {
      status.value.isLoading = true
      console.warn('加载中----', status.value.isLoading)
    })

    // 报错
    audioManager.value.onError((error: any) => {
      console.warn('报错了', error)
      console.error(error)
    })
  }

  return {
    playlist,
    playInfo,
    currentIndex,
    currentAudio,
    audioManager,
    status,
    getAudioList,
    initAudioManager,
    playAudio,
    resume,
    pause,
    prev,
    next,
    onDragStart,
    onDragEnd,
  }
})
