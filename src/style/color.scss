/* 主色系 - Primary Colors */
.tc--primary {
    color: #103a62 !important;
  }
  .tc--primary-hover {
    color: #5d7d9d !important;
  }
  .tc--primary-light {
    color: #91a8be !important;
  }
  .tc--primary-lighter {
    color: #acbecf !important;
  }
  .tc--primary-lightest {
    color: #cfd6dd !important;
  }
  .tc--primary-pale {
    color: #edf1f6 !important;
  }
  /* 辅色 - Semantic Colors */
  .tc--accent {
    color: #111b19 !important;
  }
  .tc--text {
    color: #444444 !important;
  }
  .tc--text-light {
    color: #999999 !important;
  }
  .tc--border {
    color: #e6e6e6 !important;
  }
  /* 背景色系 - Background Colors */
  .tc--bg {
    color: #f5f5f5 !important;
  }
  .tc--bg-secondary {
    color: #f2fbfa !important;
  }
  .tc--bg-dark {
    color: #1a1a3d !important;
  }
  /* 功能色系 - Functional Colors */
  .tc--error {
    color: #982e37 !important;
  }
  .tc--success {
    color: #3bc1b1 !important;
  }
  .tc--info {
    color: #008dd0 !important;
  }
  .tc--warning {
    color: #ffc83e !important;
  }
  /* 基础色 - Base Colors */
  .tc--white {
    color: #ffffff !important;
  }
  .tc--drop-box {
    color: #ffffff !important;
  }
  
  /* 背景颜色工具类 - Background Color Utilities */
  /* 主色系 - Primary Colors */
  .bg--primary {
    background-color: #103a62 !important;
  }
  .bg--primary-hover {
    background-color: #5d7d9d !important;
  }
  .bg--primary-light {
    background-color: #91a8be !important;
  }
  .bg--primary-lighter {
    background-color: #acbecf !important;
  }
  .bg--primary-lightest {
    background-color: #cfd6dd !important;
  }
  .bg--primary-pale {
    background-color: #edf1f6 !important;
  }
  /* 辅色 - Semantic Colors */
  .bg--accent {
    background-color: #111b19 !important;
  }
  .bg--text {
    background-color: #444444 !important;
  }
  .bg--text-light {
    background-color: #999999 !important;
  }
  .bg--border {
    background-color: #e6e6e6 !important;
  }
  /* 背景色系 - Background Colors */
  .bg--bg {
    background-color: #f5f5f5 !important;
  }
  .bg--bg-secondary {
    background-color: #f2fbfa !important;
  }
  .bg--bg-dark {
    background-color: #1a1a3d !important;
  }
  /* 功能色系 - Functional Colors */
  .bg--error {
    background-color: #982e37 !important;
  }
  .bg--success {
    background-color: #3bc1b1 !important;
  }
  .bg--info {
    background-color: #008dd0 !important;
  }
  .bg--warning {
    background-color: #ffc83e !important;
  }
  /* 基础色 - Base Colors */
  .bg--white {
    background-color: #ffffff !important;
  }
  .bg--drop-box {
    background-color: #ffffff !important;
  }
  
  /* 边框颜色工具类 - Border Color Utilities */
  /* 主色系 - Primary Colors */
  .bc--primary {
    background-color: #103a62 !important;
  }
  .bc--primary-hover {
    background-color: #5d7d9d !important;
  }
  .bc--primary-light {
    background-color: #91a8be !important;
  }
  .bc--primary-lighter {
    background-color: #acbecf !important;
  }
  .bc--primary-lightest {
    background-color: #cfd6dd !important;
  }
  .bc--primary-pale {
    background-color: #edf1f6 !important;
  }
  /* 辅色 - Semantic Colors */
  .bc--accent {
    background-color: #111b19 !important;
  }
  .bc--text {
    background-color: #444444 !important;
  }
  .bc--text-light {
    background-color: #999999 !important;
  }
  .bc--border {
    background-color: #e6e6e6 !important;
  }
  /* 背景色系 - Background Colors */
  .bc--bg {
    background-color: #f5f5f5 !important;
  }
  .bc--bg-secondary {
    background-color: #f2fbfa !important;
  }
  .bc--bg-dark {
    background-color: #1a1a3d !important;
  }
  /* 功能色系 - Functional Colors */
  .bc--error {
    background-color: #982e37 !important;
  }
  .bc--success {
    background-color: #3bc1b1 !important;
  }
  .bc--info {
    background-color: #008dd0 !important;
  }
  .bc--warning {
    background-color: #ffc83e !important;
  }
  /* 基础色 - Base Colors */
  .bc--white {
    background-color: #ffffff !important;
  }
  .bc--drop-box {
    background-color: #ffffff !important;
  }
  