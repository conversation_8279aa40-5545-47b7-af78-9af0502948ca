@font-face {
    font-family: '<PERSON><PERSON><PERSON> FangYuanTi VF'; /* 字体别名，可自定义 */
    src: url('@/static/font/AlimamaFangYuanTiVF-Thin.ttf');
  }
  
  /* 字体样式 */
  .fs--8--400 {
    font-family: <PERSON><PERSON><PERSON> FangYuanTi VF !important;
    font-size: 16rpx !important;
    font-weight: 400 !important;
  }
  .fs--8--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 16rpx !important;
    font-weight: 700 !important;
  }
  .fs--10--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 20rpx !important;
    font-weight: 400 !important;
  }
  .fs--10--700 {
    font-family: <PERSON><PERSON><PERSON> FangYuanTi VF !important;
    font-size: 20rpx !important;
    font-weight: 700 !important;
  }
  .fs--12--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 24rpx !important;
    font-weight: 400 !important;
  }
  .fs--12--700 {
    font-family: <PERSON><PERSON><PERSON>i VF !important;
    font-size: 24rpx !important;
    font-weight: 700 !important;
  }
  .fs--14--400 {
    font-family: <PERSON><PERSON><PERSON>uanTi VF !important;
    font-size: 28rpx !important;
    font-weight: 400 !important;
  }
  .fs--14--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 28rpx !important;
    font-weight: 700 !important;
  }
  .fs--16--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 32rpx !important;
    font-weight: 400 !important;
  }
  .fs--16--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 32rpx !important;
    font-weight: 700 !important;
  }
  .fs--18--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 36rpx !important;
    font-weight: 400 !important;
  }
  .fs--18--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 36rpx !important;
    font-weight: 700 !important;
  }
  .fs--20--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 40rpx !important;
    font-weight: 400 !important;
  }
  .fs--20--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 40rpx !important;
    font-weight: 700 !important;
  }
  .fs--22--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 44rpx !important;
    font-weight: 400 !important;
  }
  .fs--22--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 44rpx !important;
    font-weight: 700 !important;
  }
  .fs--24--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 48rpx !important;
    font-weight: 400 !important;
  }
  .fs--24--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 48rpx !important;
    font-weight: 700 !important;
  }
  .fs--26--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 52rpx !important;
    font-weight: 400 !important;
  }
  .fs--26--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 52rpx !important;
    font-weight: 700 !important;
  }
  .fs--28--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 56rpx !important;
    font-weight: 400 !important;
  }
  .fs--28--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 56rpx !important;
    font-weight: 700 !important;
  }
  .fs--30--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 60rpx !important;
    font-weight: 400 !important;
  }
  .fs--30--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 60rpx !important;
    font-weight: 700 !important;
  }
  .fs--32--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 64rpx !important;
    font-weight: 400 !important;
  }
  .fs--32--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 64rpx !important;
    font-weight: 700 !important;
  }
  .fs--34--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 68rpx !important;
    font-weight: 400 !important;
  }
  .fs--34--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 68rpx !important;
    font-weight: 700 !important;
  }
  .fs--36--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 72rpx !important;
    font-weight: 400 !important;
  }
  .fs--36--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 72rpx !important;
    font-weight: 700 !important;
  }
  .fs--38--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 76rpx !important;
    font-weight: 400 !important;
  }
  .fs--38--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 76rpx !important;
    font-weight: 700 !important;
  }
  .fs--40--400 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 80rpx !important;
    font-weight: 400 !important;
  }
  .fs--40--700 {
    font-family: Alimama FangYuanTi VF !important;
    font-size: 80rpx !important;
    font-weight: 700 !important;
  }
  
  /* 字体系列 */
  .ff--Alimama {
    font-family: Alimama FangYuanTi VF !important;
  }
  
  /* 字体粗细 */
  .fw--400 {
    font-weight: 400 !important;
  }
  .fw--700 {
    font-weight: 700 !important;
  }
  
  /* 字体大小 */
  .fz--10{
      font-size: 20rpx !important;
  }
  .fz--12{
      font-size: 24rpx !important;
  }
  .fz--14{
      font-size: 28rpx !important;
  }
  .fz--16{
      font-size: 32rpx !important;
  }
  .fz--18{
      font-size: 36rpx !important;
  }
  .fz--20{
      font-size: 40rpx !important;
  }
  .fz--22{
      font-size: 44rpx !important;
  }
  .fz--24{
      font-size: 48rpx !important;
  }
  .fz--26{
      font-size: 52rpx !important;
  }
  .fz--28{
      font-size: 56rpx !important;
  }
  .fz--30{
      font-size: 60rpx !important;
  }
  .fz--32{
      font-size: 64rpx !important;
  }
  .fz--34{
      font-size: 68rpx !important;
  }
  .fz--36{
      font-size: 72rpx !important;
  }
  .fz--38{
      font-size: 76rpx !important;
  }
  .fz--40{
      font-size: 80rpx !important;
  }


$fontSMap: (
	'11': 22rpx,
	'12': 24rpx,
	'13': 26rpx,
	'14': 28rpx,
	'15': 30rpx,
	'16': 32rpx
);

$fontWMap: (
	'400': 400,
	'500': 500,
	'600': 600,
	'700': 700,
);

@each $key, $value in $fontSMap {
	.fs-#{$key} {
		font-size: $value;
	}
  .fs-#{$key} {
		font-size: $value;
	}
}

@each $key, $value in $fontWMap {
	.fw-#{$key} {
		font-weight: $value;
	}
}

@each $fontKey, $fontValue in $fontSMap {
	@each $fontWKey, $fontWValue in $fontWMap{
		.ff-#{$fontKey}-#{$fontWKey} {
			font-family: 'Alimama FangYuanTi VF';
			font-size: $fontValue;
			font-weight: $fontWValue;
		}
	}
}