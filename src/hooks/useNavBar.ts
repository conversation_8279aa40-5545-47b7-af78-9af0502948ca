import { computed, ref } from 'vue'
import { rpx2px } from '@/utils/pixel'

export function useNavBar(initialNavBarHeight: number = 88) {
  const systemInfo = uni.getSystemInfoSync()

  const statusBarHeight = ref<number>(systemInfo.statusBarHeight ?? rpx2px(88))
  const navBarHeight = ref<number>(rpx2px(initialNavBarHeight))
  const placeholderHeight = computed<number>(() => statusBarHeight.value + navBarHeight.value)

  return {
    statusBarHeight,
    navBarHeight,
    placeholderHeight,
  }
}
