import { computed, ref } from 'vue'
import { rpx2px } from '@/utils/pixel'

export function useTabbar(initialNavBarHeight: number = 88) {
  const systemInfo = uni.getSystemInfoSync()
  const bottomSafeArea = ref<number>(systemInfo.safeAreaInsets?.bottom || 0)
  const tabbarHeight = ref<number>(rpx2px(initialNavBarHeight))
  const placeholderHeight = computed<number>(() => bottomSafeArea.value + tabbarHeight.value)

  return {
    bottomSafeArea,
    tabbarHeight,
    placeholderHeight,
  }
}
