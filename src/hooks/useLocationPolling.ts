import { ref } from 'vue'

export function useLocationPolling() {
  const locationTimer = ref<number | null>(null)

  //   开始轮询
  function startLocationPolling(interval = 60000, callback: (lat: number, lng: number) => void) {
    stopLocationPolling()
    locationTimer.value = setInterval(() => {
      uni.getLocation({
        success(res) {
          const { latitude, longitude } = res
          console.warn('当前定位:', latitude, longitude)
          if (callback) {
            callback(latitude, longitude)
          }
        },
        fail(err) {
          console.error('获取定位失败:', err)
        },
      })
    }, interval) as unknown as number
  }

  //   停止轮询
  function stopLocationPolling() {
    if (locationTimer.value) {
      clearInterval(locationTimer.value)
      locationTimer.value = null
    }
  }

  return {
    startLocationPolling,
    stopLocationPolling,
  }
}
