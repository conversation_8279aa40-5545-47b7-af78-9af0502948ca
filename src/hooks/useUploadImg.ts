import type { UploadFileItem } from 'wot-design-uni/components/wd-upload/types'
import { useUpload } from 'wot-design-uni'
import { langMapping } from '@/locale'

export function useUploadImg() {
  const { startUpload, UPLOAD_STATUS } = useUpload()

  const upload = async ({
    tempFilePath,
    success,
    fail,
  }: {
    tempFilePath: string
    success?: (url: string) => void
    fail?: () => void
  }) => {
    const file: UploadFileItem = {
      url: tempFilePath,
      status: UPLOAD_STATUS.PENDING,
      percent: 0,
      uid: new Date().getTime(),
    }

    try {
      uni.showLoading({ title: '上传中...' })
      const localLang = uni.getStorageSync('lang') || 'en'
      const lang = langMapping[localLang] || ''
      // 开始上传
      await startUpload(file, {
        action: `${import.meta.env.VITE_API_URL}api/upload/image`,
        name: 'file',
        header: {
          lang,
        },
        async onSuccess(data: any) {
          const { uri } = JSON.parse(data.data).data
          await success?.(uri)
          uni.hideLoading()
        },
        onError() {
          uni.hideLoading()
          uni.showToast({
            title: '图片上传失败',
            icon: 'none',
          })
          fail?.()
        },
        onProgress(res: any) {
          console.warn('上传进度:', res.progress)
        },
      })
    }
    catch (error) {
      console.error('上传失败:', error)
    }
  }

  return {
    upload,
  }
}
