import type { PaginatedResponse } from '@/model/global'
import { reactive, ref, shallowRef } from 'vue'

export function useList<T>(
  fetcher: (params: any) => Promise<PaginatedResponse<T>>,
  initialParams: Record<string, any> = {},
) {
  const loading = ref(false) // 是否加载中
  const list = shallowRef<T[]>([]) // 数据列表
  const total = ref(0) // 总数
  const finished = ref(false) // 是否全部加载完成

  const queryParams = reactive({
    page_no: 1,
    page_size: 10,
    ...initialParams,
  })

  async function fetchList(isReset = false) {
    if (loading.value)
      return
    loading.value = true
    try {
      const res = await fetcher(queryParams)
      const data = res.lists ?? []
      const count = res.count ?? 0

      if (isReset) {
        list.value = data
      }
      else {
        list.value = [...list.value, ...data]
      }
      total.value = count

      // 判断是否加载完
      if (list.value.length >= count) {
        finished.value = true
      }
      else {
        finished.value = false
      }
    }
    finally {
      loading.value = false
    }
  }

  //   初始化 ｜｜ 刷新
  function refresh() {
    queryParams.page_no = 1
    finished.value = false
    return fetchList(true)
  }

  //   修改请求参数
  function setParams(params: Record<string, any>) {
    Object.assign(queryParams, params)
    queryParams.page_no = 1
    finished.value = false
    return fetchList(true)
  }

  //   分页加载
  async function loadMore() {
    if (loading.value || finished.value)
      return
    queryParams.page_no += 1
    await fetchList(false)
  }

  return {
    loading,
    list,
    total,
    finished,
    queryParams,
    fetchList,
    refresh,
    setParams,
    loadMore,
  }
}
