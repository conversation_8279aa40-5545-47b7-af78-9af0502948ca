import type { PaginatedResponse } from '@/model/global'
import type { MessageCard } from '@/model/home/<USER>'
import type { PackageItem } from '@/model/home/<USER>'
import type { HomePage } from '@/model/home/<USER>'
import type { RouteInfo, HotInfo as TicketRoute, TicketRouteDetail, TrasnferStationInfo } from '@/model/home/<USER>'
import type { TicketPreOrderResult, TicketSearchResultItem } from '@/model/home/<USER>'
import type { TransferBananer } from '@/model/home/<USER>'
import { http } from '@/utils/http'

// 获取首页
export const ApiGetHomeData = () => http.get<HomePage>('api/index/index')

// 选择路线列表
export const ApiGetRouteList = (params: any) => http.get<RouteInfo>('api/tickets/search', params)

// 获取选择路线列表
export const ApiGetTicketRouteList = () => http.get<TicketRoute[]>('api/tickets/routes')

// 获取路线详情
export const ApiGetTicketRouteDetail = (id?: number) => http.get<TicketRouteDetail>('api/tickets/routeDetail', { id })

// 查询车票列表
export const ApiGetSearchTicketList = (params: any) => http.post<TicketSearchResultItem[]>('api/tickets/searchTickets', params)

// 获取套票列表
export const ApiGetTicketPackageList = (params: any) => http.get <PaginatedResponse<PackageItem[]>>('api/Tickets/packageTickets', params)

// 获取消息列表
export const ApiGetMessageList = (params: any) => http.get <PaginatedResponse<MessageCard[]>>('api/user/noticeLIsts', params)

// 读取消息
export const ApiReadedMessage = (params: any) => http.get <PaginatedResponse<PackageItem[]>>('api/user/noticeRead', params)

// 获取接送服务轮播图
export const ApiGetTransferBananer = () => http.get <TransferBananer>('api/tickets/pickupServiceIndex')

// 获取接送服务站点列表
export const ApiGetTransferAddressList = () => http.get <TrasnferStationInfo[]>('/api/tickets/pickupServiceLists')

// 获取接送服务预下单
export const ApiGetTransferPreOrder = (params: any) => http.post('api/tickets/pickupServicePreOrder', params)

// 获取接送服务下单
export const ApiPayTransferOrder = (params: any) => http.post('api/tickets/pickupServiceOrder', params)

// 车票预下单
export const ApiGetTicketPreOrder = (params: any) => http.post <TicketPreOrderResult>('api/tickets/preOrder', params)
