import type { HomePage } from '@/model/home/<USER>'
import type { RouteInfo, HotInfo as TicketRoute, TicketRouteDetail } from '@/model/home/<USER>'
import { http } from '@/utils/http'

// 获取首页
export const ApiGetHomeData = () => http.get<HomePage>('api/index/index')

// 选择路线列表
export const ApiGetRouteList = (params: any) => http.get<RouteInfo>('api/tickets/search', params)

// 获取选择路线列表
export const ApiGetTicketRouteList = () => http.get<TicketRoute[]>('api/tickets/routes')

// 获取路线详情
export const ApiGetTicketRouteDetail = (id?: number) => http.get<TicketRouteDetail>('api/tickets/routeDetail', { id })
