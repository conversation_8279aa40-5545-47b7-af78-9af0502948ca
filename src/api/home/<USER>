import type { PaginatedResponse } from '@/model/global'
import type { PackageItem } from '@/model/home/<USER>'
import type { HomePage } from '@/model/home/<USER>'
import type { RouteInfo, HotInfo as TicketRoute, TicketRouteDetail } from '@/model/home/<USER>'
import { http } from '@/utils/http'

// 获取首页
export const ApiGetHomeData = () => http.get<HomePage>('api/index/index')

// 选择路线列表
export const ApiGetRouteList = (params: any) => http.get<RouteInfo>('api/tickets/search', params)

// 获取选择路线列表
export const ApiGetTicketRouteList = () => http.get<TicketRoute[]>('api/tickets/routes')

// 获取路线详情
export const ApiGetTicketRouteDetail = (id?: number) => http.get<TicketRouteDetail>('api/tickets/routeDetail', { id })

// 查询车票列表
export const ApiGetSearchTicketList = (params: any) => http.post<TicketRoute[]>('api/tickets/searchTickets', params)

// 获取套票列表
export const ApiGetTicketPackageList = (params: any) => http.get <PaginatedResponse<PackageItem[]>>('api/Tickets/packageTickets', params)
