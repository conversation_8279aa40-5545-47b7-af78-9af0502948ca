import type { PaginatedResponse } from '@/model/global'
import type { AudioItemType, ConfigPackType, GuideDetailType, GuideListItemType, GuideOrderItemType, GuidePlayDetailType, PriceInfoType, SaveOrderResultType } from '@/model/guide'
import { http } from '@/utils/http'

// 电子导游列表
export const ApiGuideList = (params: any) => http.get<PaginatedResponse<GuideListItemType>>('api/Eguide/list', params)
// 电子导游播放详情
export const ApiGuiPlaydetail = (params: any) => http.get<GuidePlayDetailType>('api/Eguide/AudioPlaybackDetail', params)
// 电子导游播放详情
export const ApiGuideDetail = (params: any) => http.get<GuideDetailType>('api/Eguide/AudioPlaybackList', params)
// 电子导游收藏
export const ApiGuideCollect = (params: any) => http.post('api/Eguide/AudioCollect', params)
// 预结算
export const ApisSettlementDetail = (params: any) => http.post<PriceInfoType>('api/order/settlementdetail', params)
// 音频列表
export const ApiAudioList = (params: any) => http.get<PaginatedResponse<AudioItemType>>('api/Eguide/AudioList', params)
// 下单
export const ApiSaveOrder = (params: any) => http.post<SaveOrderResultType>('api/order/saveorder', params)
// 获取打包购买价
export const ApiConfigPack = () => http.get<ConfigPackType>('/api/eguide/configpack', {})
// 电子导游订单
export const ApiGuideOrder = (params: any) => http.get<PaginatedResponse<GuideOrderItemType>>('api/userEguideOrder/lists', params)
