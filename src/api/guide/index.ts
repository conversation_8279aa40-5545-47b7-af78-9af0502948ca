import type { PaginatedResponse } from '@/model/global'
import type { GuideDetailType, GuideListItemType, GuidePlayDetailType } from '@/model/guide'
import { http } from '@/utils/http'

// 电子导游列表
export const ApiGuideList = (params: any) => http.get<PaginatedResponse<GuideListItemType>>('api/Eguide/list', params)
// 电子导游播放详情
export const ApiGuiPlaydetail = (params: any) => http.get<GuidePlayDetailType>('api/Eguide/AudioPlaybackDetail', params)
// 电子导游播放详情
export const ApiGuideDetail = (params: any) => http.get<GuideDetailType>('api/Eguide/AudioPlaybackList', params)
// 电子导游收藏
export const ApiGuideCollect = (params: any) => http.post('api/Eguide/AudioCollect', params)
// 预结算
export const ApisSettlementDetail = (params: any) => http.post('api/order/settlementdetail', params)
