import type { TaskDetailType, TaskListItemType } from '@/model/driver'
import type { PaginatedResponse } from '@/model/global'
import { http } from '@/utils/http'

// 任务列表
export const ApiTaskList = (params: any) => http.get<PaginatedResponse<TaskListItemType>>('api/tickets/taskLists', params)
// 任务详情
export const ApiTaskDetail = (params: any) => http.get<TaskDetailType>('api/tickets/taskDetail', params)
// 任务时刻
export const ApiTaskDates = (params: any) => http.get<number[]>('api/tickets/taskDates', params)
// 确认上车
export const ApiCheckInBoarding = (params: any) => http.post('api/tickets/checkInBoarding', params)
// 确认发车
export const ApiCheckInDeparture = (params: any) => http.post('api/tickets/checkInDeparture', params)
// 确认到达
export const ApiCheckInArrival = (params: any) => http.post('api/tickets/checkInArrival', params)
// 扫码验票
export const ApiVerify = (params: any) => http.post('api/tickets/verify', params)
// 上报位置
export const ApiCoachLocation = (params: any) => http.post('api/tickets/coachLocation', params)
