import type { BaseSwiperItemType, DistItemType, NoticeInfo, NoticeType } from '@/model/common'
import type { PaginatedResponse } from '@/model/global'
import type { AreaListType } from '@/model/mine'
import { http } from '@/utils/http'

export const ApiGetNoticeInfo = (type: NoticeType) => http.get<NoticeInfo>('/api/agreement/list', { type })
// 区号列表
export const ApiAreaList = (params: any) => http.get<PaginatedResponse<AreaListType>>('/api/common/getcountryregion', params)
// banner列表
export const ApiBannerList = (params: any) => http.get<PaginatedResponse<BaseSwiperItemType>>('api/banner/lists', params)
// 字典
export const ApiDictList = (params: any) => http.get('api/config/dict', params)
