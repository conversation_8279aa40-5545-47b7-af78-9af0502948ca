import type { BaseSwiperItemType, NoticeInfo, NoticeNameType, NoticeType, PayResult, PayStatusResult, PayTypeInfo, PayTypeParams, SendPayParamsType } from '@/model/common'
import type { PaginatedResponse } from '@/model/global'
import type { AreaListType } from '@/model/mine'
import { http } from '@/utils/http'

export const ApiGetNoticeInfo = (params: { type: NoticeType, name?: NoticeNameType }) => http.get<NoticeInfo>('/api/agreement/list', params)
// 区号列表
export const ApiAreaList = (params: any) => http.get<PaginatedResponse<AreaListType>>('/api/common/getcountryregion', params)
// banner列表
export const ApiBannerList = (params: any) => http.get<PaginatedResponse<BaseSwiperItemType>>('api/banner/lists', params)
// 字典
export const ApiDictList = (params: any) => http.get('api/config/dict', params)
// 获取支付方式
export const ApiGetPayType = (params: PayTypeParams) => http.get<PayTypeInfo>('api/pay/payWay', params)

// 发起支付
export const ApiSendPay = (params: SendPayParamsType) => http.post<PayResult>('api/pay/prepay', params)

// 发起支付
export const ApiGetPayStatus = (params: PayTypeParams) => http.get<PayStatusResult>('api/pay/payStatus', params)
