import type { PaginatedResponse } from '@/model/global'
import type { LoginResType, UserInfoType } from '@/model/login'
import type { ICouponListType, PointCenterType, PointDetailType, StoreListType, UserCenterType } from '@/model/mine'
import { http } from '@/utils/http'

// 发送手机验证码
export const ApiSendPhoneCode = (params: any) => http.post('api/sms/sendCode', params)
// 发送邮箱验证码
export const ApiSendEmailCode = (params: any) => http.post('api/email/sendCode', params)
// 手机号验证码登陆
export const ApiPhoneLogin = (params: any) => http.post<LoginResType>('api/login/account', params)
// 邮箱登陆
export const ApiEmailLogon = (params: any) => http.post<LoginResType>('api/login/account', params)
// 获取用户信息
export const ApiUserInfo = () => http.get<UserInfoType>('api/user/info')
// 发送邮箱验证码
export const ApiEditInfo = (params: any) => http.post('api/user/editInfo', params)
// 个人中心
export const ApiUserCenter = () => http.get<UserCenterType>('api/user/center')
// 积分中心
export const ApiPointCenter = () => http.get<PointCenterType>('api/account_log/index')
// 门店列表
// export const ApiStoreList = () => http.get<PaginatedResponse>('api/store/index')

/**
 * 门店列表
 */
export const ApiStoreList = (params: any) => http.get<PaginatedResponse<StoreListType>>('api/stores/lists', params)

/**
 * 优惠券列表
 */
export const ApiCouponList = (params: any) => http.get<PaginatedResponse<ICouponListType>>('api/stores/couponLists', params)

/**
 * 积分明细
 */
export const ApiPointDetail = (params: any) => http.get<PaginatedResponse<PointDetailType>>('api/account_log/lists', params)
