import type { PaginatedResponse } from '@/model/global'
import type { IOrderListType, OrderListParams } from '@/model/order'
import { http } from '@/utils/http'

/** 订单列表 */
export const ApiOrderList = (params: OrderListParams) => http.get<PaginatedResponse<IOrderListType>>('api/ticketsOrder/lists', params)

/** 车票订单详情 */
export const ApiOrderDetail = (params: { id: number }) => http.get<IOrderListType>('api/ticketsOrder/detail', params)

/** 一日游订单详情 */
export const ApiDayToursOrderDetail = (params: { id: number }) => http.get<IOrderListType>('api/ticketsOrder/dayToursOrderDetail', params)

/** 接送服务订单详情 */
export const ApiPickupServiceOrderDetail = (params: { id: number }) => http.get<IOrderListType>('api/ticketsOrder/pickupServiceOrderDetail', params)
