import type { PaginatedResponse } from '@/model/global'
import type { IOrderDetail, IOrderListType, OrderListParams } from '@/model/order'
import { http } from '@/utils/http'

/** 订单列表 */
export const ApiOrderList = (params: OrderListParams) => http.get<PaginatedResponse<IOrderListType>>('api/ticketsOrder/lists', params)

/** 车票订单详情 */
export const ApiOrderDetail = (params: { order_id: number }) => http.get<IOrderDetail>('api/ticketsOrder/detail', params)

/** 一日游订单详情 */
export const ApiDayToursOrderDetail = (params: { order_id: number }) => http.get<IOrderDetail>('api/ticketsOrder/dayToursOrderDetail', params)

/** 接送服务订单详情 */
export const ApiPickupServiceOrderDetail = (params: { order_id: number }) => http.get<IOrderDetail>('api/ticketsOrder/pickupServiceOrderDetail', params)

/** 取消订单 */
export const ApiCancelOrder = (params: { order_id: number }) => http.post('api/ticketOrder/refundTickets', params)
