/**
 * 将秒数格式化为 mm:ss 或 hh:mm:ss 格式
 * @param seconds 秒数
 */
export function formatAudioTime(seconds: number): string {
  if (Number.isNaN(seconds) || seconds < 0)
    return '00:00'

  const hrs = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  const pad = (n: number) => n.toString().padStart(2, '0')

  if (hrs > 0) {
    return `${pad(hrs)}:${pad(mins)}:${pad(secs)}`
  }
  else {
    return `${pad(mins)}:${pad(secs)}`
  }
}
