import type { CalendarItem, CurrentCalendar } from '@/model/utils'

const DateMap = {
  1: 'Mon',
  2: 'Tu<PERSON>',
  3: 'Wed',
  4: 'Thu',
  5: 'Fri',
  6: 'Sat',
  0: 'Sun',
}
export function getCurrentDate(): CurrentCalendar {
  const date = new Date()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const week = date.getDay()
  const dateMap: CurrentCalendar = {
    dateStr: `${month}/${day}`,
    dateText: DateMap[week],
    date,
  }
  return dateMap
}

export function generateDate(date: Date, days: number): CalendarItem {
  const future = new Date(date)
  future.setDate(date.getDate() + days)
  const year = future.getFullYear()
  const month = future.getMonth() + 1
  const day = future.getDate()
  const week = future.getDay()
  const timestamp = future.getTime()
  const seconds = timestamp / 1000
  const dateObj: CalendarItem = {
    dateFullStr: `${year}-${month}-${day}`,
    dateStr: `${month}/${day}`,
    dateText: DateMap[week],
    seconds,
    timestamp,
  }
  return dateObj
}

export function generateDateList(date: Date, len: number = 4) {
  const currentTime: CurrentCalendar = getCurrentDate()
  const dateList: CalendarItem[] = []
  for (let i = 0; i < len; i++) {
    const time: CalendarItem = generateDate(date, i)
    if (currentTime.dateStr === time.dateStr) {
      time.dateText = 'Today'
    }
    dateList.push(time)
  }
  return dateList
}
