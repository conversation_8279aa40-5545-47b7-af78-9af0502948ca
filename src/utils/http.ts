import { langMapping } from '@/locale'
import { getToken } from './token'

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

const baseURL = `${import.meta.env.VITE_API_URL}`

const httpInterceptor = {
  invoke(options: UniNamespace.RequestOptions) {
    if (!options.url.startsWith('http')) {
      options.url = baseURL + options.url
    }
    options.timeout = 10000
    options.header = {
      ...options.header,
    }
    const lang = uni.getStorageSync('lang') || 'en'
    options.header.lang = langMapping[lang] || ''
    const token = getToken()
    if (token) {
      options.header.token = token
    }
  },
}

uni.addInterceptor('request', httpInterceptor)
uni.addInterceptor('uploadFile', httpInterceptor)

interface ResponseData<T> {
  code: number
  msg: string
  data: T
}

function showErrorToast(msg: string) {
  uni.showToast({
    icon: 'none',
    title: msg,
  })
}

export function request<T>(url: string, method: RequestMethod, data?: any): Promise<T> {
  return new Promise((resolve, reject) => {
    uni.request({
      url,
      method,
      data,
      success: (res) => {
        const responseData = res.data as ResponseData<T>
        if (res.statusCode >= 200 && res.statusCode <= 300) {
          if (responseData?.code === 1) {
            resolve(responseData.data)
          }
          else {
            showErrorToast((responseData.msg || '请求错误'))
            reject(responseData)
          }
        }
        else if (res.statusCode === 401) {
          uni.navigateTo({ url: '/subPackages/page-mine/login/login' })
          reject(res)
        }
        else {
          uni.showToast({
            icon: 'none',
            title: (res.data as ResponseData<T>).msg || '请求错误',
          })
        }
      },
      fail: (err) => {
        uni.showToast({
          icon: 'none',
          title: '网络错误',
        })
        reject(err)
      },
    })
  })
}

export const http = {
  get: async<T>(url: string, data?: any) => request<T>(url, 'GET', data),
  post: <T>(url: string, data?: any) => request<T>(url, 'POST', data),
  put: <T>(url: string, data?: any) => request<T>(url, 'PUT', data),
  delete: <T>(url: string, data?: any) => request<T>(url, 'DELETE', data),
}
