// Apple 登录相关类型 👇

interface AppleLoginResult {
  authResult: {
    access_token: string
    openid: string
  }
  appleInfo: {
    authorizationCode: string
    fullName: Record<string, any>
    identityToken: string
    realUserStatus: number
    user: string
  }
  errMsg: string
}

interface AppleUserInfoResult {
  userInfo: {
    openId: string
    fullName: Record<string, any>
    authorizationCode: string
    identityToken: string
    realUserStatus: number
  }
  errMsg: string
}

interface AppleLoginCancel {
  errMsg: string
  code: 1000 | 1001
}

// Apple 登录相关类型 👆

// Google 登录相关类型 👇

interface GooleLoginResult {
  authResult: {
    openid: string
    unionid: string
  }
  errMsg: string
}

interface GoogleUserInfoResult {
  userInfo: {
    nickname: string
    unionid: string
    openid: string
    email: string
    openId: string
    nickName: string
  }
  errMsg: string
}

interface GoogleLoginCancel {
  errMsg: string
  code: -1002
}

// Google 登录相关类型 👆

// Facebook 登录相关类型（缺少，待补充） 👇

interface FacebookLoginResult {
  authResult: Record<string, any>
  errMsg: string
}

interface FacebookUserInfoResult {
  userInfo: Record<string, any>
  authResult: Record<string, any>
  errMsg: string
}

// facebook 登录取消或者被拒绝了也会得到该信息
interface FacebookLoginCancel {
  errMsg: string
  code: -2
}

// Facebook 登录相关类型（缺少，待补充） 👆

export enum LoginUtilsProvider {
  Apple = 'apple',
  Google = 'google',
  Facebook = 'facebook',
}

export class LoginUtils {
  /** 获取苹果登录信息 */
  static async getAppleLoginInfo() {
    const provider = 'apple'

    const loginResult = await uni.login({
      provider,
    })

    const userInfoResult = await uni.getUserInfo({
      provider,
    })

    const appleLoginResult: AppleLoginResult = loginResult as any
    const appleUserInfoResult: AppleUserInfoResult = userInfoResult as any

    return {
      openId: appleUserInfoResult.userInfo.openId,
      authorizationCode: appleLoginResult.appleInfo.authorizationCode,
      identityToken: appleLoginResult.appleInfo.identityToken,
      realUserStatus: appleLoginResult.appleInfo.realUserStatus,
      authResult: appleLoginResult.authResult,
      appleInfo: appleLoginResult.appleInfo,
      userInfo: appleUserInfoResult.userInfo,
      // 原始数据
      raw: {
        login: loginResult,
        userInfo: userInfoResult,
      },
    }
  }

  /** google 登录 */
  static async getGoogleLoginInfo() {
    const provider = 'google'

    const loginResult = await uni.login({
      provider: provider as UniNamespace.LoginOptions['provider'],
    })

    const userInfoResult = await uni.getUserInfo({
      provider: provider as UniNamespace.GetUserInfoOptions['provider'],
    })

    const googleLoginResult: GooleLoginResult = loginResult as any
    const googleUserInfoResult: GoogleUserInfoResult = userInfoResult as any

    return {
      openid: googleLoginResult.authResult.openid,
      email: googleUserInfoResult.userInfo.email,
      nickname: googleUserInfoResult.userInfo.nickname,
      authResult: googleLoginResult.authResult,
      userInfo: googleUserInfoResult.userInfo,
      raw: {
        login: loginResult,
        userInfo: userInfoResult,
      },
    }
  }

  /** 获取 Facebook 登录信息 */
  static async getFacebookLoginInfo() {
    const provider = 'facebook'

    const loginResult = await uni.login({
      provider: provider as UniNamespace.LoginOptions['provider'],
    })

    const userInfoResult = await uni.getUserInfo({
      provider: provider as UniNamespace.GetUserInfoOptions['provider'],
    })

    const FacebookLoginResult: FacebookLoginResult = loginResult as any
    const FacebookUserInfoResult: FacebookUserInfoResult = userInfoResult as any

    // TODO 需要获取数据一下
    return {
      authResult: FacebookLoginResult.authResult,
      userInfo: FacebookUserInfoResult.userInfo,
      raw: {
        login: loginResult,
        userInfo: userInfoResult,
      },
    }
  }

  static showErrorToast(option: { provider: LoginUtilsProvider, error: any, cancelMsg: string, defualtMsg: string }) {
    const { provider, error } = option
    uni.hideLoading()
    console.error(error)

    // 取消处理
    if (provider === LoginUtilsProvider.Apple && [1000, 1001].includes((error as AppleLoginCancel).code)) {
      uni.showToast({
        title: option.cancelMsg || (error as AppleLoginCancel).errMsg,
        icon: 'none',
      })
      return
    }
    else if (provider === LoginUtilsProvider.Google && (error as GoogleLoginCancel).code === -1002) {
      uni.showToast({
        title: option.cancelMsg || (error as GoogleLoginCancel).errMsg,
        icon: 'none',
      })
      return
    }
    else if (provider === LoginUtilsProvider.Facebook && (error as FacebookLoginCancel).code === -2) {
      uni.showToast({
        title: option.cancelMsg || (error as FacebookLoginCancel).errMsg,
        icon: 'none',
      })
      return
    }
    else if ((error as any).action === 'cancel') {
      // wot-design-ui 的 message-box 的取消
      return
    }

    // 错误处理
    if ((error as any).data && (error as any).data.msg) {
      uni.showToast({
        title: (error as any).data.msg,
        icon: 'none',
      })
    }
    else if ((error as any).errMsg) {
      uni.showToast({
        title: (error as any).errMsg,
        icon: 'none',
      })
    }
    else if ((error as Error).message) {
      uni.showToast({
        title: (error as Error).message,
        icon: 'none',
      })
    }
    else {
      uni.showToast({
        title: option.defualtMsg || '登录失败',
        icon: 'none',
      })
    }
  }
}
