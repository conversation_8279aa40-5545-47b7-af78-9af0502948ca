export function gotoPage(url: string) {
  if (url.startsWith('/subPackages/')) {
    uni.navigateTo({
      url,
    })
  }
  else if (url.startsWith('subPackages/')) {
    uni.navigateTo({
      url: `/${url}`,
    })
  }
  else {
    uni.navigateTo({
      url: `/pages/${url}`,
    })
  }
}

export function goBack(delta = 1) {
  const pages = getCurrentPages()

  if (pages.length > delta) {
    uni.navigateBack({
      delta,
    })
  }
  else {
    uni.switchTab({ url: '/pages/page-home/index' })
  }
}

/**
 * 获取当前页面路径
 * @returns string 当前页面路径，例如: 'pages/home/<USER>'
 */
export function getCurrentPagePath(): string {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage?.route || ''
}
