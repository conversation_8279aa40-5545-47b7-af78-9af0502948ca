<script lang="ts" setup>
import type { CalendarInstance } from 'wot-design-uni/components/wd-calendar/types'
import type { CalendarItem } from '@/model/utils'
import { onShow } from '@dcloudio/uni-app'
import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue'
import { AddressInfoEnum } from '@/enum/home'
import { generateDateList } from '@/utils/calendar'
import { getStore, setStore } from '@/utils/store'

const props = withDefaults(defineProps<{
  modelValue?: number | null
  placeholder?: string
  prop: string
  index?: number
}>(), {
  placeholder: 'Time',
})
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'update:modelValue', data: number): void
}>()

const date = computed({
  get() {
    return props.modelValue!
  },
  set(val: number) {
    emit('update:modelValue', val)
  },
})

const addressInfo = ref()
const calendarRef = ref<CalendarInstance | null>(null)
const dateValue = ref(0)
const activeIdx = ref(0)
const dateList = ref<CalendarItem[]>([])
const minDate = ref<number>(0)
const generateDate = ref(true)

watch(() => props.modelValue, (val) => {
  if (val && generateDate.value) {
    nextTick(() => {
      dateList.value = generateDateList(new Date(val), 4)
    })
  }
}, {
  immediate: true,
})

onShow(() => {
  addressInfo.value = getStore(AddressInfoEnum.ADDRESS_INFO)
})

onMounted(() => {
  const currentTime = new Date()
  minDate.value = Date.now()
  dateList.value = generateDateList(currentTime, 4)
})

watchEffect(() => {
  dateList.value = generateDateList(new Date(dateValue.value), 4)
})

function handleVisibleCalendar() {
  calendarRef.value?.open()
  // visible.value = true
}

function handleSelectDate(item: CalendarItem, idx: number) {
  generateDate.value = false
  date.value = item.timestamp
  activeIdx.value = idx
  if (props.index !== undefined) {
    addressInfo.value[props.index][props.prop] = item.timestamp
    setStore(AddressInfoEnum.ADDRESS_INFO, addressInfo.value)
    return
  }
  setStore(AddressInfoEnum.ADDRESS_INFO, {
    ...addressInfo.value,
    [props.prop]: item.timestamp,
  })
}

// function handleClose() {
//   emit('close')
// }

// function formatDate(date: Date): string {
//   return `${date.getMonth() + 1}/${date.getDate()}`
// }

function handleConfirm(time: { value: number, type: string }) {
  activeIdx.value = 0
  dateValue.value = time.value
  date.value = dateValue.value
  if (props.index !== undefined) {
    addressInfo.value[props.index][props.prop] = time.value
    setStore(AddressInfoEnum.ADDRESS_INFO, addressInfo.value)
    return
  }
  setStore(AddressInfoEnum.ADDRESS_INFO, {
    ...addressInfo.value,
    [props.prop]: time.value,
  })
}
</script>

<template>
  <view class="calendar-container">
    <view class="calendar-container__content" @click.stop="handleVisibleCalendar">
      <view
        v-for="(item, idx) in dateList"
        :key="item.dateStr"
        class="calendar-container__content__item mr"
        :class="{ 'calendar-container__content__item--active': activeIdx === idx }"
        @click.stop="handleSelectDate(item, idx)"
      >
        <text
          class="calendar-container__content__item__time"
          :class="{ 'calendar-container__content__item__time--active': activeIdx === idx }"
        >
          {{ item.dateStr }}
        </text>
        <text
          class="calendar-container__content__item__desc"
          :class="{ 'calendar-container__content__item__desc--active': activeIdx === idx }"
        >
          {{ item.dateText }}
        </text>
      </view>
      <view class="calendar-container__content__date">
        <image class="calendar-container__content__date__img" src="/static/icons/calendar.png" mode="aspectFill" />
      </view>
    </view>
    <wd-calendar ref="calendarRef" v-model="date" :min-date="minDate" :with-cell="false" @confirm="handleConfirm" />
  </view>
</template>

<style lang="scss" scoped>
.calendar-container {
    &__content{
        display: flex;
        flex-direction: row;
        align-items: center;
        // border: 1px solid red;
        &__item{
            display: flex;
             flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 140rpx;
            padding: 16rpx 30rpx;
            background-color: #fafafa;
            border-radius: 16rpx;
            &--active{
                border: 1px solid #103a62;
                background-color: #edf1f6;
            }
            &__time, &__desc{
                color: #000;
                &--active{
                    color: #103a62;
                }
            }
            &__time{
                font-size: 28rpx;
            }
            &__desc{
                font-size: 20rpx;
            }
        }

        &__date{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            width: 86rpx;
            height: 94rpx;
            border-radius: 16rpx;
            background-color: #edf1f6;
            &__img{
                width: 58rpx;
                height: 50rpx;
            }
        }
    }

}

.mr {
    margin-right: 10rpx;
}
</style>
