<script lang="ts" setup>
withDefaults(defineProps<{
  type?: string // ticket: 车票 default: 普通矩形
}>(), {
  type: 'ticket',
})
</script>

<template>
  <view>
    <view v-if="type === 'ticket'" class="ticket">
      <!-- 头部 -->
      <view class="ticket__info">
        <slot name="top" />
      </view>
      <!-- 中间样式 -->
      <view class="ticket__center">
        <view class="ticket__center__line" />
        <view class="ticket__center__circle left" />
        <view class="ticket__center__circle right" />
      </view>
      <!-- 底部 -->
      <view class="ticket__bottom">
        <slot name="bottom" />
      </view>
    </view>
    <view v-else>
      <view class="ticket__info default">
        <slot />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import './ticket-style-card.scss'
</style>
