<script setup lang="ts">
import { gotoPage } from '@/utils/router'

const props = withDefaults(defineProps<{
  text?: string
  list?: string[]
  path?: string
}>(), {
  text: 'View',
  list: () => [] as string[],
  path: '/subPackages/page-home/message-list/index',
})

function toPath() {
  gotoPage(props.path)
}
</script>

<template>
  <view class="tips">
    <view class="tips__left">
      <zui-svg-icon width="40rpx" height="40rpx" color="#103A62" icon="volume-down" />
    </view>

    <view class="tips__content">
      <wd-notice-bar
        background-color="#FAFAFA"
        :text="list"
        color="#103A62"
        direction="vertical"
        :speed="10"
        custom-class="tips__content__bar"
      />
    </view>

    <view class="tips__right" @click.stop="toPath">
      {{ text }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.tips{
    padding: 24rpx;
    border-radius: 24rpx;
    background-color: #FAFAFA;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__left{
        margin-right: 24rpx;
    }

    &__content{
        flex: 1;

        &__bar{
            line-height: 40rpx;
            padding: 0;
        }
    }

    &__right{
        width: 120rpx;
        text-align: right;
        color: #06AED4;
        font-size: 24rpx;
        font-weight: 500;
        text-decoration-line: underline;
    }
}
</style>
