<script lang="ts" setup>
withDefaults(defineProps<{
  isFull?: boolean
}>(), {
  isFull: false,
})
</script>

<template>
  <view class="page">
    <view class="page__content" :style="{ height: isFull ? '100vh' : 'auto' }">
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;

  &__content{
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 750rpx;
    min-width: 750rpx;
    max-width: 750rpx;
  }
}
</style>
