<script lang="ts" setup>
import type { TabbarItemType } from '@/model/common'
import { ref } from 'vue'
import { useTabbar } from '@/hooks/useTabbar'

const props = withDefaults(defineProps<{
  isPlaceholder?: boolean
  current?: number
}>(), {
  isPlaceholder: true,
  current: 0,
})

const { placeholderHeight, bottomSafeArea } = useTabbar(120)

const tabbarList = ref<TabbarItemType[]>([
  {
    activeIcon: '/static/tab-icon/home1.png',
    defaultIcon: '/static/tab-icon/home-light1.png',
    label: 'Home',
    path: '/pages/page-home/index',
  },
  {
    activeIcon: '/static/tab-icon/discovery1.png',
    defaultIcon: '/static/tab-icon/discovery-light1.png',
    label: 'E-Guide',
    path: '/pages/page-guide/index',
  },
  {
    activeIcon: '/static/tab-icon/location1.png',
    defaultIcon: '/static/tab-icon/location-light1.png',
    label: 'Attractions',
    path: '/pages/page-view/index',
  },
  {
    activeIcon: '/static/tab-icon/profile1.png',
    defaultIcon: '/static/tab-icon/profile-light1.png',
    label: 'Mine',
    path: '/pages/page-mine/index',
  },
])

function switchTab(i: number): void {
  if (props.current === i)
    return
  uni.switchTab({
    url: `${tabbarList.value[i].path}`,
  })
}
// 导航栏隐藏 👇
uni.hideTabBar()
</script>

<template>
  <view class="tabbar" :style="{ paddingBottom: `${bottomSafeArea}px`, height: `${placeholderHeight}px` }">
    <view
      v-for="(item, index) in tabbarList" :key="index" class="tabbar__item"
      :class="[current === index ? 'active' : '']" @tap.stop="switchTab(index)"
    >
      <image class="tabbar__item__icon" :src="current === index ? item.activeIcon : item.defaultIcon" />
      <text class="tabbar__item__text fs--12--400">
        {{ item.label }}
      </text>
    </view>
  </view>
  <view v-if="isPlaceholder" class="tabbar__placeholder" :style="{ paddingBottom: `${bottomSafeArea}px`, height: `${placeholderHeight}px` }" />
</template>

<style lang="scss" scoped>
@import "./tab-bar.scss";
</style>
