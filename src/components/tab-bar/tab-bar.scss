.tabbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 750rpx;
    flex-direction: row;
    padding: 0 24rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0 -6px 16px 0 rgba(0, 0, 0, 0.03);
    // height: calc(120rpx + constant(safe-area-inset-bottom));
    // height: calc(120rpx + env(safe-area-inset-bottom));
    // padding-bottom: constant(safe-area-inset-bottom);
    // padding-bottom: env(safe-area-inset-bottom);

    &__item {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &__icon {
            width: 42rpx;
            height: 42rpx;
        }

        &__text {
            color: #A4A7AE;
            line-height: 40rpx;
        }

        &.active {
            .tabbar__item__text {
                color: #103A62;
            }

        }
    }

    &__placeholder{
        width: 750rpx;
        // height: calc(125rpx + constant(safe-area-inset-bottom));
        // height: calc(125rpx + env(safe-area-inset-bottom));
        // padding-bottom: constant(safe-area-inset-bottom);
        // padding-bottom: env(safe-area-inset-bottom);
    }
}