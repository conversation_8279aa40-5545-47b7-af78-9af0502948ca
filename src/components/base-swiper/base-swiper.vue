<script setup lang="ts">
import type { BaseSwiperItemType, ImageMode } from '@/model/common'
import { ref } from 'vue'

withDefaults(defineProps<{
  list?: BaseSwiperItemType[] // 列表
  height?: string // 高度
  borderRadius?: string // 圆角
  mode?: ImageMode // 图片裁剪模式
  autoplay?: boolean // 是否自动播放
  interval?: number // 自动播放时长
  circular?: boolean // 是否循环播放
  indicatorDots?: boolean // 是否显示指示点
  activeColor?: string // 指示器颜色
  inactiveColor?: string // 未选中指示器颜色
}>(), {
  list: () => [] as BaseSwiperItemType[],
  height: '250rpx',
  borderRadius: '0',
  mode: 'aspectFill',
  autoplay: true,
  interval: 3000,
  circular: true,
  indicatorDots: true,
  activeColor: '#ffffff',
  inactiveColor: 'rgba(255,255,255, 0.5)',
})

const current = ref<number>(0)

function onChange(e: any) {
  current.value = e.detail.current || 0
}
</script>

<template>
  <view class="swiper-wrpper" :style="{ borderRadius }">
    <swiper
      :style="{ height }"
      :current="current"
      :autoplay="autoplay"
      :circular="circular"
      :interval="interval"
      :indicator-dots="indicatorDots"
      :indicator-color="inactiveColor"
      :indicator-active-color="activeColor"
      @change="onChange"
    >
      <swiper-item v-for="(item, index) in list" :key="index">
        <image :src="item.image" class="swiper-items-img" :mode="mode" />
      </swiper-item>
    </swiper>
  </view>
</template>

<style scoped lang="scss">
.swiper-wrpper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .swiper-items-img {
    width: 100%;
    height: 100%;
  }
}
</style>
