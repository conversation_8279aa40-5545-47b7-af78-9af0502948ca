	.player {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		position: fixed;
		left: 0;
        z-index: 1000;
		box-shadow: 0 -6px 16px 0 rgba(0, 0, 0, 0.04);
		border-bottom: 0.5px solid rgba(234, 236, 240, 1);
		padding: 24rpx;
		display: flex;
		align-items: center;
		flex-direction: row;
		justify-content: space-between;

		&_img {
			width: 100rpx;
			height: 100rpx;
			border-radius: 10rpx;
			margin-right: 20rpx;
		}

		&_info {
			flex: 1;

			&_text {
				color: #000000;
			}

			&_time {
				display: flex;
				align-content: center;
				flex-direction: row;
				margin-top: 16rpx;

				&_text {
					color: #717680;
				}
			}
		}

		&_btn {
			width: 48rpx;
			height: 48rpx;
		}

		&_placeholder {
			height: 150rpx;
		}
	}