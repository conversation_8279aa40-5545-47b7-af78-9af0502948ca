<script setup lang="ts">
// import type { AudioItem } from '@/stores/audio'
import { nextTick, onMounted, ref } from 'vue'
import { useTabbar } from '@/hooks/useTabbar'
import { useAudioStore } from '@/stores/audio'
import { gotoPage } from '@/utils/router'

const { placeholderHeight } = useTabbar(120)

const audioStore = useAudioStore()

const prevviewShow = ref<boolean>(false)

function toDetail() {
  gotoPage(`/subPackages/page-guide/guide-detail/guide-detail?id=${audioStore.currentAudio?.guide_id}`)
}

// 播放和暂停
let isToggling: boolean = false
function player() {
  // 正在切换中
  if (isToggling)
    return
  isToggling = true
  setTimeout(() => {
    if (audioStore.status.isPreview) {
      prevviewShow.value = true
    }
    else {
      audioStore.status.isPlaying ? audioStore.pause() : audioStore.resume()
    }
    isToggling = false
  }, 50)
}

// 自动计算占位高度
const bottomHeight = ref<number>(0)

onMounted(() => {
  // 等渲染完成后测量
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this) // 注意要指定 this 才会在当前组件内查找
    query.select('.player').boundingClientRect((rect) => {
      if (rect) {
        bottomHeight.value = (rect as UniApp.NodeInfo).height || 0
      }
    }).exec()
  })
})
</script>

<template>
  <view class="player" :style="{ bottom: `${placeholderHeight}px` }" @click="toDetail">
    <image class="player_img" :src="audioStore.currentAudio?.image" />
    <view class="player_info">
      <text class="player_info_text fs--14--700">
        {{ audioStore.currentAudio?.name }}
      </text>
      <view class="player_info_time">
        <text class="player_info_time_text fs--12--400">
          {{ audioStore.playInfo.formatCurrentTime }} / {{ audioStore.playInfo.formatDuration }}
        </text>
      </view>
    </view>
    <view class="player_btn">
      <zui-svg-icon
        width="48rpx"
        height="48rpx"
        color="#000"
        :icon="audioStore.status.isPlaying ? 'player-light' : 'play-light'"
        @click.stop="player"
      />
    </view>
    <confirm-popup
      v-model="prevviewShow"
      title="Audition end notification"
      content="Your trial has ended. Purchase this audio to listen to the full version."
      confirm-text="Purchase"
      cancel-text="Not yet"
      height="350rpx"
    />
  </view>
  <view class="player_placeholder" :style="{ height: `${bottomHeight}px` }" />
</template>

<style scoped lang="scss">
@import './audio-bar.scss'
</style>
