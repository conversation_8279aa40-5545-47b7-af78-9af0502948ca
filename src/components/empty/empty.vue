<script lang="ts" setup>
withDefaults(defineProps<{
  image?: string
  imageSize?: string
  description?: string
  height?: string
}>(), {
  image: '/static/common/empty.png',
  imageSize: '160rpx',
  description: '',
  height: '600rpx',
})
</script>

<template>
  <view class="empty" :style="{ height }">
    <wd-status-tip :image="image" :image-size="imageSize" :tip="description">
      <slot />
    </wd-status-tip>
  </view>
</template>

<style scoped lang="scss">
.empty{
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
