<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { computed, ref } from 'vue'
import { ApiSendEmailCode, ApiSendPhoneCode } from '@/api/user/index'

const props = withDefaults(defineProps<{
  style?: CSSProperties // 样式对象
  codeType?: 'phone' | 'email' // 验证码类型
  btnText?: string // 按钮文字
  time?: number // 每多少秒可发送给一次
  format?: string // 倒计时格式化方式
  account?: string // 手机或验证码
  scene?: 'YZMDL' | 'YXYZMDL' | 'BDSJHM' | 'BDYXYZM' // 场景值 验证码登陆或更换手机号
  rules?: string // 校验正则字符串
}>(), {
  style: () => ({
    height: '90rpx',
    backgroundColor: '#EDF1F6',
    color: '#000',
    width: '220rpx',
    fontSize: '28rpx',
    fontWeight: '400',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  }),
  codeType: 'phone',
  btnText: 'Send OTP',
  time: 60 * 1000,
  format: 'mm:ss',
  account: '',
  scene: 'YZMDL',
  rules: '',
})

const newRules = computed(() => props.rules ? new RegExp(props.rules) : null)

const isSend = ref<boolean>(false)

async function sendOtp() {
  // 非空校验
  if (!props.account) {
    return uni.showToast({
      icon: 'none',
      title: `Please enter the ${props.codeType === 'phone' ? 'phone number' : 'email'}`,
    })
  }
  //   正则校验
  if (newRules.value && !newRules.value?.test(props.account)) {
    return uni.showToast({
      icon: 'none',
      title: `Please enter the correct one ${props.codeType === 'phone' ? 'phone number' : 'email'}`,
    })
  }
  sendCode()
}

async function sendCode() {
  try {
    if (isSend.value)
      return
    props.codeType === 'phone'
      ? await ApiSendPhoneCode({
          scene_id: props.scene,
          mobile: props.account,
          area: 86,
        })
      : await ApiSendEmailCode({
          scene: props.scene,
          email: props.account,
        })
    isSend.value = true
    uni.showToast({ title: 'OTP sent successfully', icon: 'none' })
  }
  catch (error) {
    console.error(error)
  }
}

// 倒计时结束
function onFinish() {
  isSend.value = false
}
</script>

<template>
  <view :style="style" @click="sendOtp">
    <text v-if="!isSend">
      {{ props.btnText }}
    </text>
    <wd-count-down v-else auto-start :time="time" :format="format" @finish="onFinish" />
  </view>
</template>

<style scoped lang="scss"></style>
