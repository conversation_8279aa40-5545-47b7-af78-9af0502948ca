<script setup lang="ts">
import type { CSSProperties } from 'vue'

withDefaults(defineProps<{
  zIndex?: number // 层级
  title?: string
  height?: string
  isFooter?: boolean
  style?: CSSProperties
}>(), {
  zIndex: 1002,
  title: '',
  height: '300rpx',
  isFooter: false,
  style: () => ({}),
})

const emit = defineEmits(['close'])
const show = defineModel<boolean>('modelValue', { default: false })

function close() {
  show.value = false
  emit('close')
}
</script>

<template>
  <wd-popup
    v-model="show"
    position="bottom"
    :safe-area-inset-bottom="true"
    :z-index="zIndex"
    custom-style="border-radius: 40rpx 40rpx 0 0;"
    @close="close"
  >
    <view class="popup" :style="{ height, ...style }">
      <view class="popup__title">
        <text v-if="title" class="popup__title__text fs--16--700">
          {{ title }}
        </text>
        <view class="popup__title__close">
          <zui-svg-icon width="36rpx" height="36rpx" color="#000" icon="cancel-light" @tap="close" />
        </view>
      </view>
      <scroll-view class="popup__content" :srcoll-y="true" :show-scrollbar="false">
        <slot />
      </scroll-view>

      <view v-if="isFooter" class="popup__footer">
        <slot name="footer" />
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
@import './base-popup.scss'
</style>
