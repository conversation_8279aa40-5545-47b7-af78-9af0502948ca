<script setup lang="ts">
withDefaults(defineProps<{
  zIndex?: number // 层级
  title?: string // 标题
  height?: string // 弹窗高度
  content?: string // 内容
  confirmText?: string // 确认文字
  cancelText?: string // 取消文字
  confirmBtnColor?: 'blue' | 'red' // 确认按钮颜色 blue || red
}>(), {
  zIndex: 1002,
  title: '',
  height: '300rpx',
  content: '',
  confirmText: 'confirm',
  cancelText: 'cancel',
  confirmBtnColor: 'blue',
})
const emit = defineEmits(['close', 'return', 'confirm'])
const show = defineModel<boolean>('modelValue', { default: false })

function close() {
  show.value = false
  emit('close')
}

function back() {
  close()
  emit('return')
}

function submit() {
  emit('confirm')
}
</script>

<template>
  <base-popup v-model="show" is-footer :title="title" :height="height" :z-index="zIndex" @close="close">
    <view class="popup__wrapper">
      <text class="popup__wrapper_text fs--12--400">
        {{ content }}
      </text>
    </view>
    <template #footer>
      <view class="popup__wrapper__footer">
        <view class="popup__wrapper__footer__btn left" @tap.stop="back">
          <text class="fs--14--400 popup__wrapper__footer__btn__text">
            {{ cancelText }}
          </text>
        </view>
        <view class="popup__wrapper__footer__btn right" :class="confirmBtnColor" @tap.stop="submit">
          <text class="fs--14--400 popup__wrapper__footer__btn__text">
            {{ confirmText }}
          </text>
        </view>
      </view>
    </template>
  </base-popup>
</template>

<style scoped lang="scss">
@import './confirm-popup.scss'
</style>
