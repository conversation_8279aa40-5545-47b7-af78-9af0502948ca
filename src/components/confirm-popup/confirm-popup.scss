.popup__wrapper {
    padding: 32rpx 0;

    &_text {
        color: #103A62;
        line-height: 36rpx;
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;

        &__btn {
            height: 60rpx;
            width: 48%;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &.left {
                border: 1px solid #103A62;

                .popup__wrapper__footer__btn__text {
                    color: #103A62;
                }
            }

            &.right {
                border: 1px solid transparent;


                .popup__wrapper__footer__btn__text {
                    color: #fff;
                }

            }

            &.blue {
                background-color: #103A62;
            }

            &.red {
                background-color: #B13C3C;
            }
        }

    }
}