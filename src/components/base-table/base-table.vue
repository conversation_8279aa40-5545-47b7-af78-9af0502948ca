<script setup lang="ts">
import type { CSSProperties, ExtractPropTypes } from 'vue'
import { computed } from 'vue'
import wdTableCol from 'wot-design-uni/components/wd-table-col/wd-table-col.vue'
import wdTable from 'wot-design-uni/components/wd-table/wd-table.vue'

type BaseTableColoumn = ExtractPropTypes<typeof wdTableCol> & {
  slot?: string
}

type BaseTableProps = ExtractPropTypes<typeof wdTable>

const props = withDefaults(defineProps<{
  data?: any[]
  coloumns?: BaseTableColoumn[]
  tableProps?: BaseTableProps
  tableStyle?: CSSProperties
}>(), {
  coloumns: () => [
    {
      prop: 'name',
      label: 'Stopover station',
      width: '212rpx',
      align: 'center',
      slot: 'site',
    },
    {
      prop: 'school',
      label: 'Arrive',
      width: '156rpx',
      align: 'center',
    },
    {
      prop: 'major',
      label: 'Departure',
      width: '174rpx',
      align: 'center',
    },
    {
      prop: 'stay',
      label: 'Stay',
      width: '156rpx',
      align: 'center',
    },
  ],
  tableProps: () => ({}),
  tableStyle: () => ({ 'border-radius': '40rpx' }),
  data: () => [],
})

const defaultBaseTableProps = computed(() => {
  return {
    stripe: false,
    ...props.tableProps,
  }
})
</script>

<template>
  <view class="table-container">
    <wd-table :data="data" v-bind="defaultBaseTableProps" :style="tableStyle">
      <wd-table-col
        v-for="item in coloumns"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        v-bind="item"
      >
        <template v-if="item.slot" #value="{ row }">
          <view>
            <slot :name="item.slot" :row="row" />
          </view>
        </template>
      </wd-table-col>
    </wd-table>
    <empty v-if="!data.length" description="No data" height="400rpx" />
  </view>
</template>

<style lang="scss" scoped>
.table-container{

:deep(.wd-table__content--header){
    font-family: Alimama FangYuanTi VF;
    font-weight: 600;
    font-size: 24rpx;
    color: #103A62;
    .wd-table__cell{
        background: #EDF1F6;
    }
}
:deep(.wd-table__value.is-ellipsis){
    & >span{
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        align-items: center;
        word-break: break-word;
        text-align: center;
    }
}
:deep(.wd-table__body){
    .wd-table__cell{
        font-family: Alimama FangYuanTi VF;
        font-weight: 600;
        font-size: 24rpx;
        color: #5D7D9D;
    }
}
}
</style>
