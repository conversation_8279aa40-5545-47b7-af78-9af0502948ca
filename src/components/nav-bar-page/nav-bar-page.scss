.nav-bar {
		position: fixed;
		z-index: 999;
		top: 0;
		width: 750rpx;

		&__main {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-direction: row;
			padding: 0 32rpx;

			&__left {
				height: 100%;
				display: flex;
				align-items: center;
				flex-direction: row;


				&__btn {
					width: 60rpx;
					height: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					margin-right: 20rpx;
				}

				&__icon {
					width: 48rpx;
					height: 48rpx;
				}

				&__title {
					font-family: Alimama FangYuanTi VF !important;
					font-size: 32rpx !important;
					font-weight: 700 !important;
					max-width: 380rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			&__right {
				display: flex;
				align-items: center;
				flex-direction: row;
				height: 100%;
			}
		}
	}