<script lang="ts" setup>
import { useNavBar } from '@/hooks/useNavBar'

const props = withDefaults(defineProps<{
  navBarHeight?: number
  backgroundColor?: string
  title?: string
  isPlaceholder?: boolean
  isCustom?: boolean
}>(), {
  navBarHeight: 108,
  backgroundColor: '#FFFFFF',
  title: '',
  isPlaceholder: true,
  isCustom: false,
})

const { statusBarHeight, navBarHeight, placeholderHeight } = useNavBar(props.navBarHeight)

function back() {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
  })
}
</script>

<template>
  <view v-if="isPlaceholder" :style="{ height: `${placeholderHeight}px` }" />
  <view class="nav-bar" :style="{ backgroundColor }">
    <view :style="{ height: `${statusBarHeight}px` }" />
    <view class="nav-bar__main" :style="{ height: `${navBarHeight}px` }">
      <slot v-if="isCustom" />
      <template v-else>
        <view class="nav-bar__main__left">
          <view class="nav-bar__main__left__btn" @tap="back">
            <zui-svg-icon width="48rpx" height="48rpx" color="#000" icon="arrow-left2-light" />
          </view>
          <text class="nav-bar__main__left__title">
            {{ title }}
          </text>
        </view>
        <view class="nav-bar__main__right">
          <slot name="right" />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import './nav-bar-page.scss';
</style>
