<!-- eslint-disable style/no-tabs -->
<script lang="ts" setup>
import { useNavBar } from '@/hooks/useNavBar'
import { gotoPage } from '@/utils/router'

const props = withDefaults(defineProps<{
  num?: number
  height?: number
  path?: string
}>(), {
  num: 0,
  height: 128,
  path: '/subPackages/page-home/message-list/index',
})

const { statusBarHeight, navBarHeight, placeholderHeight } = useNavBar(props.height)

function toPath() {
  gotoPage(props.path)
}
</script>

<template>
  <view class="nav-bar">
    <view class="nav-bar__status" :style="{ height: `${statusBarHeight}px` }" />
    <view class="nav-bar__content" :style="{ height: `${navBarHeight}px` }">
      <view class="nav-bar__content__left">
        <image class="nav-bar__content__left__logo" src="@/static/home/<USER>" mode="aspectFill" />
        <slot />
      </view>
      <view class="nav-bar__content__message-box" @click.stop="toPath">
        <image class="nav-bar__content__message-box-icon" src="@/static/home/<USER>" mode="aspectFill" />
        <text v-if="num !== 0" class="nav-bar__content__message-box-num fs-12-700">
          {{ num }}
        </text>
      </view>
    </view>
  </view>
  <view class="nav-bar__placeholder" :style="{ height: `${placeholderHeight}px` }" />
</template>

<style lang="scss" scoped>
.nav-bar {
  width: 750rpx;
  background: #fff;
  position: fixed;
  // top: 40px;
  left: 0;
  z-index: 999;

  &__status{
    width: 100%;
    background-color: #fff;
  }

  &__content{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left:40rpx;
    padding-right: 40rpx;

    &__left{
      display: flex;
      align-items: center;
      flex-direction: row;
      flex: 1;

      &__logo {
        width: 226rpx;
        height: 76rpx;
      }
    }

    &__message-box {
      width: 96rpx;
      height: 96rpx;
      position: relative;

      &-icon {
        position: relative;
        width: 100%;
        height: 100%;
      }

      &-num {
        width: 36rpx;
        height: 36rpx;
        border-radius: 36rpx;
        text-align: center;
        color: white;
        position: absolute;
        top: 0;
        right: 0;
        background-color: #103a62;
        font-size: 24rpx;
        line-height: 36rpx;
      }
    }
  }

  &__placeholder{
    width: 100%;
  }
}
</style>
