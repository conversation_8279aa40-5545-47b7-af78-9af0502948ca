.wrapper {
    padding: 30rpx 0;

    .info {
        &__time {
            display: flex;
            align-items: center;
            flex-direction: row;

            &__text {
                color: #101010;
                line-height: 48rpx;
            }

            &__tag {
                color: #fff;
                padding: 8rpx 12rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: row;
                background-color: #17B26A;
                border-radius: 100rpx;
                margin-left: 18rpx;
            }
        }

        &__stop {
            margin: 32rpx 0;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;

            &__info {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                &__text {
                    color: #101010;
                    line-height: 48rpx;
                }

                &__time {
                    color: #878787;
                    line-height: 32rpx;
                }
            }

            &__center {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                &__num {
                    color: #000;
                    line-height: 32rpx;
                }

                &__img {
                    width: 150rpx;
                    height: 32rpx;
                }

                &__km {
                    line-height: 32rpx;
                    color: #103A62;
                }
            }
        }

        &__classes {
            display: flex;
            align-items: center;
            flex-direction: row;

            &__label {
                color: #103A62;
                line-height: 32rpx;
            }

            &__value {
                color: #252B37;
                line-height: 32rpx;
            }
        }

        &__tips {
            color: #F97066;
            line-height: 36rpx;
            margin-top: 32rpx;
        }
    }

    .bottom{

        &__title{
            color: #101010;
            line-height: 48rpx;
            text-align: center;
        }

        &__content{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;
            margin: 32rpx 0;
        }

        &__code{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;

            &__container{
                background-color: #CFD6DD;
                padding: 8rpx 20rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: row;
                border-radius: 50rpx;

                &__text{
                    color: #000;
                }
            }
        }
    }

}