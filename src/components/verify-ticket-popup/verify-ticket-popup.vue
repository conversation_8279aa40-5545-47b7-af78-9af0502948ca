<script lang="ts" setup>
import ikunQrcode from '@/uni_modules/ikun-qrcode/components/ikun-qrcode/ikun-qrcode.vue'

const show = defineModel<boolean>('modelValue', { default: false })
</script>

<template>
  <view>
    <!-- 验票 -->
    <base-popup v-model="show" title="Scan to check ticket" height="1100rpx">
      <view class="wrapper">
        <ticket-style-card>
          <template #top>
            <view class="info">
              <view class="info__time">
                <text class="info__time__text fs--12--400">
                  2025/07/04
                </text>
                <text class="info__time__tag fs--10--400">
                  To be used
                </text>
              </view>
              <view class="info__stop">
                <view class="info__stop__info">
                  <text class="info__stop__info__text fs--16--700">
                    Shenzhen N.
                  </text>
                  <text class="info__stop__info__time fs--12--400">
                    10:00 AM
                  </text>
                </view>
                <view class="info__stop__center">
                  <text class="info__stop__center__num fs--14--400">
                    D3534
                  </text>
                  <image class="info__stop__center__img" src="/static/mine/location_right.png" />
                  <text class="info__stop__center__km fs--12--400">
                    1h45m
                  </text>
                </view>
                <view class="info__stop__info">
                  <text class="info__stop__info__text fs--16--700">
                    Shenzhen N.
                  </text>
                  <text class="info__stop__info__time fs--12--400">
                    10:00 AM
                  </text>
                </view>
              </view>
              <view class="info__classes">
                <text class="info__classes__label fs--12--400">
                  D3534:
                </text>
                <text class="info__classes__value fs--12--400">
                  East route ZQN-DUN-CHC
                </text>
              </view>
              <text class="info__tips fs--12--700">
                Please show this QR code to the staff when taking the vehicle
              </text>
            </view>
          </template>
          <template #bottom>
            <view class="bottom">
              <text class="bottom__title fs--12--400">
                Ride code
              </text>
              <view class="bottom__content">
                <ikun-qrcode
                  width="300"
                  height="300"
                  unit="rpx"
                  color="#000000"
                  data="守护最好的哥哥"
                />
              </view>
              <view class="bottom__code">
                <view class="bottom__code__container">
                  <text class="bottom__code__container__text fs--12--400">
                    D222 6885 1564 1643
                  </text>
                </view>
              </view>
            </view>
          </template>
        </ticket-style-card>
      </view>
    </base-popup>
  </view>
</template>

<style scoped lang="scss">
@import './verify-ticket-popup.scss'
</style>
