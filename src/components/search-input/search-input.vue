<script setup lang="ts">
withDefaults(defineProps<{
  disabled?: boolean
  placeholder?: string
  text?: string // 右侧文字
  textClass?: string // 右侧文字类名
}>(), {
  disabled: false,
  placeholder: '',
  text: '',
  textClass: 'default', // 右侧文字类名
})

const emits = defineEmits(['search', 'action-click', 'clear'])

const keyword = defineModel<string>('modelValue', { default: '' })

function search() {
  emits('search', keyword.value)
}

function clearKeyword() {
  emits('clear')
}

function cancel() {
  emits('action-click', keyword.value)
}
</script>

<template>
  <wd-search
    v-model="keyword"
    placeholder-left
    :disabled="disabled"
    :placeholder="placeholder"
    placeholder-class="search__placeholder"
    custom-class="search"
    @search="search"
    @clear="clearKeyword"
  >
    <template #prefix>
      <view class="search__icon">
        <zui-svg-icon width="32rpx" height="32rpx" color="#ababab" icon="search-light" />
      </view>
    </template>
    <template #suffix>
      <view class="search__text" :class="textClass" :style="{ marginLeft: `${text ? '20rpx' : '0'}` }" @tap.stop="cancel">
        {{ text }}
      </view>
    </template>
  </wd-search>
</template>

<style scoped lang="scss">
.search{
    padding: 0;

    &__text{
        font-size: 26rpx;
        font-weight: 400;
        // margin-left: 20rpx;

        &.default{
            color: #161616;
        }

        &.blue{
            text-decoration-line: underline;
            color: #103A62;
            font-weight: 600;
        }
    }

    &__icon{
        padding-right: 20rpx;
        padding-left: 30rpx;
        padding-top: 5rpx;
    }

    :deep(.wd-search__block){
        height: 80rpx;
        border-radius: 100rpx;
        background-color: #F7F7F7;

        .wd-search__input{
            padding-left: 0rpx;
        }

        .wd-search__search-left-icon{
            display: none;
        }
    }

    &__placeholder{
        font-size: 28rpx;
        font-weight: 400;
    }
}
</style>
