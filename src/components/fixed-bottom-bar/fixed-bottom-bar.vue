<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { useTabbar } from '@/hooks/useTabbar'

const { bottomSafeArea } = useTabbar()

// 记录 bottom-bar 的高度
const bottomBarHeight = ref(0)

onMounted(() => {
  // 等渲染完成后测量
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this) // 注意要指定 this 才会在当前组件内查找
    query.select('.bottom-bar').boundingClientRect((rect) => {
      if (rect) {
        bottomBarHeight.value = (rect as UniApp.NodeInfo).height || 0
      }
    }).exec()
  })
})
</script>

<template>
  <view class="bottom-bar" :style="{ paddingBottom: `${bottomSafeArea + 10}px` }">
    <slot />
  </view>
  <!-- 占位元素 -->
  <view class="bottom-bar__placeholder" :style="{ height: `${bottomBarHeight}px` }" />
</template>

<style scoped lang="scss">
.bottom-bar{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 750rpx;
    flex-direction: row;
    // padding: 0 32rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    // padding-top: 10rpx;
    background-color: #fff;
    box-shadow: 0 -6px 16px 0 rgba(0, 0, 0, 0.03);

    &__placeholder{
        width: 750rpx;
    }
}
</style>
