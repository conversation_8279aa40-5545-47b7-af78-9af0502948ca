<script lang="ts" setup>
import type { CSSProperties } from 'vue'

withDefaults(defineProps<{ dstyle?: CSSProperties, discount?: number }>(), {
  dstyle: () => {
    return {
      'position': 'absolute',
      'top': '0',
      'right': '0',
      'border-radius': '0 24rpx 0 24rpx',
    }
  },
  discount: 10,
})
</script>

<template>
  <view class="discount-container" :style="dstyle">
    <text class="discount-container__text">
      {{ discount }}% off
    </text>
  </view>
</template>

<style lang="scss" scoped>
.discount-container{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #EDF1F6;
  border-radius: 10rpx;
  &__text{
    font-family: Alimama FangYuanTi VF;
    font-weight: 400;
    font-size: 24rpx;
    color: #103A62;
  }
}
</style>
