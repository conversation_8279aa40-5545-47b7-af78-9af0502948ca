{"files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "prettier.enable": false, "editor.formatOnSave": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "nvue", "uvue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "svelte", "css", "less", "scss", "pcss", "postcss"], "i18n-ally.localesPaths": ["src/locale"]}