{"files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "prettier.enable": false, "editor.formatOnSave": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "nvue", "uvue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "svelte", "css", "less", "scss", "pcss", "postcss"], "i18n-ally.localesPaths": ["src/locales"], "i18n-ally.keystyle": "nested", "i18n-ally.pathMatcher": "{locale}.json", "i18n-ally.namespace": false, "i18n-ally.enabledParsers": ["json"], "i18n-ally.sourceLanguage": "zh-Hans", "i18n-ally.displayLanguage": "zh-Hans", "i18n-ally.enabledFrameworks": ["vue", "vue-sfc"], "i18n-ally.extract.keygenStyle": "camelCase"}