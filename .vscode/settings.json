{"files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "prettier.enable": false, "editor.formatOnSave": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "nvue", "uvue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "svelte", "css", "less", "scss", "pcss", "postcss"], "i18n-ally.localesPaths": ["src/locale/modules"], "i18n-ally.keystyle": "nested", "i18n-ally.pathMatcher": "{locale}/modules/*.ts", "i18n-ally.namespace": true, "i18n-ally.enabledParsers": ["ts"], "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "en", "i18n-ally.enabledFrameworks": ["vue", "vue-sfc"], "i18n-ally.extract.keygenStyle": "camelCase", "i18n-ally.sortKeys": true, "i18n-ally.keepFulfilled": true, "i18n-ally.dirStructure": "dir", "i18n-ally.extract.autoDetect": true, "i18n-ally.extract.keyMaxLength": 50, "i18n-ally.usage.scanningIgnore": ["node_modules/**", "dist/**", ".git/**"], "i18n-ally.review.enabled": true, "i18n-ally.review.gutters": true}